{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/app/page.tsx"], "sourcesContent": ["import { redirect } from 'next/navigation';\r\n\r\nexport default function RootPage() {\r\n  // Redirect root to unified customer home\r\n  redirect('/home');\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEe,SAAS;IACtB,yCAAyC;IACzC,CAAA,GAAA,oMAAA,CAAA,WAAQ,AAAD,EAAE;AACX", "debugId": null}}]}