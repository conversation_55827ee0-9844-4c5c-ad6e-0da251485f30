const CHUNK_PUBLIC_PATH = "server/app/page.js";
const runtime = require("../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/3c0f5_next_dist_4078febf._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__fb250061._.js");
runtime.loadChunk("server/chunks/ssr/apps_web_src_app_3a9022d1._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__8da584cb._.js");
runtime.loadChunk("server/chunks/ssr/_1e04a43f._.js");
runtime.loadChunk("server/chunks/ssr/3c0f5_next_dist_client_components_forbidden-error_f5612234.js");
runtime.loadChunk("server/chunks/ssr/3c0f5_next_dist_client_components_unauthorized-error_ad2297e1.js");
runtime.loadChunk("server/chunks/ssr/3c0f5_next_dist_a105feab._.js");
runtime.loadChunk("server/chunks/ssr/apps_web_85b883e4._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/web/.next-internal/server/app/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/apps/web/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { METADATA_0 => \"[project]/apps/web/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/apps/web/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/apps/web/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/apps/web/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/apps/web/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/apps/web/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/apps/web/src/app/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/apps/web/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { METADATA_0 => \"[project]/apps/web/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/apps/web/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/apps/web/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/apps/web/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/apps/web/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/apps/web/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/apps/web/src/app/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
