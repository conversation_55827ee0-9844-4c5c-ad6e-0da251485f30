{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/home/<USER>"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport {\r\n  HomeIcon,\r\n  BuildingStorefrontIcon,\r\n  XMarkIcon,\r\n  UserIcon,\r\n  ShoppingBagIcon,\r\n  HeartIcon,\r\n  MapPinIcon,\r\n  CreditCardIcon,\r\n  BellIcon,\r\n  Cog6ToothIcon,\r\n  GiftIcon,\r\n  StarIcon,\r\n  ClockIcon,\r\n  DocumentTextIcon,\r\n  QuestionMarkCircleIcon,\r\n  ChatBubbleLeftRightIcon,\r\n} from '@heroicons/react/24/outline';\r\n\r\n// Type utility for React 19 compatibility with Heroicons\r\ntype IconComponent = any;\r\n\r\ninterface NavigationItem {\r\n  name: string;\r\n  href: string;\r\n  icon: IconComponent;\r\n}\r\n\r\ninterface NavigationCategory {\r\n  name: string;\r\n  icon: IconComponent;\r\n  items: NavigationItem[];\r\n}\r\n\r\nconst navigationCategories: NavigationCategory[] = [\r\n  {\r\n    name: 'Browse',\r\n    icon: HomeIcon,\r\n    items: [\r\n      { name: 'Home', href: '/home', icon: HomeIcon },\r\n      { name: 'Stores', href: '/restaurants', icon: BuildingStorefrontIcon },\r\n    ]\r\n  },\r\n  {\r\n    name: 'Account',\r\n    icon: HomeIcon,\r\n    items: [\r\n      { name: 'Dashboard', href: '/account/dashboard', icon: HomeIcon },\r\n      { name: 'Profile', href: '/account/profile', icon: UserIcon },\r\n    ]\r\n  },\r\n  {\r\n    name: 'Orders & Favorites',\r\n    icon: ShoppingBagIcon,\r\n    items: [\r\n      { name: 'Order History', href: '/account/orders', icon: ShoppingBagIcon },\r\n      { name: 'Track Order', href: '/account/orders/track', icon: ClockIcon },\r\n      { name: 'Favorites', href: '/account/favorites', icon: HeartIcon },\r\n      { name: 'Reviews', href: '/account/reviews', icon: StarIcon },\r\n    ]\r\n  },\r\n  {\r\n    name: 'Delivery & Payment',\r\n    icon: MapPinIcon,\r\n    items: [\r\n      { name: 'Addresses', href: '/account/addresses', icon: MapPinIcon },\r\n      { name: 'Payment Methods', href: '/account/payment', icon: CreditCardIcon },\r\n    ]\r\n  },\r\n  {\r\n    name: 'Rewards & Offers',\r\n    icon: GiftIcon,\r\n    items: [\r\n      { name: 'Loyalty Points', href: '/account/loyalty', icon: GiftIcon },\r\n      { name: 'Promotions', href: '/account/promotions', icon: DocumentTextIcon },\r\n      { name: 'Referrals', href: '/account/referrals', icon: UserIcon },\r\n    ]\r\n  },\r\n  {\r\n    name: 'Support & Settings',\r\n    icon: Cog6ToothIcon,\r\n    items: [\r\n      { name: 'Help Center', href: '/account/help', icon: QuestionMarkCircleIcon },\r\n      { name: 'Contact Support', href: '/account/support', icon: ChatBubbleLeftRightIcon },\r\n      { name: 'Notifications', href: '/account/notifications', icon: BellIcon },\r\n      { name: 'Settings', href: '/account/settings', icon: Cog6ToothIcon },\r\n    ]\r\n  }\r\n];\r\n\r\ninterface HomeSidebarProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  isCollapsed?: boolean;\r\n  onExpandAndNavigate?: (href: string, categoryName: string) => void;\r\n}\r\n\r\nexport default function HomeSidebar({\r\n  isOpen,\r\n  onClose,\r\n  isCollapsed = false,\r\n  onExpandAndNavigate\r\n}: HomeSidebarProps) {\r\n  const pathname = usePathname();\r\n\r\n  const isItemActive = (href: string) => {\r\n    return pathname === href || (href === '/home' && pathname === '/');\r\n  };\r\n\r\n  // Handle navigation link clicks - only close sidebar on mobile\r\n  const handleNavClick = () => {\r\n    // Only close sidebar on mobile (when screen is small)\r\n    if (window.innerWidth < 1024) { // lg breakpoint\r\n      onClose();\r\n    }\r\n  };\r\n\r\n  const handleCollapsedCategoryClick = (category: NavigationCategory) => {\r\n    if (onExpandAndNavigate && category.items.length > 0) {\r\n      // Get the first item in the category\r\n      const firstItem = category.items[0];\r\n      // Expand sidebar and navigate to first item\r\n      onExpandAndNavigate(firstItem.href, category.name);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Mobile overlay */}\r\n      {isOpen && (\r\n        <div\r\n          className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\r\n          onClick={onClose}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <div className={`fixed bottom-0 left-0 z-50 bg-gray-50 shadow-lg transform transition-all duration-300 ease-in-out lg:translate-x-0 ${\r\n        isOpen ? 'translate-x-0' : '-translate-x-full'\r\n      } ${isCollapsed ? 'w-16' : 'w-64'}`} style={{ top: '67px' }}>\r\n        {/* Mobile close button */}\r\n        <div className=\"lg:hidden flex justify-end p-2 border-b border-gray-200 bg-gray-50\">\r\n          <button\r\n            onClick={onClose}\r\n            className=\"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100\"\r\n          >\r\n            <XMarkIcon className=\"h-6 w-6\" />\r\n          </button>\r\n        </div>\r\n\r\n        {/* Scrollable Navigation Container */}\r\n        <div className=\"h-full overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400\">\r\n          {/* Navigation */}\r\n          <nav className={`py-3 ${isCollapsed ? 'px-2' : 'px-4'}`}>\r\n            <div className=\"space-y-1\">\r\n              {navigationCategories.map((category) => {\r\n\r\n                if (isCollapsed) {\r\n                  // Collapsed view - show only category icons (interactive)\r\n                  return (\r\n                    <div key={category.name} className=\"relative group\">\r\n                      <button\r\n                        onClick={() => handleCollapsedCategoryClick(category)}\r\n                        className=\"w-full flex items-center justify-center p-3 rounded-md transition-all duration-200 hover:scale-105 text-gray-600 hover:bg-orange-50 hover:text-orange-600 hover:shadow-sm\"\r\n                        title={`${category.name} - Click to expand and view ${category.items[0]?.name || 'items'}`}\r\n                      >\r\n                        <category.icon className=\"h-6 w-6\" />\r\n                      </button>\r\n\r\n                      {/* Enhanced Tooltip with interaction hint */}\r\n                      <div className=\"absolute left-full ml-3 top-0 bg-gray-900 text-white text-sm px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50 shadow-lg\">\r\n                        <div className=\"font-medium\">{category.name}</div>\r\n                        <div className=\"text-xs text-gray-300 mt-1\">\r\n                          Click to expand & go to {category.items[0]?.name || 'first item'}\r\n                        </div>\r\n                        <div className=\"absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-gray-900 rotate-45\"></div>\r\n                      </div>\r\n                    </div>\r\n                  );\r\n                }\r\n\r\n                // Expanded view - show full categories and items (always visible)\r\n                return (\r\n                  <div key={category.name}>\r\n                    {/* Category Header - Static Label */}\r\n                    <div className=\"category-header px-3 py-2 text-gray-700\">\r\n                      <span className=\"category-text uppercase tracking-wide text-sm font-bold leading-tight\">\r\n                        {category.name}\r\n                      </span>\r\n                    </div>\r\n\r\n                    {/* Category Items - Always Visible */}\r\n                    <div className=\"ml-3 space-y-1 mt-1 mb-4\">\r\n                      {category.items.map((item) => {\r\n                        const isActive = isItemActive(item.href);\r\n                        return (\r\n                          <Link\r\n                            key={item.name}\r\n                            href={item.href}\r\n                            onClick={handleNavClick}\r\n                            className={`group flex items-center px-3 py-2.5 text-sm font-medium rounded-md transition-colors ${\r\n                              isActive\r\n                                ? 'bg-orange-100 text-orange-700 border-r-2 border-orange-500'\r\n                                : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'\r\n                            }`}\r\n                          >\r\n                            <item.icon\r\n                              className={`mr-3 h-4 w-4 ${\r\n                                isActive ? 'text-orange-500' : 'text-gray-400 group-hover:text-gray-500'\r\n                              }`}\r\n                            />\r\n                            <span className=\"text-sm font-normal\">{item.name}</span>\r\n                          </Link>\r\n                        );\r\n                      })}\r\n                    </div>\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          </nav>\r\n\r\n          {/* Bottom Spacer for better scrolling experience */}\r\n          <div className=\"h-6\"></div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAuCA,MAAM,uBAA6C;IACjD;QACE,MAAM;QACN,MAAM,8NAAA,CAAA,WAAQ;QACd,OAAO;YACL;gBAAE,MAAM;gBAAQ,MAAM;gBAAS,MAAM,8NAAA,CAAA,WAAQ;YAAC;YAC9C;gBAAE,MAAM;gBAAU,MAAM;gBAAgB,MAAM,0PAAA,CAAA,yBAAsB;YAAC;SACtE;IACH;IACA;QACE,MAAM;QACN,MAAM,8NAAA,CAAA,WAAQ;QACd,OAAO;YACL;gBAAE,MAAM;gBAAa,MAAM;gBAAsB,MAAM,8NAAA,CAAA,WAAQ;YAAC;YAChE;gBAAE,MAAM;gBAAW,MAAM;gBAAoB,MAAM,8NAAA,CAAA,WAAQ;YAAC;SAC7D;IACH;IACA;QACE,MAAM;QACN,MAAM,4OAAA,CAAA,kBAAe;QACrB,OAAO;YACL;gBAAE,MAAM;gBAAiB,MAAM;gBAAmB,MAAM,4OAAA,CAAA,kBAAe;YAAC;YACxE;gBAAE,MAAM;gBAAe,MAAM;gBAAyB,MAAM,gOAAA,CAAA,YAAS;YAAC;YACtE;gBAAE,MAAM;gBAAa,MAAM;gBAAsB,MAAM,gOAAA,CAAA,YAAS;YAAC;YACjE;gBAAE,MAAM;gBAAW,MAAM;gBAAoB,MAAM,8NAAA,CAAA,WAAQ;YAAC;SAC7D;IACH;IACA;QACE,MAAM;QACN,MAAM,kOAAA,CAAA,aAAU;QAChB,OAAO;YACL;gBAAE,MAAM;gBAAa,MAAM;gBAAsB,MAAM,kOAAA,CAAA,aAAU;YAAC;YAClE;gBAAE,MAAM;gBAAmB,MAAM;gBAAoB,MAAM,0OAAA,CAAA,iBAAc;YAAC;SAC3E;IACH;IACA;QACE,MAAM;QACN,MAAM,8NAAA,CAAA,WAAQ;QACd,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;gBAAoB,MAAM,8NAAA,CAAA,WAAQ;YAAC;YACnE;gBAAE,MAAM;gBAAc,MAAM;gBAAuB,MAAM,8OAAA,CAAA,mBAAgB;YAAC;YAC1E;gBAAE,MAAM;gBAAa,MAAM;gBAAsB,MAAM,8NAAA,CAAA,WAAQ;YAAC;SACjE;IACH;IACA;QACE,MAAM;QACN,MAAM,wOAAA,CAAA,gBAAa;QACnB,OAAO;YACL;gBAAE,MAAM;gBAAe,MAAM;gBAAiB,MAAM,0PAAA,CAAA,yBAAsB;YAAC;YAC3E;gBAAE,MAAM;gBAAmB,MAAM;gBAAoB,MAAM,4PAAA,CAAA,0BAAuB;YAAC;YACnF;gBAAE,MAAM;gBAAiB,MAAM;gBAA0B,MAAM,8NAAA,CAAA,WAAQ;YAAC;YACxE;gBAAE,MAAM;gBAAY,MAAM;gBAAqB,MAAM,wOAAA,CAAA,gBAAa;YAAC;SACpE;IACH;CACD;AASc,SAAS,YAAY,EAClC,MAAM,EACN,OAAO,EACP,cAAc,KAAK,EACnB,mBAAmB,EACF;IACjB,MAAM,WAAW,CAAA,GAAA,iJAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe,CAAC;QACpB,OAAO,aAAa,QAAS,SAAS,WAAW,aAAa;IAChE;IAEA,+DAA+D;IAC/D,MAAM,iBAAiB;QACrB,sDAAsD;QACtD,IAAI,OAAO,UAAU,GAAG,MAAM;YAC5B;QACF;IACF;IAEA,MAAM,+BAA+B,CAAC;QACpC,IAAI,uBAAuB,SAAS,KAAK,CAAC,MAAM,GAAG,GAAG;YACpD,qCAAqC;YACrC,MAAM,YAAY,SAAS,KAAK,CAAC,EAAE;YACnC,4CAA4C;YAC5C,oBAAoB,UAAU,IAAI,EAAE,SAAS,IAAI;QACnD;IACF;IAEA,qBACE;;YAEG,wBACC,6PAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,6PAAC;gBAAI,WAAW,CAAC,mHAAmH,EAClI,SAAS,kBAAkB,oBAC5B,CAAC,EAAE,cAAc,SAAS,QAAQ;gBAAE,OAAO;oBAAE,KAAK;gBAAO;;kCAExD,6PAAC;wBAAI,WAAU;kCACb,cAAA,6PAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6PAAC,gOAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAKzB,6PAAC;wBAAI,WAAU;;0CAEb,6PAAC;gCAAI,WAAW,CAAC,KAAK,EAAE,cAAc,SAAS,QAAQ;0CACrD,cAAA,6PAAC;oCAAI,WAAU;8CACZ,qBAAqB,GAAG,CAAC,CAAC;wCAEzB,IAAI,aAAa;4CACf,0DAA0D;4CAC1D,qBACE,6PAAC;gDAAwB,WAAU;;kEACjC,6PAAC;wDACC,SAAS,IAAM,6BAA6B;wDAC5C,WAAU;wDACV,OAAO,GAAG,SAAS,IAAI,CAAC,4BAA4B,EAAE,SAAS,KAAK,CAAC,EAAE,EAAE,QAAQ,SAAS;kEAE1F,cAAA,6PAAC,SAAS,IAAI;4DAAC,WAAU;;;;;;;;;;;kEAI3B,6PAAC;wDAAI,WAAU;;0EACb,6PAAC;gEAAI,WAAU;0EAAe,SAAS,IAAI;;;;;;0EAC3C,6PAAC;gEAAI,WAAU;;oEAA6B;oEACjB,SAAS,KAAK,CAAC,EAAE,EAAE,QAAQ;;;;;;;0EAEtD,6PAAC;gEAAI,WAAU;;;;;;;;;;;;;+CAfT,SAAS,IAAI;;;;;wCAmB3B;wCAEA,kEAAkE;wCAClE,qBACE,6PAAC;;8DAEC,6PAAC;oDAAI,WAAU;8DACb,cAAA,6PAAC;wDAAK,WAAU;kEACb,SAAS,IAAI;;;;;;;;;;;8DAKlB,6PAAC;oDAAI,WAAU;8DACZ,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC;wDACnB,MAAM,WAAW,aAAa,KAAK,IAAI;wDACvC,qBACE,6PAAC,2KAAA,CAAA,UAAI;4DAEH,MAAM,KAAK,IAAI;4DACf,SAAS;4DACT,WAAW,CAAC,qFAAqF,EAC/F,WACI,+DACA,uDACJ;;8EAEF,6PAAC,KAAK,IAAI;oEACR,WAAW,CAAC,aAAa,EACvB,WAAW,oBAAoB,2CAC/B;;;;;;8EAEJ,6PAAC;oEAAK,WAAU;8EAAuB,KAAK,IAAI;;;;;;;2DAd3C,KAAK,IAAI;;;;;oDAiBpB;;;;;;;2CA/BM,SAAS,IAAI;;;;;oCAmC3B;;;;;;;;;;;0CAKJ,6PAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/hooks/useSSRSafeAuth.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { AuthContextType } from '@/types';\r\n\r\n/**\r\n * PROFESSIONAL SSR-safe authentication hook that prevents layout shifts\r\n * and ensures consistent auth state across server and client\r\n */\r\nexport function useSSRSafeAuth() {\r\n  const auth = useAuth() as AuthContextType & { authError?: string | null; isInitialized?: boolean };\r\n  const [isHydrated, setIsHydrated] = useState(false);\r\n\r\n  // Mark as hydrated after first render\r\n  useEffect(() => {\r\n    setIsHydrated(true);\r\n  }, []);\r\n\r\n  // PROFESSIONAL: Return consistent state that prevents layout shifts\r\n  return {\r\n    user: isHydrated ? auth.user : null,\r\n    loading: isHydrated ? auth.loading : true, // Show loading during SSR to prevent layout shifts\r\n    isAuthenticated: isHydrated ? !!auth.user : false,\r\n    authError: isHydrated ? auth.authError : null,\r\n    isInitialized: isHydrated ? auth.isInitialized : false, // Don't assume initialized during SSR\r\n    isHydrated,\r\n    signIn: auth.signIn,\r\n    signUp: auth.signUp,\r\n    signOut: auth.signOut,\r\n    updateProfile: auth.updateProfile,\r\n  };\r\n}\r\n\r\n/**\r\n * PROFESSIONAL Hook for components that need to show different content based on auth state\r\n * Prevents layout shifts by ensuring proper loading states\r\n */\r\nexport function useSSRSafeAuthState() {\r\n  const { user, loading, isHydrated, isInitialized } = useSSRSafeAuth();\r\n\r\n  return {\r\n    user,\r\n    loading,\r\n    isAuthenticated: !!user,\r\n    isHydrated,\r\n    isInitialized,\r\n    // PROFESSIONAL: Only show content when auth state is properly resolved\r\n    canShowAuthContent: isHydrated && isInitialized, // Only show when auth is resolved\r\n    canShowUserContent: isHydrated && isInitialized && !!user,\r\n    canShowGuestContent: isHydrated && isInitialized && !user,\r\n    // For components that need to wait for auth resolution\r\n    shouldWaitForAuth: !isHydrated || !isInitialized || loading,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;AAUO,SAAS;IACd,MAAM,OAAO,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;IACnB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,sCAAsC;IACtC,CAAA,GAAA,oNAAA,CAAA,YAAS,AAAD,EAAE;QACR,cAAc;IAChB,GAAG,EAAE;IAEL,oEAAoE;IACpE,OAAO;QACL,MAAM,aAAa,KAAK,IAAI,GAAG;QAC/B,SAAS,aAAa,KAAK,OAAO,GAAG;QACrC,iBAAiB,aAAa,CAAC,CAAC,KAAK,IAAI,GAAG;QAC5C,WAAW,aAAa,KAAK,SAAS,GAAG;QACzC,eAAe,aAAa,KAAK,aAAa,GAAG;QACjD;QACA,QAAQ,KAAK,MAAM;QACnB,QAAQ,KAAK,MAAM;QACnB,SAAS,KAAK,OAAO;QACrB,eAAe,KAAK,aAAa;IACnC;AACF;AAMO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAErD,OAAO;QACL;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB;QACA;QACA,uEAAuE;QACvE,oBAAoB,cAAc;QAClC,oBAAoB,cAAc,iBAAiB,CAAC,CAAC;QACrD,qBAAqB,cAAc,iBAAiB,CAAC;QACrD,uDAAuD;QACvD,mBAAmB,CAAC,cAAc,CAAC,iBAAiB;IACtD;AACF", "debugId": null}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/search/HeaderSearch.tsx"], "sourcesContent": ["'use client';\r\n\r\n// Professional Header Search Component - FoodPanda Style\r\n// Features: Real-time suggestions, debounced search, keyboard navigation\r\n\r\nimport React, { useState, useEffect, useRef, useCallback } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { MagnifyingGlassIcon, ClockIcon, FireIcon, XMarkIcon, SparklesIcon } from '@heroicons/react/24/outline';\r\n\r\ninterface SearchSuggestion {\r\n  text: string;\r\n  type: 'restaurant' | 'cuisine' | 'popular' | 'intelligent' | 'learned';\r\n  score?: number;\r\n}\r\n\r\ninterface HeaderSearchProps {\r\n  placeholder?: string;\r\n  className?: string;\r\n  isMobile?: boolean;\r\n}\r\n\r\nexport default function HeaderSearch({\r\n  placeholder = \"Search restaurants, cuisines...\",\r\n  className = \"\",\r\n  isMobile = false\r\n}: HeaderSearchProps) {\r\n  const router = useRouter();\r\n  const { user } = useAuth();\r\n  const [query, setQuery] = useState('');\r\n  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);\r\n  const [showSuggestions, setShowSuggestions] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [selectedIndex, setSelectedIndex] = useState(-1);\r\n  const [recentSearches, setRecentSearches] = useState<string[]>([]);\r\n  const [sessionId] = useState(() => `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);\r\n\r\n  const inputRef = useRef<HTMLInputElement>(null);\r\n  const suggestionsRef = useRef<HTMLDivElement>(null);\r\n  const debounceTimer = useRef<NodeJS.Timeout | null>(null);\r\n\r\n  // Load recent searches from localStorage\r\n  useEffect(() => {\r\n    const saved = localStorage.getItem('tap2go_recent_searches');\r\n    if (saved) {\r\n      try {\r\n        setRecentSearches(JSON.parse(saved));\r\n      } catch (error) {\r\n        console.error('Error loading recent searches:', error);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Fetch suggestions from API\r\n  const fetchSuggestions = useCallback(async (searchQuery: string) => {\r\n    if (!searchQuery || searchQuery.length < 2) {\r\n      // Show popular searches when no query\r\n      try {\r\n        const response = await fetch('/api/search/suggestions');\r\n        if (response.ok) {\r\n          const data = await response.json();\r\n          if (data.success) {\r\n            const popularSuggestions = data.data.suggestions.map((text: string) => ({\r\n              text,\r\n              type: 'popular' as const\r\n            }));\r\n            setSuggestions(popularSuggestions);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error fetching popular searches:', error);\r\n      }\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    try {\r\n      // Try learned suggestions first (with user context)\r\n      const learnedResponse = await fetch(\r\n        `/api/search/learn?type=suggestions&q=${encodeURIComponent(searchQuery)}${user?.id ? `&userId=${user.id}` : ''}`\r\n      );\r\n\r\n      let suggestions: SearchSuggestion[] = [];\r\n\r\n      if (learnedResponse.ok) {\r\n        const learnedData = await learnedResponse.json();\r\n        if (learnedData.success && learnedData.data.suggestions.length > 0) {\r\n          suggestions = learnedData.data.suggestions.map((text: string, index: number) => ({\r\n            text,\r\n            type: 'learned' as const,\r\n            score: learnedData.data.suggestions.length - index\r\n          }));\r\n        }\r\n      }\r\n\r\n      // Fallback to intelligent suggestions if no learned suggestions\r\n      if (suggestions.length === 0) {\r\n        const response = await fetch(`/api/search/suggestions?q=${encodeURIComponent(searchQuery)}`);\r\n        if (response.ok) {\r\n          const data = await response.json();\r\n          if (data.success) {\r\n            suggestions = data.data.suggestions.map((text: string, index: number) => ({\r\n              text,\r\n              type: 'intelligent' as const,\r\n              score: data.data.suggestions.length - index\r\n            }));\r\n          }\r\n        }\r\n      }\r\n\r\n      setSuggestions(suggestions);\r\n    } catch (error) {\r\n      console.error('Error fetching suggestions:', error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [user?.id]);\r\n\r\n  // Debounced search\r\n  const debouncedFetchSuggestions = useCallback((searchQuery: string) => {\r\n    if (debounceTimer.current) {\r\n      clearTimeout(debounceTimer.current);\r\n    }\r\n\r\n    debounceTimer.current = setTimeout(() => {\r\n      fetchSuggestions(searchQuery);\r\n    }, 300); // 300ms delay like FoodPanda\r\n  }, [fetchSuggestions]);\r\n\r\n  // Handle input change\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const value = e.target.value;\r\n    setQuery(value);\r\n    setSelectedIndex(-1);\r\n    \r\n    if (value.length >= 0) {\r\n      setShowSuggestions(true);\r\n      debouncedFetchSuggestions(value);\r\n    } else {\r\n      setShowSuggestions(false);\r\n      setSuggestions([]);\r\n    }\r\n  };\r\n\r\n  // Handle input focus\r\n  const handleInputFocus = () => {\r\n    setShowSuggestions(true);\r\n    if (query.length === 0) {\r\n      fetchSuggestions(''); // Load popular searches\r\n    }\r\n  };\r\n\r\n  // Handle input blur (with delay to allow suggestion clicks)\r\n  const handleInputBlur = () => {\r\n    setTimeout(() => {\r\n      setShowSuggestions(false);\r\n      setSelectedIndex(-1);\r\n    }, 200);\r\n  };\r\n\r\n  // Save search to recent searches\r\n  const saveToRecentSearches = (searchTerm: string) => {\r\n    if (!searchTerm.trim()) return;\r\n    \r\n    const updated = [searchTerm, ...recentSearches.filter(s => s !== searchTerm)].slice(0, 5);\r\n    setRecentSearches(updated);\r\n    localStorage.setItem('tap2go_recent_searches', JSON.stringify(updated));\r\n  };\r\n\r\n  // Track search event for learning\r\n  const trackSearch = async (searchTerm: string, resultCount: number = 0) => {\r\n    try {\r\n      await fetch('/api/search/learn', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({\r\n          action: 'track_search',\r\n          query: searchTerm,\r\n          results: resultCount,\r\n          userId: user?.id,\r\n          sessionId\r\n        })\r\n      });\r\n    } catch (error) {\r\n      console.error('Error tracking search:', error);\r\n    }\r\n  };\r\n\r\n  // Track suggestion click for learning\r\n  const trackSuggestionClick = async (suggestion: SearchSuggestion, position: number) => {\r\n    try {\r\n      await fetch('/api/search/learn', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({\r\n          action: 'track_click',\r\n          query: query,\r\n          clickedResult: suggestion.text,\r\n          position: position,\r\n          userId: user?.id,\r\n          sessionId\r\n        })\r\n      });\r\n    } catch (error) {\r\n      console.error('Error tracking click:', error);\r\n    }\r\n  };\r\n\r\n  // Handle search submission\r\n  const handleSearch = (searchTerm: string) => {\r\n    if (!searchTerm.trim()) return;\r\n\r\n    saveToRecentSearches(searchTerm.trim());\r\n    trackSearch(searchTerm.trim()); // Track for learning\r\n    setShowSuggestions(false);\r\n    setQuery('');\r\n    router.push(`/search?q=${encodeURIComponent(searchTerm.trim())}`);\r\n  };\r\n\r\n  // Handle form submit\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (selectedIndex >= 0 && suggestions[selectedIndex]) {\r\n      handleSearch(suggestions[selectedIndex].text);\r\n    } else {\r\n      handleSearch(query);\r\n    }\r\n  };\r\n\r\n  // Handle suggestion click\r\n  const handleSuggestionClick = (suggestion: SearchSuggestion, index: number) => {\r\n    trackSuggestionClick(suggestion, index); // Track for learning\r\n    handleSearch(suggestion.text);\r\n  };\r\n\r\n  // Handle keyboard navigation\r\n  const handleKeyDown = (e: React.KeyboardEvent) => {\r\n    if (!showSuggestions || suggestions.length === 0) return;\r\n\r\n    switch (e.key) {\r\n      case 'ArrowDown':\r\n        e.preventDefault();\r\n        setSelectedIndex(prev => \r\n          prev < suggestions.length - 1 ? prev + 1 : prev\r\n        );\r\n        break;\r\n      case 'ArrowUp':\r\n        e.preventDefault();\r\n        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);\r\n        break;\r\n      case 'Escape':\r\n        setShowSuggestions(false);\r\n        setSelectedIndex(-1);\r\n        inputRef.current?.blur();\r\n        break;\r\n    }\r\n  };\r\n\r\n  // Clear search\r\n  const clearSearch = () => {\r\n    setQuery('');\r\n    setShowSuggestions(false);\r\n    setSuggestions([]);\r\n    inputRef.current?.focus();\r\n  };\r\n\r\n  // Get suggestion icon\r\n  const getSuggestionIcon = (type: string) => {\r\n    switch (type) {\r\n      case 'popular':\r\n        return <FireIcon className=\"h-4 w-4 text-orange-500\" />;\r\n      case 'recent':\r\n        return <ClockIcon className=\"h-4 w-4 text-gray-400\" />;\r\n      case 'intelligent':\r\n        return <MagnifyingGlassIcon className=\"h-4 w-4 text-blue-500\" />;\r\n      case 'learned':\r\n        return <SparklesIcon className=\"h-4 w-4 text-purple-500\" />;\r\n      default:\r\n        return <MagnifyingGlassIcon className=\"h-4 w-4 text-gray-400\" />;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      <form onSubmit={handleSubmit} className=\"relative\">\r\n        <MagnifyingGlassIcon className={`absolute ${isMobile ? 'left-4' : 'left-3'} top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400`} />\r\n        <input\r\n          ref={inputRef}\r\n          type=\"text\"\r\n          placeholder={placeholder}\r\n          value={query}\r\n          onChange={handleInputChange}\r\n          onFocus={handleInputFocus}\r\n          onBlur={handleInputBlur}\r\n          onKeyDown={handleKeyDown}\r\n          className={`\r\n            w-full ${isMobile ? 'pl-12 pr-12 py-3 rounded-full' : 'pl-10 pr-10 py-2 rounded-lg'} \r\n            bg-white text-gray-700 placeholder-gray-500 border border-gray-300\r\n            focus:outline-none focus:ring-2 focus:ring-white focus:border-transparent\r\n            transition-all duration-200\r\n            ${showSuggestions && suggestions.length > 0 ? 'rounded-b-none border-b-0' : ''}\r\n          `}\r\n        />\r\n        {query && (\r\n          <button\r\n            type=\"button\"\r\n            onClick={clearSearch}\r\n            className={`absolute ${isMobile ? 'right-4' : 'right-3'} top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600`}\r\n          >\r\n            <XMarkIcon className=\"h-5 w-5\" />\r\n          </button>\r\n        )}\r\n      </form>\r\n\r\n      {/* Suggestions Dropdown */}\r\n      {showSuggestions && suggestions.length > 0 && (\r\n        <div \r\n          ref={suggestionsRef}\r\n          className=\"absolute top-full left-0 right-0 bg-white border border-gray-300 border-t-0 rounded-b-lg shadow-lg max-h-80 overflow-y-auto z-50\"\r\n        >\r\n          {/* Recent Searches */}\r\n          {query.length === 0 && recentSearches.length > 0 && (\r\n            <>\r\n              <div className=\"px-4 py-2 text-xs font-medium text-gray-500 bg-gray-50 border-b\">\r\n                Recent Searches\r\n              </div>\r\n              {recentSearches.slice(0, 3).map((search, index) => (\r\n                <button\r\n                  key={`recent-${index}`}\r\n                  onClick={() => handleSearch(search)}\r\n                  className=\"w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center space-x-3 border-b border-gray-100\"\r\n                >\r\n                  <ClockIcon className=\"h-4 w-4 text-gray-400\" />\r\n                  <span className=\"text-gray-700\">{search}</span>\r\n                </button>\r\n              ))}\r\n              {suggestions.length > 0 && (\r\n                <div className=\"px-4 py-2 text-xs font-medium text-gray-500 bg-gray-50 border-b\">\r\n                  {query.length === 0 ? 'Popular Searches' : 'Suggestions'}\r\n                </div>\r\n              )}\r\n            </>\r\n          )}\r\n\r\n          {/* Suggestions */}\r\n          {suggestions.map((suggestion, index) => (\r\n            <button\r\n              key={index}\r\n              onClick={() => handleSuggestionClick(suggestion, index)}\r\n              className={`\r\n                w-full px-4 py-3 text-left hover:bg-gray-50 flex items-center space-x-3\r\n                ${selectedIndex === index ? 'bg-orange-50 border-l-2 border-orange-500' : ''}\r\n                ${index === suggestions.length - 1 ? '' : 'border-b border-gray-100'}\r\n              `}\r\n            >\r\n              {getSuggestionIcon(suggestion.type)}\r\n              <span className=\"text-gray-700\">{suggestion.text}</span>\r\n              {suggestion.type === 'learned' && (\r\n                <span className=\"ml-auto text-xs text-purple-500 font-medium\">Smart</span>\r\n              )}\r\n            </button>\r\n          ))}\r\n\r\n          {/* Loading state */}\r\n          {isLoading && (\r\n            <div className=\"px-4 py-3 text-center text-gray-500\">\r\n              <div className=\"animate-spin inline-block w-4 h-4 border-2 border-orange-500 border-t-transparent rounded-full\"></div>\r\n              <span className=\"ml-2\">Loading suggestions...</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,yDAAyD;AACzD,yEAAyE;AAEzE;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;AAsBe,SAAS,aAAa,EACnC,cAAc,iCAAiC,EAC/C,YAAY,EAAE,EACd,WAAW,KAAK,EACE;IAClB,MAAM,SAAS,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACrE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,UAAU,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE,IAAM,CAAC,QAAQ,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAErG,MAAM,WAAW,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,iBAAiB,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,gBAAgB,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAyB;IAEpD,yCAAyC;IACzC,CAAA,GAAA,oNAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACT,IAAI;gBACF,kBAAkB,KAAK,KAAK,CAAC;YAC/B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;YAClD;QACF;IACF,GAAG,EAAE;IAEL,6BAA6B;IAC7B,MAAM,mBAAmB,CAAA,GAAA,oNAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC1C,IAAI,CAAC,eAAe,YAAY,MAAM,GAAG,GAAG;YAC1C,sCAAsC;YACtC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,IAAI,KAAK,OAAO,EAAE;wBAChB,MAAM,qBAAqB,KAAK,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,OAAiB,CAAC;gCACtE;gCACA,MAAM;4BACR,CAAC;wBACD,eAAe;oBACjB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD;YACA;QACF;QAEA,aAAa;QACb,IAAI;YACF,oDAAoD;YACpD,MAAM,kBAAkB,MAAM,MAC5B,CAAC,qCAAqC,EAAE,mBAAmB,eAAe,MAAM,KAAK,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,GAAG,IAAI;YAGlH,IAAI,cAAkC,EAAE;YAExC,IAAI,gBAAgB,EAAE,EAAE;gBACtB,MAAM,cAAc,MAAM,gBAAgB,IAAI;gBAC9C,IAAI,YAAY,OAAO,IAAI,YAAY,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,GAAG;oBAClE,cAAc,YAAY,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,MAAc,QAAkB,CAAC;4BAC/E;4BACA,MAAM;4BACN,OAAO,YAAY,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG;wBAC/C,CAAC;gBACH;YACF;YAEA,gEAAgE;YAChE,IAAI,YAAY,MAAM,KAAK,GAAG;gBAC5B,MAAM,WAAW,MAAM,MAAM,CAAC,0BAA0B,EAAE,mBAAmB,cAAc;gBAC3F,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,IAAI,KAAK,OAAO,EAAE;wBAChB,cAAc,KAAK,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,MAAc,QAAkB,CAAC;gCACxE;gCACA,MAAM;gCACN,OAAO,KAAK,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG;4BACxC,CAAC;oBACH;gBACF;YACF;YAEA,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC,MAAM;KAAG;IAEb,mBAAmB;IACnB,MAAM,4BAA4B,CAAA,GAAA,oNAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7C,IAAI,cAAc,OAAO,EAAE;YACzB,aAAa,cAAc,OAAO;QACpC;QAEA,cAAc,OAAO,GAAG,WAAW;YACjC,iBAAiB;QACnB,GAAG,MAAM,6BAA6B;IACxC,GAAG;QAAC;KAAiB;IAErB,sBAAsB;IACtB,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,SAAS;QACT,iBAAiB,CAAC;QAElB,IAAI,MAAM,MAAM,IAAI,GAAG;YACrB,mBAAmB;YACnB,0BAA0B;QAC5B,OAAO;YACL,mBAAmB;YACnB,eAAe,EAAE;QACnB;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAmB;QACvB,mBAAmB;QACnB,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,iBAAiB,KAAK,wBAAwB;QAChD;IACF;IAEA,4DAA4D;IAC5D,MAAM,kBAAkB;QACtB,WAAW;YACT,mBAAmB;YACnB,iBAAiB,CAAC;QACpB,GAAG;IACL;IAEA,iCAAiC;IACjC,MAAM,uBAAuB,CAAC;QAC5B,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,MAAM,UAAU;YAAC;eAAe,eAAe,MAAM,CAAC,CAAA,IAAK,MAAM;SAAY,CAAC,KAAK,CAAC,GAAG;QACvF,kBAAkB;QAClB,aAAa,OAAO,CAAC,0BAA0B,KAAK,SAAS,CAAC;IAChE;IAEA,kCAAkC;IAClC,MAAM,cAAc,OAAO,YAAoB,cAAsB,CAAC;QACpE,IAAI;YACF,MAAM,MAAM,qBAAqB;gBAC/B,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,OAAO;oBACP,SAAS;oBACT,QAAQ,MAAM;oBACd;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,sCAAsC;IACtC,MAAM,uBAAuB,OAAO,YAA8B;QAChE,IAAI;YACF,MAAM,MAAM,qBAAqB;gBAC/B,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,OAAO;oBACP,eAAe,WAAW,IAAI;oBAC9B,UAAU;oBACV,QAAQ,MAAM;oBACd;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,2BAA2B;IAC3B,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,qBAAqB,WAAW,IAAI;QACpC,YAAY,WAAW,IAAI,KAAK,qBAAqB;QACrD,mBAAmB;QACnB,SAAS;QACT,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,mBAAmB,WAAW,IAAI,KAAK;IAClE;IAEA,qBAAqB;IACrB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,iBAAiB,KAAK,WAAW,CAAC,cAAc,EAAE;YACpD,aAAa,WAAW,CAAC,cAAc,CAAC,IAAI;QAC9C,OAAO;YACL,aAAa;QACf;IACF;IAEA,0BAA0B;IAC1B,MAAM,wBAAwB,CAAC,YAA8B;QAC3D,qBAAqB,YAAY,QAAQ,qBAAqB;QAC9D,aAAa,WAAW,IAAI;IAC9B;IAEA,6BAA6B;IAC7B,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,mBAAmB,YAAY,MAAM,KAAK,GAAG;QAElD,OAAQ,EAAE,GAAG;YACX,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OACf,OAAO,YAAY,MAAM,GAAG,IAAI,OAAO,IAAI;gBAE7C;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OAAQ,OAAO,IAAI,OAAO,IAAI,CAAC;gBAChD;YACF,KAAK;gBACH,mBAAmB;gBACnB,iBAAiB,CAAC;gBAClB,SAAS,OAAO,EAAE;gBAClB;QACJ;IACF;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,SAAS;QACT,mBAAmB;QACnB,eAAe,EAAE;QACjB,SAAS,OAAO,EAAE;IACpB;IAEA,sBAAsB;IACtB,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,qBAAO,6PAAC,8NAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6PAAC,gOAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;YAC9B,KAAK;gBACH,qBAAO,6PAAC,oPAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;YACxC,KAAK;gBACH,qBAAO,6PAAC,sOAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACjC;gBACE,qBAAO,6PAAC,oPAAA,CAAA,sBAAmB;oBAAC,WAAU;;;;;;QAC1C;IACF;IAEA,qBACE,6PAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BACrC,6PAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,6PAAC,oPAAA,CAAA,sBAAmB;wBAAC,WAAW,CAAC,SAAS,EAAE,WAAW,WAAW,SAAS,yDAAyD,CAAC;;;;;;kCACrI,6PAAC;wBACC,KAAK;wBACL,MAAK;wBACL,aAAa;wBACb,OAAO;wBACP,UAAU;wBACV,SAAS;wBACT,QAAQ;wBACR,WAAW;wBACX,WAAW,CAAC;mBACH,EAAE,WAAW,kCAAkC,8BAA8B;;;;YAIpF,EAAE,mBAAmB,YAAY,MAAM,GAAG,IAAI,8BAA8B,GAAG;UACjF,CAAC;;;;;;oBAEF,uBACC,6PAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAW,CAAC,SAAS,EAAE,WAAW,YAAY,UAAU,qEAAqE,CAAC;kCAE9H,cAAA,6PAAC,gOAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAM1B,mBAAmB,YAAY,MAAM,GAAG,mBACvC,6PAAC;gBACC,KAAK;gBACL,WAAU;;oBAGT,MAAM,MAAM,KAAK,KAAK,eAAe,MAAM,GAAG,mBAC7C;;0CACE,6PAAC;gCAAI,WAAU;0CAAkE;;;;;;4BAGhF,eAAe,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,sBACvC,6PAAC;oCAEC,SAAS,IAAM,aAAa;oCAC5B,WAAU;;sDAEV,6PAAC,gOAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6PAAC;4CAAK,WAAU;sDAAiB;;;;;;;mCAL5B,CAAC,OAAO,EAAE,OAAO;;;;;4BAQzB,YAAY,MAAM,GAAG,mBACpB,6PAAC;gCAAI,WAAU;0CACZ,MAAM,MAAM,KAAK,IAAI,qBAAqB;;;;;;;;oBAOlD,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6PAAC;4BAEC,SAAS,IAAM,sBAAsB,YAAY;4BACjD,WAAW,CAAC;;gBAEV,EAAE,kBAAkB,QAAQ,8CAA8C,GAAG;gBAC7E,EAAE,UAAU,YAAY,MAAM,GAAG,IAAI,KAAK,2BAA2B;cACvE,CAAC;;gCAEA,kBAAkB,WAAW,IAAI;8CAClC,6PAAC;oCAAK,WAAU;8CAAiB,WAAW,IAAI;;;;;;gCAC/C,WAAW,IAAI,KAAK,2BACnB,6PAAC;oCAAK,WAAU;8CAA8C;;;;;;;2BAX3D;;;;;oBAiBR,2BACC,6PAAC;wBAAI,WAAU;;0CACb,6PAAC;gCAAI,WAAU;;;;;;0CACf,6PAAC;gCAAK,WAAU;0CAAO;;;;;;;;;;;;;;;;;;;;;;;;AAOrC", "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/lib/fcm.ts"], "sourcesContent": ["/**\r\n * Firebase Cloud Messaging Service\r\n * Handles FCM token generation, storage, and notification management\r\n */\r\n\r\nimport { getToken, onMessage, MessagePayload, Messaging } from 'firebase/messaging';\r\nimport { doc, setDoc, serverTimestamp } from 'firebase/firestore';\r\nimport { initializeMessaging } from './firebase';\r\nimport { db } from './firebase';\r\n\r\n// FCM Configuration\r\nconst VAPID_KEY = process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY;\r\n\r\n// Types\r\nexport interface FCMToken {\r\n  token: string;\r\n  userId: string;\r\n  deviceType: 'web' | 'mobile';\r\n  userAgent: string;\r\n  createdAt: unknown;\r\n  updatedAt: unknown;\r\n  isActive: boolean;\r\n}\r\n\r\nexport interface NotificationPayload {\r\n  title: string;\r\n  body: string;\r\n  icon?: string;\r\n  badge?: string;\r\n  image?: string;\r\n  data?: Record<string, unknown>;\r\n  actions?: Array<{\r\n    action: string;\r\n    title: string;\r\n    icon?: string;\r\n  }>;\r\n}\r\n\r\n/**\r\n * FCM Service Class\r\n */\r\nexport class FCMService {\r\n  private static messaging: Messaging | null = null;\r\n\r\n  /**\r\n   * Initialize FCM\r\n   */\r\n  static async initialize(): Promise<boolean> {\r\n    try {\r\n      const messagingInstance = await initializeMessaging();\r\n      this.messaging = messagingInstance as Messaging | null;\r\n      return !!this.messaging;\r\n    } catch (error) {\r\n      console.error('Failed to initialize FCM:', error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Request notification permission\r\n   */\r\n  static async requestPermission(): Promise<NotificationPermission> {\r\n    if (!('Notification' in window)) {\r\n      console.warn('This browser does not support notifications');\r\n      return 'denied';\r\n    }\r\n\r\n    if (Notification.permission === 'granted') {\r\n      return 'granted';\r\n    }\r\n\r\n    if (Notification.permission === 'denied') {\r\n      return 'denied';\r\n    }\r\n\r\n    // Request permission\r\n    const permission = await Notification.requestPermission();\r\n    console.log('Notification permission:', permission);\r\n    return permission;\r\n  }\r\n\r\n  /**\r\n   * Generate FCM token\r\n   */\r\n  static async generateToken(userId: string): Promise<string | null> {\r\n    try {\r\n      if (!this.messaging) {\r\n        await this.initialize();\r\n      }\r\n\r\n      if (!this.messaging) {\r\n        throw new Error('FCM not initialized');\r\n      }\r\n\r\n      if (!VAPID_KEY) {\r\n        throw new Error('VAPID key not configured');\r\n      }\r\n\r\n      // Request permission first\r\n      const permission = await this.requestPermission();\r\n      if (permission !== 'granted') {\r\n        throw new Error('Notification permission denied');\r\n      }\r\n\r\n      // Generate token\r\n      const token = await getToken(this.messaging as Messaging, {\r\n        vapidKey: VAPID_KEY,\r\n      });\r\n\r\n      if (token) {\r\n        console.log('FCM token generated:', token);\r\n        \r\n        // Store token in Firestore\r\n        await this.storeToken(token, userId);\r\n        \r\n        return token;\r\n      } else {\r\n        throw new Error('Failed to generate FCM token');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error generating FCM token:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Store FCM token in Firestore\r\n   */\r\n  static async storeToken(token: string, userId: string): Promise<void> {\r\n    try {\r\n      const tokenData: FCMToken = {\r\n        token,\r\n        userId,\r\n        deviceType: 'web',\r\n        userAgent: navigator.userAgent,\r\n        createdAt: serverTimestamp(),\r\n        updatedAt: serverTimestamp(),\r\n        isActive: true,\r\n      };\r\n\r\n      // Store in user's FCM tokens subcollection\r\n      const tokenRef = doc(db, `users/${userId}/fcmTokens`, token);\r\n      await setDoc(tokenRef, tokenData, { merge: true });\r\n\r\n      console.log('FCM token stored successfully');\r\n    } catch (error) {\r\n      console.error('Error storing FCM token:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get stored FCM token for user\r\n   */\r\n  static async getStoredToken(userId: string): Promise<string | null> {\r\n    try {\r\n      // This would typically get the most recent active token\r\n      // For now, we'll implement a simple approach\r\n      return localStorage.getItem(`fcm_token_${userId}`);\r\n    } catch (error) {\r\n      console.error('Error getting stored FCM token:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Set up foreground message listener\r\n   */\r\n  static setupForegroundListener(callback: (payload: MessagePayload) => void): void {\r\n    if (!this.messaging) {\r\n      console.warn('FCM not initialized, cannot set up foreground listener');\r\n      return;\r\n    }\r\n\r\n    onMessage(this.messaging as Messaging, (payload) => {\r\n      console.log('Foreground message received:', payload);\r\n      callback(payload);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Show notification\r\n   */\r\n  static async showNotification(payload: NotificationPayload): Promise<void> {\r\n    try {\r\n      if (!('serviceWorker' in navigator)) {\r\n        throw new Error('Service Worker not supported');\r\n      }\r\n\r\n      const registration = await navigator.serviceWorker.ready;\r\n      \r\n      const notificationOptions: NotificationOptions & {\r\n        image?: string;\r\n        actions?: Array<{\r\n          action: string;\r\n          title: string;\r\n          icon?: string;\r\n        }>;\r\n      } = {\r\n        body: payload.body,\r\n        icon: payload.icon || '/favicon.ico',\r\n        badge: payload.badge || '/favicon.ico',\r\n        data: payload.data,\r\n        tag: 'tap2go-notification',\r\n        requireInteraction: true,\r\n        // Note: 'image' and 'actions' are not part of standard NotificationOptions in all browsers\r\n        ...(payload.image && { image: payload.image }),\r\n        ...(payload.actions && { actions: payload.actions }),\r\n      };\r\n\r\n      await registration.showNotification(payload.title, notificationOptions);\r\n    } catch (error) {\r\n      console.error('Error showing notification:', error);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handle notification click (for service worker context)\r\n   * Note: This method is intended to be used in a service worker context\r\n   */\r\n  static handleNotificationClick(event: Event & {\r\n    notification: Notification;\r\n    waitUntil: (promise: Promise<unknown>) => void\r\n  }): void {\r\n    console.log('Notification clicked:', event.notification);\r\n\r\n    event.notification.close();\r\n\r\n    // Handle different notification types\r\n    const data = event.notification.data;\r\n    if (data?.url && typeof self !== 'undefined' && 'clients' in self) {\r\n      event.waitUntil(\r\n        (self as { clients: { openWindow: (url: string) => Promise<unknown> } }).clients.openWindow(data.url)\r\n      );\r\n    }\r\n  }\r\n}\r\n\r\n// Export for convenience\r\nexport default FCMService;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AAAA;AACA;AAAA;AACA;;;;;AAGA,oBAAoB;AACpB,MAAM;AA8BC,MAAM;IACX,OAAe,YAA8B,KAAK;IAElD;;GAEC,GACD,aAAa,aAA+B;QAC1C,IAAI;YACF,MAAM,oBAAoB,MAAM,CAAA,GAAA,qIAAA,CAAA,sBAAmB,AAAD;YAClD,IAAI,CAAC,SAAS,GAAG;YACjB,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;QACT;IACF;IAEA;;GAEC,GACD,aAAa,oBAAqD;QAChE,IAAI,CAAC,CAAC,kBAAkB,MAAM,GAAG;YAC/B,QAAQ,IAAI,CAAC;YACb,OAAO;QACT;QAEA,IAAI,aAAa,UAAU,KAAK,WAAW;YACzC,OAAO;QACT;QAEA,IAAI,aAAa,UAAU,KAAK,UAAU;YACxC,OAAO;QACT;QAEA,qBAAqB;QACrB,MAAM,aAAa,MAAM,aAAa,iBAAiB;QACvD,QAAQ,GAAG,CAAC,4BAA4B;QACxC,OAAO;IACT;IAEA;;GAEC,GACD,aAAa,cAAc,MAAc,EAA0B;QACjE,IAAI;YACF,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,MAAM,IAAI,CAAC,UAAU;YACvB;YAEA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,MAAM,IAAI,MAAM;YAClB;YAEA,uCAAgB;;YAEhB;YAEA,2BAA2B;YAC3B,MAAM,aAAa,MAAM,IAAI,CAAC,iBAAiB;YAC/C,IAAI,eAAe,WAAW;gBAC5B,MAAM,IAAI,MAAM;YAClB;YAEA,iBAAiB;YACjB,MAAM,QAAQ,MAAM,CAAA,GAAA,0KAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,CAAC,SAAS,EAAe;gBACxD,UAAU;YACZ;YAEA,IAAI,OAAO;gBACT,QAAQ,GAAG,CAAC,wBAAwB;gBAEpC,2BAA2B;gBAC3B,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO;gBAE7B,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACT;IACF;IAEA;;GAEC,GACD,aAAa,WAAW,KAAa,EAAE,MAAc,EAAiB;QACpE,IAAI;YACF,MAAM,YAAsB;gBAC1B;gBACA;gBACA,YAAY;gBACZ,WAAW,UAAU,SAAS;gBAC9B,WAAW,CAAA,GAAA,iKAAA,CAAA,kBAAe,AAAD;gBACzB,WAAW,CAAA,GAAA,iKAAA,CAAA,kBAAe,AAAD;gBACzB,UAAU;YACZ;YAEA,2CAA2C;YAC3C,MAAM,WAAW,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,qIAAA,CAAA,KAAE,EAAE,CAAC,MAAM,EAAE,OAAO,UAAU,CAAC,EAAE;YACtD,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,UAAU,WAAW;gBAAE,OAAO;YAAK;YAEhD,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,eAAe,MAAc,EAA0B;QAClE,IAAI;YACF,wDAAwD;YACxD,6CAA6C;YAC7C,OAAO,aAAa,OAAO,CAAC,CAAC,UAAU,EAAE,QAAQ;QACnD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,OAAO,wBAAwB,QAA2C,EAAQ;QAChF,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,CAAA,GAAA,0KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,SAAS,EAAe,CAAC;YACtC,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,SAAS;QACX;IACF;IAEA;;GAEC,GACD,aAAa,iBAAiB,OAA4B,EAAiB;QACzE,IAAI;YACF,IAAI,CAAC,CAAC,mBAAmB,SAAS,GAAG;gBACnC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,eAAe,MAAM,UAAU,aAAa,CAAC,KAAK;YAExD,MAAM,sBAOF;gBACF,MAAM,QAAQ,IAAI;gBAClB,MAAM,QAAQ,IAAI,IAAI;gBACtB,OAAO,QAAQ,KAAK,IAAI;gBACxB,MAAM,QAAQ,IAAI;gBAClB,KAAK;gBACL,oBAAoB;gBACpB,2FAA2F;gBAC3F,GAAI,QAAQ,KAAK,IAAI;oBAAE,OAAO,QAAQ,KAAK;gBAAC,CAAC;gBAC7C,GAAI,QAAQ,OAAO,IAAI;oBAAE,SAAS,QAAQ,OAAO;gBAAC,CAAC;YACrD;YAEA,MAAM,aAAa,gBAAgB,CAAC,QAAQ,KAAK,EAAE;QACrD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF;IAEA;;;GAGC,GACD,OAAO,wBAAwB,KAG9B,EAAQ;QACP,QAAQ,GAAG,CAAC,yBAAyB,MAAM,YAAY;QAEvD,MAAM,YAAY,CAAC,KAAK;QAExB,sCAAsC;QACtC,MAAM,OAAO,MAAM,YAAY,CAAC,IAAI;QACpC,IAAI,MAAM,OAAO,OAAO,SAAS,eAAe,aAAa,MAAM;YACjE,MAAM,SAAS,CACb,AAAC,KAAwE,OAAO,CAAC,UAAU,CAAC,KAAK,GAAG;QAExG;IACF;AACF;uCAGe", "debugId": null}}, {"offset": {"line": 1084, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/hooks/useAuth.ts"], "sourcesContent": ["/**\r\n * React Hook for Firebase Authentication\r\n * Handles user authentication state and operations\r\n */\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { \r\n  User,\r\n  onAuthStateChanged,\r\n  signInWithEmailAndPassword,\r\n  createUserWithEmailAndPassword,\r\n  signOut,\r\n  updateProfile,\r\n} from 'firebase/auth';\r\nimport { auth } from '@/lib/firebase';\r\n\r\nexport interface AuthState {\r\n  user: User | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n}\r\n\r\nexport interface AuthActions {\r\n  signIn: (email: string, password: string) => Promise<User | null>;\r\n  signUp: (email: string, password: string, displayName?: string) => Promise<User | null>;\r\n  logout: () => Promise<void>;\r\n  clearError: () => void;\r\n}\r\n\r\n/**\r\n * Custom hook for Firebase Authentication\r\n */\r\nexport const useAuth = (): AuthState & AuthActions => {\r\n  const [state, setState] = useState<AuthState>({\r\n    user: null,\r\n    loading: true,\r\n    error: null,\r\n  });\r\n\r\n  // Listen to authentication state changes\r\n  useEffect(() => {\r\n    const unsubscribe = onAuthStateChanged(auth, (user) => {\r\n      setState(prev => ({\r\n        ...prev,\r\n        user,\r\n        loading: false,\r\n      }));\r\n    });\r\n\r\n    return unsubscribe;\r\n  }, []);\r\n\r\n  // Sign in with email and password\r\n  const signIn = async (email: string, password: string): Promise<User | null> => {\r\n    try {\r\n      setState(prev => ({ ...prev, loading: true, error: null }));\r\n      \r\n      const userCredential = await signInWithEmailAndPassword(auth, email, password);\r\n      const user = userCredential.user;\r\n      \r\n      setState(prev => ({ ...prev, user, loading: false }));\r\n      return user;\r\n    } catch (error: unknown) {\r\n      const authError = error as { message?: string };\r\n      const errorMessage = authError.message || 'Failed to sign in';\r\n      setState(prev => ({ ...prev, loading: false, error: errorMessage }));\r\n      return null;\r\n    }\r\n  };\r\n\r\n  // Sign up with email and password\r\n  const signUp = async (email: string, password: string, displayName?: string): Promise<User | null> => {\r\n    try {\r\n      setState(prev => ({ ...prev, loading: true, error: null }));\r\n      \r\n      const userCredential = await createUserWithEmailAndPassword(auth, email, password);\r\n      const user = userCredential.user;\r\n      \r\n      // Update display name if provided\r\n      if (displayName) {\r\n        await updateProfile(user, { displayName });\r\n      }\r\n      \r\n      setState(prev => ({ ...prev, user, loading: false }));\r\n      return user;\r\n    } catch (error: unknown) {\r\n      const authError = error as { message?: string };\r\n      const errorMessage = authError.message || 'Failed to create account';\r\n      setState(prev => ({ ...prev, loading: false, error: errorMessage }));\r\n      return null;\r\n    }\r\n  };\r\n\r\n  // Sign out\r\n  const logout = async (): Promise<void> => {\r\n    try {\r\n      setState(prev => ({ ...prev, loading: true, error: null }));\r\n      await signOut(auth);\r\n      setState(prev => ({ ...prev, user: null, loading: false }));\r\n    } catch (error: unknown) {\r\n      const authError = error as { message?: string };\r\n      const errorMessage = authError.message || 'Failed to sign out';\r\n      setState(prev => ({ ...prev, loading: false, error: errorMessage }));\r\n    }\r\n  };\r\n\r\n  // Clear error\r\n  const clearError = (): void => {\r\n    setState(prev => ({ ...prev, error: null }));\r\n  };\r\n\r\n  return {\r\n    ...state,\r\n    signIn,\r\n    signUp,\r\n    logout,\r\n    clearError,\r\n  };\r\n};\r\n\r\nexport default useAuth;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAED;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;;;;AAkBO,MAAM,UAAU;IACrB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAa;QAC5C,MAAM;QACN,SAAS;QACT,OAAO;IACT;IAEA,yCAAyC;IACzC,CAAA,GAAA,oNAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,qIAAA,CAAA,OAAI,EAAE,CAAC;YAC5C,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP;oBACA,SAAS;gBACX,CAAC;QACH;QAEA,OAAO;IACT,GAAG,EAAE;IAEL,kCAAkC;IAClC,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK,CAAC;YAEzD,MAAM,iBAAiB,MAAM,CAAA,GAAA,iOAAA,CAAA,6BAA0B,AAAD,EAAE,qIAAA,CAAA,OAAI,EAAE,OAAO;YACrE,MAAM,OAAO,eAAe,IAAI;YAEhC,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE;oBAAM,SAAS;gBAAM,CAAC;YACnD,OAAO;QACT,EAAE,OAAO,OAAgB;YACvB,MAAM,YAAY;YAClB,MAAM,eAAe,UAAU,OAAO,IAAI;YAC1C,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAO,OAAO;gBAAa,CAAC;YAClE,OAAO;QACT;IACF;IAEA,kCAAkC;IAClC,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK,CAAC;YAEzD,MAAM,iBAAiB,MAAM,CAAA,GAAA,qOAAA,CAAA,iCAA8B,AAAD,EAAE,qIAAA,CAAA,OAAI,EAAE,OAAO;YACzE,MAAM,OAAO,eAAe,IAAI;YAEhC,kCAAkC;YAClC,IAAI,aAAa;gBACf,MAAM,CAAA,GAAA,oNAAA,CAAA,gBAAa,AAAD,EAAE,MAAM;oBAAE;gBAAY;YAC1C;YAEA,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE;oBAAM,SAAS;gBAAM,CAAC;YACnD,OAAO;QACT,EAAE,OAAO,OAAgB;YACvB,MAAM,YAAY;YAClB,MAAM,eAAe,UAAU,OAAO,IAAI;YAC1C,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAO,OAAO;gBAAa,CAAC;YAClE,OAAO;QACT;IACF;IAEA,WAAW;IACX,MAAM,SAAS;QACb,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK,CAAC;YACzD,MAAM,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE,qIAAA,CAAA,OAAI;YAClB,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;oBAAM,SAAS;gBAAM,CAAC;QAC3D,EAAE,OAAO,OAAgB;YACvB,MAAM,YAAY;YAClB,MAAM,eAAe,UAAU,OAAO,IAAI;YAC1C,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;oBAAO,OAAO;gBAAa,CAAC;QACpE;IACF;IAEA,cAAc;IACd,MAAM,aAAa;QACjB,SAAS,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,OAAO;YAAK,CAAC;IAC5C;IAEA,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;QACA;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1225, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/hooks/useFCM.ts"], "sourcesContent": ["/**\r\n * React Hook for Firebase Cloud Messaging\r\n * Handles FCM token generation, permission requests, and notification management\r\n */\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { MessagePayload } from 'firebase/messaging';\r\nimport FCMService from '@/lib/fcm';\r\nimport { useAuth } from '@/hooks/useAuth';\r\n\r\nexport interface FCMState {\r\n  token: string | null;\r\n  permission: NotificationPermission;\r\n  isSupported: boolean;\r\n  isLoading: boolean;\r\n  error: string | null;\r\n}\r\n\r\nexport interface FCMActions {\r\n  requestPermission: () => Promise<boolean>;\r\n  generateToken: () => Promise<string | null>;\r\n  setupForegroundListener: (callback: (payload: MessagePayload) => void) => void;\r\n}\r\n\r\n/**\r\n * Custom hook for Firebase Cloud Messaging\r\n */\r\nexport const useFCM = (): FCMState & FCMActions => {\r\n  const { user } = useAuth();\r\n  const [state, setState] = useState<FCMState>({\r\n    token: null,\r\n    permission: 'default',\r\n    isSupported: false,\r\n    isLoading: true,\r\n    error: null,\r\n  });\r\n\r\n  // Initialize FCM\r\n  useEffect(() => {\r\n    const initializeFCM = async () => {\r\n      try {\r\n        setState(prev => ({ ...prev, isLoading: true, error: null }));\r\n\r\n        // Check if FCM is supported\r\n        const isSupported = await FCMService.initialize();\r\n        \r\n        // Get current permission status\r\n        const permission = 'Notification' in window ? Notification.permission : 'denied';\r\n\r\n        setState(prev => ({\r\n          ...prev,\r\n          isSupported,\r\n          permission,\r\n          isLoading: false,\r\n        }));\r\n\r\n        // If user is logged in and permission is granted, try to get existing token\r\n        if (user && permission === 'granted') {\r\n          const existingToken = await FCMService.getStoredToken(user.uid);\r\n          if (existingToken) {\r\n            setState(prev => ({ ...prev, token: existingToken }));\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Error initializing FCM:', error);\r\n        setState(prev => ({\r\n          ...prev,\r\n          isLoading: false,\r\n          error: error instanceof Error ? error.message : 'Failed to initialize FCM',\r\n        }));\r\n      }\r\n    };\r\n\r\n    initializeFCM();\r\n  }, [user]);\r\n\r\n  // Request notification permission\r\n  const requestPermission = useCallback(async (): Promise<boolean> => {\r\n    try {\r\n      setState(prev => ({ ...prev, isLoading: true, error: null }));\r\n\r\n      const permission = await FCMService.requestPermission();\r\n      \r\n      setState(prev => ({ ...prev, permission, isLoading: false }));\r\n\r\n      return permission === 'granted';\r\n    } catch (error) {\r\n      console.error('Error requesting permission:', error);\r\n      setState(prev => ({\r\n        ...prev,\r\n        isLoading: false,\r\n        error: error instanceof Error ? error.message : 'Failed to request permission',\r\n      }));\r\n      return false;\r\n    }\r\n  }, []);\r\n\r\n  // Generate FCM token\r\n  const generateToken = useCallback(async (): Promise<string | null> => {\r\n    // For testing, we'll use a mock user ID if no user is authenticated\r\n    const userId = user?.uid || 'test_user_123';\r\n\r\n    try {\r\n      setState(prev => ({ ...prev, isLoading: true, error: null }));\r\n\r\n      const token = await FCMService.generateToken(userId);\r\n      \r\n      setState(prev => ({ ...prev, token, isLoading: false }));\r\n\r\n      // Store token locally for quick access\r\n      if (token) {\r\n        localStorage.setItem(`fcm_token_${userId}`, token);\r\n      }\r\n\r\n      return token;\r\n    } catch (error) {\r\n      console.error('Error generating token:', error);\r\n      setState(prev => ({\r\n        ...prev,\r\n        isLoading: false,\r\n        error: error instanceof Error ? error.message : 'Failed to generate token',\r\n      }));\r\n      return null;\r\n    }\r\n  }, [user]);\r\n\r\n  // Setup foreground message listener\r\n  const setupForegroundListener = useCallback((callback: (payload: MessagePayload) => void) => {\r\n    FCMService.setupForegroundListener(callback);\r\n  }, []);\r\n\r\n  return {\r\n    ...state,\r\n    requestPermission,\r\n    generateToken,\r\n    setupForegroundListener,\r\n  };\r\n};\r\n\r\n/**\r\n * Hook for notification display\r\n */\r\nexport const useNotifications = () => {\r\n  const [notifications, setNotifications] = useState<MessagePayload[]>([]);\r\n\r\n  const addNotification = useCallback((payload: MessagePayload) => {\r\n    setNotifications(prev => [payload, ...prev.slice(0, 9)]); // Keep last 10 notifications\r\n  }, []);\r\n\r\n  const removeNotification = useCallback((index: number) => {\r\n    setNotifications(prev => prev.filter((_, i) => i !== index));\r\n  }, []);\r\n\r\n  const clearNotifications = useCallback(() => {\r\n    setNotifications([]);\r\n  }, []);\r\n\r\n  return {\r\n    notifications,\r\n    addNotification,\r\n    removeNotification,\r\n    clearNotifications,\r\n  };\r\n};\r\n\r\nexport default useFCM;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AAEA;AACA;;;;AAmBO,MAAM,SAAS;IACpB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAY;QAC3C,OAAO;QACP,YAAY;QACZ,aAAa;QACb,WAAW;QACX,OAAO;IACT;IAEA,iBAAiB;IACjB,CAAA,GAAA,oNAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,SAAS,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,WAAW;wBAAM,OAAO;oBAAK,CAAC;gBAE3D,4BAA4B;gBAC5B,MAAM,cAAc,MAAM,gIAAA,CAAA,UAAU,CAAC,UAAU;gBAE/C,gCAAgC;gBAChC,MAAM,aAAa,kBAAkB,SAAS,aAAa,UAAU,GAAG;gBAExE,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP;wBACA;wBACA,WAAW;oBACb,CAAC;gBAED,4EAA4E;gBAC5E,IAAI,QAAQ,eAAe,WAAW;oBACpC,MAAM,gBAAgB,MAAM,gIAAA,CAAA,UAAU,CAAC,cAAc,CAAC,KAAK,GAAG;oBAC9D,IAAI,eAAe;wBACjB,SAAS,CAAA,OAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE,OAAO;4BAAc,CAAC;oBACrD;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,SAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,WAAW;wBACX,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAClD,CAAC;YACH;QACF;QAEA;IACF,GAAG;QAAC;KAAK;IAET,kCAAkC;IAClC,MAAM,oBAAoB,CAAA,GAAA,oNAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK,CAAC;YAE3D,MAAM,aAAa,MAAM,gIAAA,CAAA,UAAU,CAAC,iBAAiB;YAErD,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE;oBAAY,WAAW;gBAAM,CAAC;YAE3D,OAAO,eAAe;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,WAAW;oBACX,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;YACD,OAAO;QACT;IACF,GAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,gBAAgB,CAAA,GAAA,oNAAA,CAAA,cAAW,AAAD,EAAE;QAChC,oEAAoE;QACpE,MAAM,SAAS,MAAM,OAAO;QAE5B,IAAI;YACF,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,WAAW;oBAAM,OAAO;gBAAK,CAAC;YAE3D,MAAM,QAAQ,MAAM,gIAAA,CAAA,UAAU,CAAC,aAAa,CAAC;YAE7C,SAAS,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE;oBAAO,WAAW;gBAAM,CAAC;YAEtD,uCAAuC;YACvC,IAAI,OAAO;gBACT,aAAa,OAAO,CAAC,CAAC,UAAU,EAAE,QAAQ,EAAE;YAC9C;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS,CAAA,OAAQ,CAAC;oBAChB,GAAG,IAAI;oBACP,WAAW;oBACX,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;YACD,OAAO;QACT;IACF,GAAG;QAAC;KAAK;IAET,oCAAoC;IACpC,MAAM,0BAA0B,CAAA,GAAA,oNAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3C,gIAAA,CAAA,UAAU,CAAC,uBAAuB,CAAC;IACrC,GAAG,EAAE;IAEL,OAAO;QACL,GAAG,KAAK;QACR;QACA;QACA;IACF;AACF;AAKO,MAAM,mBAAmB;IAC9B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAEvE,MAAM,kBAAkB,CAAA,GAAA,oNAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,iBAAiB,CAAA,OAAQ;gBAAC;mBAAY,KAAK,KAAK,CAAC,GAAG;aAAG,GAAG,6BAA6B;IACzF,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,oNAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACvD,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,oNAAA,CAAA,cAAW,AAAD,EAAE;QACrC,iBAAiB,EAAE;IACrB,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/NotificationBell.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { BellIcon } from '@heroicons/react/24/outline';\r\nimport { BellIcon as BellSolidIcon } from '@heroicons/react/24/solid';\r\nimport { MessagePayload } from 'firebase/messaging';\r\nimport { useFCM, useNotifications } from '@/hooks/useFCM';\r\n\r\ninterface NotificationBellProps {\r\n  className?: string;\r\n  iconSize?: string;\r\n  textColor?: string;\r\n  hoverColor?: string;\r\n}\r\n\r\nexport default function NotificationBell({\r\n  className = '',\r\n  iconSize = 'h-6 w-6',\r\n  textColor = 'text-gray-600',\r\n  hoverColor = 'hover:text-gray-900'\r\n}: NotificationBellProps) {\r\n  const router = useRouter();\r\n  // const { user } = useAuth(); // Commented out as not currently used\r\n  const { token, permission, generateToken, setupForegroundListener } = useFCM();\r\n  const { notifications, addNotification } = useNotifications();\r\n  const [hasNewNotifications, setHasNewNotifications] = useState(false);\r\n\r\n  // Setup FCM when permission is granted\r\n  useEffect(() => {\r\n    if (permission === 'granted' && !token) {\r\n      generateToken();\r\n    }\r\n  }, [permission, token, generateToken]);\r\n\r\n  // Setup foreground listener\r\n  useEffect(() => {\r\n    if (token) {\r\n      setupForegroundListener((payload: MessagePayload) => {\r\n        console.log('Foreground notification received:', payload);\r\n        addNotification(payload);\r\n        setHasNewNotifications(true);\r\n        \r\n        // Show browser notification\r\n        if (permission === 'granted' && 'Notification' in window) {\r\n          new Notification(payload.notification?.title || 'Tap2Go', {\r\n            body: payload.notification?.body || 'You have a new notification',\r\n            icon: '/favicon.ico',\r\n            tag: 'tap2go-notification',\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }, [token, permission, setupForegroundListener, addNotification]);\r\n\r\n  const handleBellClick = () => {\r\n    // Reset new notification indicator when navigating to notifications page\r\n    setHasNewNotifications(false);\r\n    // Navigate to notifications page\r\n    router.push('/notifications');\r\n  };\r\n\r\n  // For testing, we'll show the notification bell even without authentication\r\n  // In production, you might want to require authentication\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      {/* Notification Bell Button */}\r\n      <button\r\n        onClick={handleBellClick}\r\n        className={`relative p-2 ${textColor} ${hoverColor} focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 rounded-full ${className}`}\r\n      >\r\n        {hasNewNotifications || notifications.length > 0 ? (\r\n          <BellSolidIcon className={`${iconSize} text-orange-500`} />\r\n        ) : (\r\n          <BellIcon className={iconSize} />\r\n        )}\r\n\r\n        {/* Notification Count Badge */}\r\n        {notifications.length > 0 && (\r\n          <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\">\r\n            {notifications.length > 9 ? '9+' : notifications.length}\r\n          </span>\r\n        )}\r\n\r\n        {/* New Notification Indicator */}\r\n        {hasNewNotifications && (\r\n          <span className=\"absolute top-0 right-0 block h-2 w-2 bg-red-400 rounded-full animate-pulse\"></span>\r\n        )}\r\n      </button>\r\n\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;AAPA;;;;;;;AAgBe,SAAS,iBAAiB,EACvC,YAAY,EAAE,EACd,WAAW,SAAS,EACpB,YAAY,eAAe,EAC3B,aAAa,qBAAqB,EACZ;IACtB,MAAM,SAAS,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD;IACvB,qEAAqE;IACrE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,uBAAuB,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,SAAM,AAAD;IAC3E,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,mBAAgB,AAAD;IAC1D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,uCAAuC;IACvC,CAAA,GAAA,oNAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,aAAa,CAAC,OAAO;YACtC;QACF;IACF,GAAG;QAAC;QAAY;QAAO;KAAc;IAErC,4BAA4B;IAC5B,CAAA,GAAA,oNAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,OAAO;YACT,wBAAwB,CAAC;gBACvB,QAAQ,GAAG,CAAC,qCAAqC;gBACjD,gBAAgB;gBAChB,uBAAuB;gBAEvB,4BAA4B;gBAC5B,IAAI,eAAe,aAAa,kBAAkB,QAAQ;oBACxD,IAAI,aAAa,QAAQ,YAAY,EAAE,SAAS,UAAU;wBACxD,MAAM,QAAQ,YAAY,EAAE,QAAQ;wBACpC,MAAM;wBACN,KAAK;oBACP;gBACF;YACF;QACF;IACF,GAAG;QAAC;QAAO;QAAY;QAAyB;KAAgB;IAEhE,MAAM,kBAAkB;QACtB,yEAAyE;QACzE,uBAAuB;QACvB,iCAAiC;QACjC,OAAO,IAAI,CAAC;IACd;IAEA,4EAA4E;IAC5E,0DAA0D;IAE1D,qBACE,6PAAC;QAAI,WAAU;kBAEb,cAAA,6PAAC;YACC,SAAS;YACT,WAAW,CAAC,aAAa,EAAE,UAAU,CAAC,EAAE,WAAW,wFAAwF,EAAE,WAAW;;gBAEvJ,uBAAuB,cAAc,MAAM,GAAG,kBAC7C,6PAAC,4NAAA,CAAA,WAAa;oBAAC,WAAW,GAAG,SAAS,gBAAgB,CAAC;;;;;yCAEvD,6PAAC,8NAAA,CAAA,WAAQ;oBAAC,WAAW;;;;;;gBAItB,cAAc,MAAM,GAAG,mBACtB,6PAAC;oBAAK,WAAU;8BACb,cAAc,MAAM,GAAG,IAAI,OAAO,cAAc,MAAM;;;;;;gBAK1D,qCACC,6PAAC;oBAAK,WAAU;;;;;;;;;;;;;;;;;AAM1B", "debugId": null}}, {"offset": {"line": 1502, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/home/<USER>"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useSSRSafeAuthState } from '@/hooks/useSSRSafeAuth';\r\nimport { useCart } from '@/contexts/CartContext';\r\nimport {\r\n  BellIcon,\r\n  UserIcon,\r\n  Bars3Icon,\r\n  HeartIcon,\r\n  MapPinIcon,\r\n  ShoppingCartIcon,\r\n  ChevronLeftIcon,\r\n  ChevronRightIcon,\r\n} from '@heroicons/react/24/outline';\r\nimport Link from 'next/link';\r\nimport HeaderSearch from '@/components/search/HeaderSearch';\r\nimport NotificationBell from '@/components/NotificationBell';\r\n\r\ninterface HomeHeaderProps {\r\n  onMenuClick: () => void;\r\n  sidebarCollapsed?: boolean;\r\n  onToggleCollapse?: () => void;\r\n}\r\n\r\nexport default function HomeHeader({ onMenuClick, sidebarCollapsed = false, onToggleCollapse }: HomeHeaderProps) {\r\n  const { signOut } = useAuth();\r\n  const { canShowUserContent, canShowGuestContent, shouldWaitForAuth } = useSSRSafeAuthState();\r\n  const { cart } = useCart();\r\n  const [showUserMenu, setShowUserMenu] = useState(false);\r\n  const currentLocation = 'Manila';\r\n\r\n  const handleSignOut = async () => {\r\n    try {\r\n      await signOut();\r\n    } catch (error) {\r\n      console.error('Error signing out:', error);\r\n    }\r\n  };\r\n\r\n  const cartItemsCount = cart?.items.reduce((total, item) => total + item.quantity, 0) || 0;\r\n\r\n  return (\r\n    <header className=\"bg-white shadow-sm border-b border-gray-200 fixed top-0 right-0 left-0 z-40\">\r\n      {/* Mobile/Tablet Header (767px and below) */}\r\n      <div className=\"md:hidden\" style={{ background: 'linear-gradient(to right, #f3a823, #ef7b06)' }}>\r\n        {/* Top Row - Logo, Location, Wishlist, Notifications */}\r\n        <div className=\"flex items-center justify-between px-4 py-3\">\r\n          {/* Left Side - Logo and Location */}\r\n          <div className=\"flex items-center space-x-3 text-white\">\r\n            {/* Logo */}\r\n            <Link href=\"/\" className=\"flex items-center\">\r\n              <div className=\"w-8 h-8 bg-white rounded-lg flex items-center justify-center\">\r\n                <span className=\"font-bold text-lg\" style={{ color: '#f3a823' }}>T</span>\r\n              </div>\r\n            </Link>\r\n\r\n            {/* Location */}\r\n            <div className=\"flex items-center space-x-2\">\r\n              <MapPinIcon className=\"h-6 w-6\" />\r\n              <div>\r\n                <div className=\"text-base font-semibold\">Ayala Blvd</div>\r\n                <div className=\"text-sm opacity-90\">{currentLocation}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Right Icons */}\r\n          <div className=\"flex items-center\">\r\n            {/* Wishlist */}\r\n            <Link href=\"/wishlist\" className=\"p-2\">\r\n              <HeartIcon className=\"h-6 w-6 text-white\" />\r\n            </Link>\r\n\r\n            {/* Notifications */}\r\n            <NotificationBell\r\n              iconSize=\"h-6 w-6\"\r\n              textColor=\"text-white\"\r\n              hoverColor=\"hover:text-orange-200\"\r\n              className=\"p-0\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Search Bar */}\r\n        <div className=\"px-4 pb-4\">\r\n          <HeaderSearch\r\n            placeholder=\"Pizza Hut 50% OFF Flash Sale!\"\r\n            isMobile={true}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Desktop Header (768px and above) */}\r\n      <div className=\"hidden md:block\">\r\n        <div className=\"flex min-h-16\">\r\n          {/* FIRST DIVISION: Logo + Tap2Go + Chevron (fixed width) */}\r\n          <div className=\"flex items-center justify-between px-4 lg:px-6 w-64\">\r\n            {/* Logo and Tap2Go */}\r\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\r\n              <div className=\"w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center\">\r\n                <span className=\"text-white font-bold text-lg\">T</span>\r\n              </div>\r\n              <span className=\"text-xl font-bold text-gray-900\">Tap2Go</span>\r\n            </Link>\r\n\r\n            {/* Desktop Sidebar Collapse/Expand Button */}\r\n            {onToggleCollapse && (\r\n              <button\r\n                onClick={onToggleCollapse}\r\n                className=\"hidden lg:flex p-1.5 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors\"\r\n                title={sidebarCollapsed ? \"Expand sidebar\" : \"Collapse sidebar\"}\r\n              >\r\n                {sidebarCollapsed ? (\r\n                  <ChevronRightIcon className=\"h-5 w-5\" />\r\n                ) : (\r\n                  <ChevronLeftIcon className=\"h-5 w-5\" />\r\n                )}\r\n              </button>\r\n            )}\r\n          </div>\r\n\r\n          {/* SECOND DIVISION: Search + Right Icons */}\r\n          <div className=\"flex-1 flex items-center justify-between px-4\">\r\n            {/* Left side - Mobile menu button and Search */}\r\n            <div className=\"flex items-center flex-1\">\r\n              {/* Mobile menu button */}\r\n              <button\r\n                onClick={onMenuClick}\r\n                className=\"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 mr-4\"\r\n              >\r\n                <Bars3Icon className=\"h-6 w-6\" />\r\n              </button>\r\n\r\n              {/* Search Bar - Desktop */}\r\n              <div className=\"flex-1 max-w-lg\">\r\n                <HeaderSearch\r\n                  placeholder=\"Search restaurants, cuisines, or dishes...\"\r\n                  isMobile={false}\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            {/* Right side - Navigation and User Menu */}\r\n            <div className=\"flex items-center space-x-2 lg:space-x-4\">\r\n              {canShowUserContent ? (\r\n                <>\r\n                  {/* Wishlist */}\r\n                  <Link href=\"/wishlist\" className=\"relative\">\r\n                    <HeartIcon className=\"h-6 w-6 text-gray-400 hover:text-gray-500 transition-colors\" />\r\n                  </Link>\r\n\r\n                  {/* Cart */}\r\n                  <Link href=\"/cart\" className=\"relative\">\r\n                    <ShoppingCartIcon className=\"h-6 w-6 text-gray-400 hover:text-gray-500 transition-colors\" />\r\n                    {cartItemsCount > 0 && (\r\n                      <span className=\"absolute -top-2 -right-2 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center\" style={{ backgroundColor: '#ef7b06' }}>\r\n                        {cartItemsCount}\r\n                      </span>\r\n                    )}\r\n                  </Link>\r\n\r\n                  {/* Notifications */}\r\n                  <button className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 rounded-full\">\r\n                    <BellIcon className=\"h-6 w-6\" />\r\n                    <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white\"></span>\r\n                  </button>\r\n\r\n                  {/* User Menu */}\r\n                  <div className=\"relative\">\r\n                    <button\r\n                      onClick={() => setShowUserMenu(!showUserMenu)}\r\n                      className=\"w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-colors\"\r\n                    >\r\n                      <UserIcon className=\"h-5 w-5 text-white\" />\r\n                    </button>\r\n\r\n                    {showUserMenu && (\r\n                      <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border border-gray-200\">\r\n                        <Link href=\"/profile\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\r\n                          Profile\r\n                        </Link>\r\n                        <button\r\n                          onClick={handleSignOut}\r\n                          className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\r\n                        >\r\n                          Sign Out\r\n                        </button>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </>\r\n              ) : canShowGuestContent ? (\r\n                <>\r\n                  <Link href=\"/auth/signin\" className=\"text-gray-600 hover:text-gray-900 transition-colors\">\r\n                    Sign In\r\n                  </Link>\r\n                  <Link href=\"/auth/signup\" className=\"bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded-lg transition-colors duration-200\">\r\n                    Sign Up\r\n                  </Link>\r\n                </>\r\n              ) : shouldWaitForAuth ? (\r\n                // PROFESSIONAL: Show consistent loading state to prevent layout shifts\r\n                <div className=\"flex items-center space-x-6\">\r\n                  <div className=\"h-6 w-16 bg-gray-200 rounded animate-pulse\"></div>\r\n                  <div className=\"h-8 w-20 bg-gray-200 rounded-lg animate-pulse\"></div>\r\n                </div>\r\n              ) : (\r\n                // Fallback - should rarely be seen\r\n                <div className=\"flex items-center space-x-6\">\r\n                  <div className=\"h-4 w-16 bg-gray-200 rounded animate-pulse\"></div>\r\n                  <div className=\"h-4 w-20 bg-gray-200 rounded animate-pulse\"></div>\r\n                </div>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AAlBA;;;;;;;;;;AA0Be,SAAS,WAAW,EAAE,WAAW,EAAE,mBAAmB,KAAK,EAAE,gBAAgB,EAAmB;IAC7G,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,sBAAmB,AAAD;IACzF,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,kBAAkB;IAExB,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,iBAAiB,MAAM,MAAM,OAAO,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE,MAAM;IAExF,qBACE,6PAAC;QAAO,WAAU;;0BAEhB,6PAAC;gBAAI,WAAU;gBAAY,OAAO;oBAAE,YAAY;gBAA8C;;kCAE5F,6PAAC;wBAAI,WAAU;;0CAEb,6PAAC;gCAAI,WAAU;;kDAEb,6PAAC,2KAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,cAAA,6PAAC;4CAAI,WAAU;sDACb,cAAA,6PAAC;gDAAK,WAAU;gDAAoB,OAAO;oDAAE,OAAO;gDAAU;0DAAG;;;;;;;;;;;;;;;;kDAKrE,6PAAC;wCAAI,WAAU;;0DACb,6PAAC,kOAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,6PAAC;;kEACC,6PAAC;wDAAI,WAAU;kEAA0B;;;;;;kEACzC,6PAAC;wDAAI,WAAU;kEAAsB;;;;;;;;;;;;;;;;;;;;;;;;0CAM3C,6PAAC;gCAAI,WAAU;;kDAEb,6PAAC,2KAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAC/B,cAAA,6PAAC,gOAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAIvB,6PAAC,qJAAA,CAAA,UAAgB;wCACf,UAAS;wCACT,WAAU;wCACV,YAAW;wCACX,WAAU;;;;;;;;;;;;;;;;;;kCAMhB,6PAAC;wBAAI,WAAU;kCACb,cAAA,6PAAC,2JAAA,CAAA,UAAY;4BACX,aAAY;4BACZ,UAAU;;;;;;;;;;;;;;;;;0BAMhB,6PAAC;gBAAI,WAAU;0BACb,cAAA,6PAAC;oBAAI,WAAU;;sCAEb,6PAAC;4BAAI,WAAU;;8CAEb,6PAAC,2KAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,6PAAC;4CAAI,WAAU;sDACb,cAAA,6PAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6PAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;gCAInD,kCACC,6PAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAO,mBAAmB,mBAAmB;8CAE5C,iCACC,6PAAC,8OAAA,CAAA,mBAAgB;wCAAC,WAAU;;;;;6DAE5B,6PAAC,4OAAA,CAAA,kBAAe;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAOnC,6PAAC;4BAAI,WAAU;;8CAEb,6PAAC;oCAAI,WAAU;;sDAEb,6PAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,6PAAC,gOAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAIvB,6PAAC;4CAAI,WAAU;sDACb,cAAA,6PAAC,2JAAA,CAAA,UAAY;gDACX,aAAY;gDACZ,UAAU;;;;;;;;;;;;;;;;;8CAMhB,6PAAC;oCAAI,WAAU;8CACZ,mCACC;;0DAEE,6PAAC,2KAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAC/B,cAAA,6PAAC,gOAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAIvB,6PAAC,2KAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;;kEAC3B,6PAAC,8OAAA,CAAA,mBAAgB;wDAAC,WAAU;;;;;;oDAC3B,iBAAiB,mBAChB,6PAAC;wDAAK,WAAU;wDAAoG,OAAO;4DAAE,iBAAiB;wDAAU;kEACrJ;;;;;;;;;;;;0DAMP,6PAAC;gDAAO,WAAU;;kEAChB,6PAAC,8NAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6PAAC;wDAAK,WAAU;;;;;;;;;;;;0DAIlB,6PAAC;gDAAI,WAAU;;kEACb,6PAAC;wDACC,SAAS,IAAM,gBAAgB,CAAC;wDAChC,WAAU;kEAEV,cAAA,6PAAC,8NAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;oDAGrB,8BACC,6PAAC;wDAAI,WAAU;;0EACb,6PAAC,2KAAA,CAAA,UAAI;gEAAC,MAAK;gEAAW,WAAU;0EAA0D;;;;;;0EAG1F,6PAAC;gEACC,SAAS;gEACT,WAAU;0EACX;;;;;;;;;;;;;;;;;;;uDAOP,oCACF;;0DACE,6PAAC,2KAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,WAAU;0DAAsD;;;;;;0DAG1F,6PAAC,2KAAA,CAAA,UAAI;gDAAC,MAAK;gDAAe,WAAU;0DAAiH;;;;;;;uDAIrJ,oBACF,uEAAuE;kDACvE,6PAAC;wCAAI,WAAU;;0DACb,6PAAC;gDAAI,WAAU;;;;;;0DACf,6PAAC;gDAAI,WAAU;;;;;;;;;;;+CAGjB,mCAAmC;kDACnC,6PAAC;wCAAI,WAAU;;0DACb,6PAAC;gDAAI,WAAU;;;;;;0DACf,6PAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjC", "debugId": null}}, {"offset": {"line": 2029, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/MobileFooterNav.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport { useSSRSafeAuthState } from '@/hooks/useSSRSafeAuth';\r\nimport { useCart } from '@/contexts/CartContext';\r\nimport {\r\n  HomeIcon,\r\n  BuildingStorefrontIcon,\r\n  MagnifyingGlassIcon,\r\n  UserIcon,\r\n  ShoppingCartIcon,\r\n} from '@heroicons/react/24/outline';\r\nimport {\r\n  HomeIcon as HomeIconSolid,\r\n  BuildingStorefrontIcon as BuildingStorefrontIconSolid,\r\n  MagnifyingGlassIcon as MagnifyingGlassIconSolid,\r\n  UserIcon as UserIconSolid,\r\n  ShoppingCartIcon as ShoppingCartIconSolid,\r\n} from '@heroicons/react/24/solid';\r\n\r\nexport default function MobileFooterNav() {\r\n  const pathname = usePathname();\r\n  const { user, shouldWaitForAuth } = useSSRSafeAuthState();\r\n  const { cart } = useCart();\r\n\r\n  // Don't show footer nav on admin, vendor, driver dashboard, or auth pages\r\n  if (!pathname ||\r\n      pathname.startsWith('/admin') ||\r\n      pathname.startsWith('/vendor/') ||\r\n      pathname === '/vendor' ||\r\n      pathname.startsWith('/driver/') ||\r\n      pathname === '/driver' ||\r\n      pathname.startsWith('/auth') ||\r\n      pathname.startsWith('/test-')) {\r\n    return null;\r\n  }\r\n\r\n  // Don't show for non-customer users when they're on role-specific pages\r\n  if (user && user.role !== 'customer' && user.role !== 'admin') {\r\n    return null;\r\n  }\r\n\r\n  const getAccountHref = () => {\r\n    if (shouldWaitForAuth) {\r\n      return '/account'; // SSR-safe default while loading\r\n    }\r\n    if (!user) {\r\n      return '/auth/signin';\r\n    }\r\n    return '/account';\r\n  };\r\n\r\n  const isActive = (href: string) => {\r\n    if (href === '/home') {\r\n      // Consider home active when on home page, root page, or viewing a specific restaurant\r\n      return pathname === '/home' || pathname === '/' || (pathname.startsWith('/restaurant/') && !pathname.startsWith('/restaurants'));\r\n    }\r\n    if (href === '/restaurants') {\r\n      return pathname === '/restaurants';\r\n    }\r\n    if (href === '/search') {\r\n      return pathname === '/search';\r\n    }\r\n    if (href === '/profile') {\r\n      return pathname === '/account' || pathname === '/profile' || pathname === '/orders' || (!user && pathname === '/auth/signin');\r\n    }\r\n    if (href === '/cart') {\r\n      return pathname === '/cart';\r\n    }\r\n    return pathname.startsWith(href);\r\n  };\r\n\r\n  const cartItemsCount = cart?.items.reduce((total, item) => total + item.quantity, 0) || 0;\r\n\r\n  return (\r\n    <nav className=\"md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50\">\r\n      <div className=\"grid grid-cols-5 h-14 relative\">\r\n        {/* Home */}\r\n        <Link\r\n          href=\"/home\"\r\n          className={`flex flex-col items-center justify-center space-y-0.5 transition-colors ${\r\n            isActive('/home') ? 'text-gray-500 hover:text-gray-700' : 'text-gray-500 hover:text-gray-700'\r\n          }`}\r\n        >\r\n          {isActive('/home') ? (\r\n            <HomeIconSolid className=\"h-5 w-5\" style={{ color: '#f3a823' }} />\r\n          ) : (\r\n            <HomeIcon className=\"h-5 w-5\" />\r\n          )}\r\n          <span\r\n            className=\"text-[10px] font-medium\"\r\n            style={isActive('/home') ? { color: '#f3a823' } : {}}\r\n          >\r\n            Home\r\n          </span>\r\n        </Link>\r\n\r\n        {/* Stores */}\r\n        <Link\r\n          href=\"/restaurants\"\r\n          className={`flex flex-col items-center justify-center space-y-0.5 transition-colors ${\r\n            isActive('/restaurants') ? 'text-gray-500 hover:text-gray-700' : 'text-gray-500 hover:text-gray-700'\r\n          }`}\r\n        >\r\n          {isActive('/restaurants') ? (\r\n            <BuildingStorefrontIconSolid className=\"h-5 w-5\" style={{ color: '#f3a823' }} />\r\n          ) : (\r\n            <BuildingStorefrontIcon className=\"h-5 w-5\" />\r\n          )}\r\n          <span\r\n            className=\"text-[10px] font-medium\"\r\n            style={isActive('/restaurants') ? { color: '#f3a823' } : {}}\r\n          >\r\n            Stores\r\n          </span>\r\n        </Link>\r\n\r\n        {/* Add to Cart - Elevated */}\r\n        <Link\r\n          href=\"/cart\"\r\n          className={`flex flex-col items-center justify-center space-y-0.5 transition-colors relative -top-2 ${\r\n            isActive('/cart') ? 'text-gray-800' : 'text-gray-600'\r\n          }`}\r\n        >\r\n          <div\r\n            className=\"w-10 h-10 bg-white rounded-full flex items-center justify-center relative\"\r\n            style={{\r\n              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1)'\r\n            }}\r\n          >\r\n            {isActive('/cart') ? (\r\n              <ShoppingCartIconSolid className=\"h-5 w-5 text-gray-800\" />\r\n            ) : (\r\n              <ShoppingCartIcon className=\"h-5 w-5 text-gray-600\" />\r\n            )}\r\n            {cartItemsCount > 0 && (\r\n              <span\r\n                className=\"absolute -top-1 -right-1 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium\"\r\n                style={{ backgroundColor: '#f3a823' }}\r\n              >\r\n                {cartItemsCount}\r\n              </span>\r\n            )}\r\n          </div>\r\n          <span className=\"text-[10px] font-medium text-gray-500\">Cart</span>\r\n        </Link>\r\n\r\n        {/* Search */}\r\n        <Link\r\n          href=\"/search\"\r\n          className={`flex flex-col items-center justify-center space-y-0.5 transition-colors ${\r\n            isActive('/search') ? 'text-gray-500 hover:text-gray-700' : 'text-gray-500 hover:text-gray-700'\r\n          }`}\r\n        >\r\n          {isActive('/search') ? (\r\n            <MagnifyingGlassIconSolid className=\"h-5 w-5\" style={{ color: '#f3a823' }} />\r\n          ) : (\r\n            <MagnifyingGlassIcon className=\"h-5 w-5\" />\r\n          )}\r\n          <span\r\n            className=\"text-[10px] font-medium\"\r\n            style={isActive('/search') ? { color: '#f3a823' } : {}}\r\n          >\r\n            Search\r\n          </span>\r\n        </Link>\r\n\r\n        {/* Account */}\r\n        <Link\r\n          href={getAccountHref()}\r\n          className={`flex flex-col items-center justify-center space-y-0.5 transition-colors ${\r\n            isActive('/profile') ? 'text-gray-500 hover:text-gray-700' : 'text-gray-500 hover:text-gray-700'\r\n          }`}\r\n        >\r\n          {isActive('/profile') ? (\r\n            <UserIconSolid className=\"h-5 w-5\" style={{ color: '#f3a823' }} />\r\n          ) : (\r\n            <UserIcon className=\"h-5 w-5\" />\r\n          )}\r\n          <span\r\n            className=\"text-[10px] font-medium\"\r\n            style={isActive('/profile') ? { color: '#f3a823' } : {}}\r\n          >\r\n            Account\r\n          </span>\r\n        </Link>\r\n      </div>\r\n    </nav>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAAA;AAAA;AAAA;AAAA;AAdA;;;;;;;;AAsBe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,iJAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,sBAAmB,AAAD;IACtD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;IAEvB,0EAA0E;IAC1E,IAAI,CAAC,YACD,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,eACpB,aAAa,aACb,SAAS,UAAU,CAAC,eACpB,aAAa,aACb,SAAS,UAAU,CAAC,YACpB,SAAS,UAAU,CAAC,WAAW;QACjC,OAAO;IACT;IAEA,wEAAwE;IACxE,IAAI,QAAQ,KAAK,IAAI,KAAK,cAAc,KAAK,IAAI,KAAK,SAAS;QAC7D,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,IAAI,mBAAmB;YACrB,OAAO,YAAY,iCAAiC;QACtD;QACA,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,SAAS;YACpB,sFAAsF;YACtF,OAAO,aAAa,WAAW,aAAa,OAAQ,SAAS,UAAU,CAAC,mBAAmB,CAAC,SAAS,UAAU,CAAC;QAClH;QACA,IAAI,SAAS,gBAAgB;YAC3B,OAAO,aAAa;QACtB;QACA,IAAI,SAAS,WAAW;YACtB,OAAO,aAAa;QACtB;QACA,IAAI,SAAS,YAAY;YACvB,OAAO,aAAa,cAAc,aAAa,cAAc,aAAa,aAAc,CAAC,QAAQ,aAAa;QAChH;QACA,IAAI,SAAS,SAAS;YACpB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,MAAM,iBAAiB,MAAM,MAAM,OAAO,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE,MAAM;IAExF,qBACE,6PAAC;QAAI,WAAU;kBACb,cAAA,6PAAC;YAAI,WAAU;;8BAEb,6PAAC,2KAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAW,CAAC,wEAAwE,EAClF,SAAS,WAAW,sCAAsC,qCAC1D;;wBAED,SAAS,yBACR,6PAAC,4NAAA,CAAA,WAAa;4BAAC,WAAU;4BAAU,OAAO;gCAAE,OAAO;4BAAU;;;;;iDAE7D,6PAAC,8NAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCAEtB,6PAAC;4BACC,WAAU;4BACV,OAAO,SAAS,WAAW;gCAAE,OAAO;4BAAU,IAAI,CAAC;sCACpD;;;;;;;;;;;;8BAMH,6PAAC,2KAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAW,CAAC,wEAAwE,EAClF,SAAS,kBAAkB,sCAAsC,qCACjE;;wBAED,SAAS,gCACR,6PAAC,wPAAA,CAAA,yBAA2B;4BAAC,WAAU;4BAAU,OAAO;gCAAE,OAAO;4BAAU;;;;;iDAE3E,6PAAC,0PAAA,CAAA,yBAAsB;4BAAC,WAAU;;;;;;sCAEpC,6PAAC;4BACC,WAAU;4BACV,OAAO,SAAS,kBAAkB;gCAAE,OAAO;4BAAU,IAAI,CAAC;sCAC3D;;;;;;;;;;;;8BAMH,6PAAC,2KAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAW,CAAC,wFAAwF,EAClG,SAAS,WAAW,kBAAkB,iBACtC;;sCAEF,6PAAC;4BACC,WAAU;4BACV,OAAO;gCACL,WAAW;4BACb;;gCAEC,SAAS,yBACR,6PAAC,4OAAA,CAAA,mBAAqB;oCAAC,WAAU;;;;;yDAEjC,6PAAC,8OAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;gCAE7B,iBAAiB,mBAChB,6PAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,iBAAiB;oCAAU;8CAEnC;;;;;;;;;;;;sCAIP,6PAAC;4BAAK,WAAU;sCAAwC;;;;;;;;;;;;8BAI1D,6PAAC,2KAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAW,CAAC,wEAAwE,EAClF,SAAS,aAAa,sCAAsC,qCAC5D;;wBAED,SAAS,2BACR,6PAAC,kPAAA,CAAA,sBAAwB;4BAAC,WAAU;4BAAU,OAAO;gCAAE,OAAO;4BAAU;;;;;iDAExE,6PAAC,oPAAA,CAAA,sBAAmB;4BAAC,WAAU;;;;;;sCAEjC,6PAAC;4BACC,WAAU;4BACV,OAAO,SAAS,aAAa;gCAAE,OAAO;4BAAU,IAAI,CAAC;sCACtD;;;;;;;;;;;;8BAMH,6PAAC,2KAAA,CAAA,UAAI;oBACH,MAAM;oBACN,WAAW,CAAC,wEAAwE,EAClF,SAAS,cAAc,sCAAsC,qCAC7D;;wBAED,SAAS,4BACR,6PAAC,4NAAA,CAAA,WAAa;4BAAC,WAAU;4BAAU,OAAO;gCAAE,OAAO;4BAAU;;;;;iDAE7D,6PAAC,8NAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCAEtB,6PAAC;4BACC,WAAU;4BACV,OAAO,SAAS,cAAc;gCAAE,OAAO;4BAAU,IAAI,CAAC;sCACvD;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 2321, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/app/%28customer%29/layout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport HomeSidebar from '@/components/home/<USER>';\r\nimport HomeHeader from '@/components/home/<USER>';\r\nimport MobileFooterNav from '@/components/MobileFooterNav';\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport default function CustomerLayout({ children }: CustomerLayoutProps) {\r\n  const router = useRouter();\r\n  const [sidebarOpen, setSidebarOpen] = useState(false);\r\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\r\n\r\n  // Handle expand sidebar and navigate to specific page\r\n  const handleExpandAndNavigate = (href: string, categoryName: string) => {\r\n    // First expand the sidebar\r\n    setSidebarCollapsed(false);\r\n\r\n    // Then navigate to the specified page\r\n    router.push(href);\r\n\r\n    // Close mobile sidebar if open\r\n    setSidebarOpen(false);\r\n\r\n    // Optional: Add a small delay to show the expansion animation\r\n    setTimeout(() => {\r\n      console.log(`Expanded sidebar and navigated to ${href} from ${categoryName} category`);\r\n    }, 300);\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gray-50\">\r\n      {/* Header - Fixed at top, starts after sidebar on desktop */}\r\n      <HomeHeader\r\n        onMenuClick={() => setSidebarOpen(true)}\r\n        sidebarCollapsed={sidebarCollapsed}\r\n        onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}\r\n      />\r\n\r\n      {/* Sidebar - Fixed on left, starts below header */}\r\n      <HomeSidebar\r\n        isOpen={sidebarOpen}\r\n        onClose={() => setSidebarOpen(false)}\r\n        isCollapsed={sidebarCollapsed}\r\n        onExpandAndNavigate={handleExpandAndNavigate}\r\n      />\r\n\r\n      {/* Main Content - Positioned after header height and sidebar width */}\r\n      <main className={`transition-all duration-300 ${\r\n        sidebarCollapsed ? 'lg:ml-16' : 'lg:ml-64'\r\n      } pt-14 lg:pt-16`}>\r\n        <div className=\"px-3 pt-8 pb-5 lg:p-4\">\r\n          {children}\r\n        </div>\r\n      </main>\r\n\r\n      <MobileFooterNav />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAYe,SAAS,eAAe,EAAE,QAAQ,EAAuB;IACtE,MAAM,SAAS,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,sDAAsD;IACtD,MAAM,0BAA0B,CAAC,MAAc;QAC7C,2BAA2B;QAC3B,oBAAoB;QAEpB,sCAAsC;QACtC,OAAO,IAAI,CAAC;QAEZ,+BAA+B;QAC/B,eAAe;QAEf,8DAA8D;QAC9D,WAAW;YACT,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,KAAK,MAAM,EAAE,aAAa,SAAS,CAAC;QACvF,GAAG;IACL;IAIA,qBACE,6PAAC;QAAI,WAAU;;0BAEb,6PAAC,uJAAA,CAAA,UAAU;gBACT,aAAa,IAAM,eAAe;gBAClC,kBAAkB;gBAClB,kBAAkB,IAAM,oBAAoB,CAAC;;;;;;0BAI/C,6PAAC,wJAAA,CAAA,UAAW;gBACV,QAAQ;gBACR,SAAS,IAAM,eAAe;gBAC9B,aAAa;gBACb,qBAAqB;;;;;;0BAIvB,6PAAC;gBAAK,WAAW,CAAC,4BAA4B,EAC5C,mBAAmB,aAAa,WACjC,eAAe,CAAC;0BACf,cAAA,6PAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;0BAIL,6PAAC,oJAAA,CAAA,UAAe;;;;;;;;;;;AAGtB", "debugId": null}}]}