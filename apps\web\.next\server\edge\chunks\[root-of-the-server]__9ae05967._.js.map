{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/web/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\r\nimport type { NextRequest } from 'next/server';\r\n\r\nexport function middleware(request: NextRequest) {\r\n  const { pathname } = request.nextUrl;\r\n  \r\n  // Check if this is a test route\r\n  if (pathname.startsWith('/tests/') || pathname.startsWith('/test-')) {\r\n    const isDevelopment = process.env.NODE_ENV === 'development';\r\n    const enableTestRoutes = process.env.ENABLE_TEST_ROUTES === 'true';\r\n    \r\n    // Only allow test routes in development with explicit enablement\r\n    if (!isDevelopment || !enableTestRoutes) {\r\n      // Redirect to home page in production or when tests are disabled\r\n      return NextResponse.redirect(new URL('/', request.url));\r\n    }\r\n  }\r\n  \r\n  return NextResponse.next();\r\n}\r\n\r\nexport const config = {\r\n  matcher: [\r\n    '/tests/:path*',\r\n    '/test-:path*',\r\n  ],\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGO,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,gCAAgC;IAChC,IAAI,SAAS,UAAU,CAAC,cAAc,SAAS,UAAU,CAAC,WAAW;QACnE,MAAM,gBAAgB,oDAAyB;QAC/C,MAAM,mBAAmB,QAAQ,GAAG,CAAC,kBAAkB,KAAK;QAE5D,iEAAiE;QACjE,IAAI,CAAC,iBAAiB,CAAC,kBAAkB;YACvC,iEAAiE;YACjE,OAAO,4MAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,QAAQ,GAAG;QACvD;IACF;IAEA,OAAO,4MAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;QACA;KACD;AACH"}}]}