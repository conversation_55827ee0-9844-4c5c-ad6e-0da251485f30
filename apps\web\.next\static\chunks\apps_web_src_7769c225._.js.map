{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/lib/firebase.ts"], "sourcesContent": ["// Import the functions you need from the SDKs you need\r\nimport { initializeApp } from \"firebase/app\";\r\nimport { getAuth } from \"firebase/auth\";\r\nimport { getFirestore } from \"firebase/firestore\";\r\nimport { getStorage } from \"firebase/storage\";\r\nimport { getMessaging, isSupported } from \"firebase/messaging\";\r\n\r\n// Validate environment variables\r\nconst requiredEnvVars = {\r\n  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\r\n  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\r\n  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\r\n  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,\r\n  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\r\n  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID\r\n};\r\n\r\n// Check for missing environment variables\r\nconst missingVars = Object.entries(requiredEnvVars)\r\n  .filter(([, value]) => !value)\r\n  .map(([key]) => key);\r\n\r\nif (missingVars.length > 0) {\r\n  throw new Error(`Missing Firebase environment variables: ${missingVars.join(', ')}`);\r\n}\r\n\r\n// Your web app's Firebase configuration\r\nconst firebaseConfig = {\r\n  apiKey: requiredEnvVars.apiKey!,\r\n  authDomain: requiredEnvVars.authDomain!,\r\n  projectId: requiredEnvVars.projectId!,\r\n  storageBucket: requiredEnvVars.storageBucket!,\r\n  messagingSenderId: requiredEnvVars.messagingSenderId!,\r\n  appId: requiredEnvVars.appId!\r\n};\r\n\r\n// Initialize Firebase\r\nconst app = initializeApp(firebaseConfig);\r\n\r\n// Initialize Firebase services\r\nexport const auth = getAuth(app);\r\nexport const db = getFirestore(app);\r\nexport const storage = getStorage(app);\r\n\r\n// Initialize Firebase Cloud Messaging (only in browser environment)\r\nlet messaging: unknown = null;\r\n\r\n// FCM initialization function\r\nexport const initializeMessaging = async () => {\r\n  if (typeof window !== 'undefined') {\r\n    try {\r\n      const supported = await isSupported();\r\n      if (supported) {\r\n        messaging = getMessaging(app);\r\n        console.log('Firebase Cloud Messaging initialized successfully');\r\n        return messaging;\r\n      } else {\r\n        console.warn('Firebase Cloud Messaging is not supported in this browser');\r\n        return null;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error initializing Firebase Cloud Messaging:', error);\r\n      return null;\r\n    }\r\n  }\r\n  return null;\r\n};\r\n\r\n// Get messaging instance\r\nexport const getMessagingInstance = () => messaging;\r\n\r\nexport { messaging };\r\n\r\nexport default app;\r\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;;;;;;;AAS7C;AARV;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;;;;;;AAEA,iCAAiC;AACjC,MAAM,kBAAkB;IACtB,MAAM;IACN,UAAU;IACV,SAAS;IACT,aAAa;IACb,iBAAiB;IACjB,KAAK;AACP;AAEA,0CAA0C;AAC1C,MAAM,cAAc,OAAO,OAAO,CAAC,iBAChC,MAAM,CAAC,CAAC,GAAG,MAAM,GAAK,CAAC,OACvB,GAAG,CAAC,CAAC,CAAC,IAAI,GAAK;AAElB,IAAI,YAAY,MAAM,GAAG,GAAG;IAC1B,MAAM,IAAI,MAAM,CAAC,wCAAwC,EAAE,YAAY,IAAI,CAAC,OAAO;AACrF;AAEA,wCAAwC;AACxC,MAAM,iBAAiB;IACrB,QAAQ,gBAAgB,MAAM;IAC9B,YAAY,gBAAgB,UAAU;IACtC,WAAW,gBAAgB,SAAS;IACpC,eAAe,gBAAgB,aAAa;IAC5C,mBAAmB,gBAAgB,iBAAiB;IACpD,OAAO,gBAAgB,KAAK;AAC9B;AAEA,sBAAsB;AACtB,MAAM,MAAM,CAAA,GAAA,uLAAA,CAAA,gBAAa,AAAD,EAAE;AAGnB,MAAM,OAAO,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE;AACrB,MAAM,KAAK,CAAA,GAAA,sKAAA,CAAA,eAAY,AAAD,EAAE;AACxB,MAAM,UAAU,CAAA,GAAA,oKAAA,CAAA,aAAU,AAAD,EAAE;AAElC,oEAAoE;AACpE,IAAI,YAAqB;AAGlB,MAAM,sBAAsB;IACjC,wCAAmC;QACjC,IAAI;YACF,MAAM,YAAY,MAAM,CAAA,GAAA,6KAAA,CAAA,cAAW,AAAD;YAClC,IAAI,WAAW;gBACb,YAAY,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;gBACzB,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT,OAAO;gBACL,QAAQ,IAAI,CAAC;gBACb,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,OAAO;QACT;IACF;IACA,OAAO;AACT;AAGO,MAAM,uBAAuB,IAAM;;uCAI3B", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/lib/database/collections.ts"], "sourcesContent": ["// Firebase Firestore Collection Definitions\r\n// This file defines all collection paths and document structures\r\n\r\nexport const COLLECTIONS = {\r\n  // Top-level collections (only implemented ones)\r\n  USERS: 'users',\r\n  ADMINS: 'admins',\r\n  VENDORS: 'vendors',\r\n  CUSTOMERS: 'customers',\r\n  DRIVERS: 'drivers',\r\n  RESTAURANTS: 'restaurants',\r\n  ORDERS: 'orders',\r\n  PLATFORM_CONFIG: 'platformConfig',\r\n  NOTIFICATIONS: 'notifications',\r\n  DISPUTES: 'disputes',\r\n  ANALYTICS: 'analytics',\r\n  CATEGORIES: 'categories',\r\n  SYSTEM_CONFIG: 'systemConfig',\r\n  SYSTEM: '_system',\r\n\r\n  // Subcollections (only implemented ones)\r\n  ADMIN_ACTIONS: 'adminActions',\r\n\r\n  // Vendor subcollections\r\n  VENDOR_DOCUMENTS: 'documents',\r\n  VENDOR_PAYOUTS: 'payouts',\r\n  MODIFIER_GROUPS: 'modifierGroups',\r\n  MASTER_MENU_ITEMS: 'masterMenuItems',\r\n  MASTER_MENU_ASSIGNMENTS: 'masterMenuAssignments',\r\n  VENDOR_AUDIT_LOGS: 'auditLogs',\r\n  VENDOR_ANALYTICS: 'analytics',\r\n\r\n  // Restaurant subcollections\r\n  MENU_CATEGORIES: 'menuCategories',\r\n  MENU_ITEMS: 'menuItems',\r\n  RESTAURANT_PROMOTIONS: 'promotions',\r\n  RESTAURANT_REVIEWS: 'reviews',\r\n\r\n  // Customer subcollections\r\n  CUSTOMER_ADDRESSES: 'addresses',\r\n  CUSTOMER_PAYMENT_METHODS: 'paymentMethods',\r\n  CUSTOMER_FAVORITES: 'favorites',\r\n  CUSTOMER_CART: 'cart',\r\n\r\n  // Driver subcollections\r\n  DRIVER_EARNINGS: 'earnings',\r\n  DRIVER_REVIEWS: 'reviews',\r\n  DRIVER_DELIVERY_HISTORY: 'deliveryHistory'\r\n} as const;\r\n\r\n// Document ID generators\r\nexport const generateDocId = () => {\r\n  return Date.now().toString() + Math.random().toString(36).substring(2, 11);\r\n};\r\n\r\nexport const generateReferralCode = () => {\r\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\r\n  let result = '';\r\n  for (let i = 0; i < 8; i++) {\r\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\r\n  }\r\n  return result;\r\n};\r\n\r\n// Collection path helpers\r\nexport const getCollectionPath = (collection: keyof typeof COLLECTIONS) => {\r\n  return COLLECTIONS[collection];\r\n};\r\n\r\nexport const getSubcollectionPath = (\r\n  parentCollection: keyof typeof COLLECTIONS,\r\n  parentDocId: string,\r\n  subcollection: keyof typeof COLLECTIONS\r\n) => {\r\n  return `${COLLECTIONS[parentCollection]}/${parentDocId}/${COLLECTIONS[subcollection]}`;\r\n};\r\n\r\n// Document reference helpers\r\nexport const getUserRef = (uid: string) => `users/${uid}`;\r\nexport const getAdminRef = (uid: string) => `admins/${uid}`;\r\nexport const getVendorRef = (uid: string) => `vendors/${uid}`;\r\nexport const getCustomerRef = (uid: string) => `customers/${uid}`;\r\nexport const getDriverRef = (uid: string) => `drivers/${uid}`;\r\nexport const getRestaurantRef = (restaurantId: string) => `restaurants/${restaurantId}`;\r\nexport const getOrderRef = (orderId: string) => `orders/${orderId}`;\r\nexport const getPlatformConfigRef = () => `platformConfig/config`;\r\nexport const getNotificationRef = (notificationId: string) => `notifications/${notificationId}`;\r\nexport const getDisputeRef = (disputeId: string) => `disputes/${disputeId}`;\r\nexport const getAnalyticsRef = (analyticsId: string) => `analytics/${analyticsId}`;\r\n\r\n// Validation helpers (only implemented roles)\r\nexport const isValidRole = (role: string): role is 'admin' | 'vendor' | 'customer' | 'driver' => {\r\n  return ['admin', 'vendor', 'customer', 'driver'].includes(role);\r\n};\r\n\r\n\r\n\r\nexport const isValidPaymentStatus = (status: string) => {\r\n  return ['pending', 'paid', 'failed', 'refunded'].includes(status);\r\n};\r\n"], "names": [], "mappings": "AAAA,4CAA4C;AAC5C,iEAAiE;;;;;;;;;;;;;;;;;;;;;AAE1D,MAAM,cAAc;IACzB,gDAAgD;IAChD,OAAO;IACP,QAAQ;IACR,SAAS;IACT,WAAW;IACX,SAAS;IACT,aAAa;IACb,QAAQ;IACR,iBAAiB;IACjB,eAAe;IACf,UAAU;IACV,WAAW;IACX,YAAY;IACZ,eAAe;IACf,QAAQ;IAER,yCAAyC;IACzC,eAAe;IAEf,wBAAwB;IACxB,kBAAkB;IAClB,gBAAgB;IAChB,iBAAiB;IACjB,mBAAmB;IACnB,yBAAyB;IACzB,mBAAmB;IACnB,kBAAkB;IAElB,4BAA4B;IAC5B,iBAAiB;IACjB,YAAY;IACZ,uBAAuB;IACvB,oBAAoB;IAEpB,0BAA0B;IAC1B,oBAAoB;IACpB,0BAA0B;IAC1B,oBAAoB;IACpB,eAAe;IAEf,wBAAwB;IACxB,iBAAiB;IACjB,gBAAgB;IAChB,yBAAyB;AAC3B;AAGO,MAAM,gBAAgB;IAC3B,OAAO,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;AACzE;AAEO,MAAM,uBAAuB;IAClC,MAAM,QAAQ;IACd,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAGO,MAAM,oBAAoB,CAAC;IAChC,OAAO,WAAW,CAAC,WAAW;AAChC;AAEO,MAAM,uBAAuB,CAClC,kBACA,aACA;IAEA,OAAO,GAAG,WAAW,CAAC,iBAAiB,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,WAAW,CAAC,cAAc,EAAE;AACxF;AAGO,MAAM,aAAa,CAAC,MAAgB,CAAC,MAAM,EAAE,KAAK;AAClD,MAAM,cAAc,CAAC,MAAgB,CAAC,OAAO,EAAE,KAAK;AACpD,MAAM,eAAe,CAAC,MAAgB,CAAC,QAAQ,EAAE,KAAK;AACtD,MAAM,iBAAiB,CAAC,MAAgB,CAAC,UAAU,EAAE,KAAK;AAC1D,MAAM,eAAe,CAAC,MAAgB,CAAC,QAAQ,EAAE,KAAK;AACtD,MAAM,mBAAmB,CAAC,eAAyB,CAAC,YAAY,EAAE,cAAc;AAChF,MAAM,cAAc,CAAC,UAAoB,CAAC,OAAO,EAAE,SAAS;AAC5D,MAAM,uBAAuB,IAAM,CAAC,qBAAqB,CAAC;AAC1D,MAAM,qBAAqB,CAAC,iBAA2B,CAAC,cAAc,EAAE,gBAAgB;AACxF,MAAM,gBAAgB,CAAC,YAAsB,CAAC,SAAS,EAAE,WAAW;AACpE,MAAM,kBAAkB,CAAC,cAAwB,CAAC,UAAU,EAAE,aAAa;AAG3E,MAAM,cAAc,CAAC;IAC1B,OAAO;QAAC;QAAS;QAAU;QAAY;KAAS,CAAC,QAAQ,CAAC;AAC5D;AAIO,MAAM,uBAAuB,CAAC;IACnC,OAAO;QAAC;QAAW;QAAQ;QAAU;KAAW,CAAC,QAAQ,CAAC;AAC5D", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/lib/database/users.ts"], "sourcesContent": ["import {\r\n  doc,\r\n  setDoc,\r\n  getDoc,\r\n  updateDoc,\r\n  collection,\r\n  addDoc,\r\n  query,\r\n  where,\r\n  getDocs,\r\n  Timestamp,\r\n  serverTimestamp\r\n} from 'firebase/firestore';\r\nimport { db } from '@/lib/firebase';\r\nimport { COLLECTIONS } from './collections';\r\nimport { UserDocument, AdminDocument, AdminActionDocument } from './schema';\r\n\r\n// ===== USER OPERATIONS =====\r\n\r\nexport const createUser = async (\r\n  uid: string,\r\n  userData: Omit<UserDocument, 'uid' | 'createdAt' | 'updatedAt' | 'lastLoginAt'>\r\n): Promise<void> => {\r\n  const userRef = doc(db, COLLECTIONS.USERS, uid);\r\n\r\n  const userDoc: UserDocument = {\r\n    uid,\r\n    ...userData,\r\n    createdAt: serverTimestamp() as Timestamp,\r\n    updatedAt: serverTimestamp() as Timestamp,\r\n  };\r\n\r\n  await setDoc(userRef, userDoc);\r\n};\r\n\r\nexport const getUser = async (uid: string): Promise<UserDocument | null> => {\r\n  const userRef = doc(db, COLLECTIONS.USERS, uid);\r\n  const userSnap = await getDoc(userRef);\r\n\r\n  if (userSnap.exists()) {\r\n    return userSnap.data() as unknown as UserDocument;\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport const updateUser = async (\r\n  uid: string,\r\n  updates: Partial<Omit<UserDocument, 'uid' | 'createdAt'>>\r\n): Promise<void> => {\r\n  const userRef = doc(db, COLLECTIONS.USERS, uid);\r\n\r\n  await updateDoc(userRef, {\r\n    ...updates,\r\n    updatedAt: serverTimestamp(),\r\n  });\r\n};\r\n\r\nexport const updateUserLastLogin = async (uid: string): Promise<void> => {\r\n  const userRef = doc(db, COLLECTIONS.USERS, uid);\r\n\r\n  await updateDoc(userRef, {\r\n    lastLoginAt: serverTimestamp(),\r\n    updatedAt: serverTimestamp(),\r\n  });\r\n};\r\n\r\nexport const addFCMToken = async (uid: string, token: string): Promise<void> => {\r\n  const user = await getUser(uid);\r\n  if (!user) throw new Error('User not found');\r\n\r\n  const currentTokens = user.fcmTokens || [];\r\n  if (!currentTokens.includes(token)) {\r\n    await updateUser(uid, {\r\n      fcmTokens: [...currentTokens, token]\r\n    });\r\n  }\r\n};\r\n\r\nexport const removeFCMToken = async (uid: string, token: string): Promise<void> => {\r\n  const user = await getUser(uid);\r\n  if (!user) throw new Error('User not found');\r\n\r\n  const currentTokens = user.fcmTokens || [];\r\n  await updateUser(uid, {\r\n    fcmTokens: currentTokens.filter(t => t !== token)\r\n  });\r\n};\r\n\r\nexport const getUsersByRole = async (role: UserDocument['role']): Promise<UserDocument[]> => {\r\n  const usersRef = collection(db, COLLECTIONS.USERS);\r\n  const q = query(usersRef, where('role', '==', role));\r\n  const querySnapshot = await getDocs(q);\r\n\r\n  return querySnapshot.docs.map(doc => doc.data() as unknown as UserDocument);\r\n};\r\n\r\nexport const searchUsersByEmail = async (email: string): Promise<UserDocument[]> => {\r\n  const usersRef = collection(db, COLLECTIONS.USERS);\r\n  const q = query(usersRef, where('email', '==', email));\r\n  const querySnapshot = await getDocs(q);\r\n\r\n  return querySnapshot.docs.map(doc => doc.data() as unknown as UserDocument);\r\n};\r\n\r\n// ===== ADMIN OPERATIONS =====\r\n\r\nexport const createAdmin = async (\r\n  uid: string,\r\n  adminData: Omit<AdminDocument, 'createdAt' | 'updatedAt'>\r\n): Promise<void> => {\r\n  const adminRef = doc(db, COLLECTIONS.ADMINS, uid);\r\n\r\n  const adminDoc: AdminDocument = {\r\n    ...adminData,\r\n    createdAt: serverTimestamp() as Timestamp,\r\n    updatedAt: serverTimestamp() as Timestamp,\r\n  };\r\n\r\n  await setDoc(adminRef, adminDoc);\r\n};\r\n\r\nexport const getAdmin = async (uid: string): Promise<AdminDocument | null> => {\r\n  const adminRef = doc(db, COLLECTIONS.ADMINS, uid);\r\n  const adminSnap = await getDoc(adminRef);\r\n\r\n  if (adminSnap.exists()) {\r\n    return adminSnap.data() as unknown as AdminDocument;\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport const updateAdmin = async (\r\n  uid: string,\r\n  updates: Partial<Omit<AdminDocument, 'createdAt'>>\r\n): Promise<void> => {\r\n  const adminRef = doc(db, COLLECTIONS.ADMINS, uid);\r\n\r\n  await updateDoc(adminRef, {\r\n    ...updates,\r\n    updatedAt: serverTimestamp(),\r\n  });\r\n};\r\n\r\nexport const getAdminsByDepartment = async (\r\n  department: AdminDocument['department']\r\n): Promise<AdminDocument[]> => {\r\n  const adminsRef = collection(db, COLLECTIONS.ADMINS);\r\n  const q = query(adminsRef, where('department', '==', department));\r\n  const querySnapshot = await getDocs(q);\r\n\r\n  return querySnapshot.docs.map(doc => doc.data() as unknown as AdminDocument);\r\n};\r\n\r\nexport const getAdminsByAccessLevel = async (\r\n  accessLevel: AdminDocument['accessLevel']\r\n): Promise<AdminDocument[]> => {\r\n  const adminsRef = collection(db, COLLECTIONS.ADMINS);\r\n  const q = query(adminsRef, where('accessLevel', '==', accessLevel));\r\n  const querySnapshot = await getDocs(q);\r\n\r\n  return querySnapshot.docs.map(doc => doc.data() as unknown as AdminDocument);\r\n};\r\n\r\n// ===== ADMIN ACTIONS OPERATIONS =====\r\n\r\nexport const createAdminAction = async (\r\n  adminUid: string,\r\n  actionData: Omit<AdminActionDocument, 'actionId' | 'timestamp'>\r\n): Promise<string> => {\r\n  const actionsRef = collection(db, COLLECTIONS.ADMINS, adminUid, COLLECTIONS.ADMIN_ACTIONS);\r\n\r\n  const actionDoc: Omit<AdminActionDocument, 'actionId'> = {\r\n    ...actionData,\r\n    timestamp: serverTimestamp() as Timestamp,\r\n  };\r\n\r\n  const docRef = await addDoc(actionsRef, actionDoc);\r\n\r\n  // Update the document with its own ID\r\n  await updateDoc(docRef, { actionId: docRef.id });\r\n\r\n  return docRef.id;\r\n};\r\n\r\nexport const getAdminActions = async (\r\n  adminUid: string,\r\n  actionType?: AdminActionDocument['actionType']\r\n): Promise<AdminActionDocument[]> => {\r\n  const actionsRef = collection(db, COLLECTIONS.ADMINS, adminUid, COLLECTIONS.ADMIN_ACTIONS);\r\n\r\n  let q = query(actionsRef);\r\n  if (actionType) {\r\n    q = query(actionsRef, where('actionType', '==', actionType));\r\n  }\r\n\r\n  const querySnapshot = await getDocs(q);\r\n  return querySnapshot.docs.map(doc => doc.data() as unknown as AdminActionDocument);\r\n};\r\n\r\n// ===== UTILITY FUNCTIONS =====\r\n\r\nexport const checkUserExists = async (uid: string): Promise<boolean> => {\r\n  const user = await getUser(uid);\r\n  return user !== null;\r\n};\r\n\r\nexport const checkAdminExists = async (uid: string): Promise<boolean> => {\r\n  const admin = await getAdmin(uid);\r\n  return admin !== null;\r\n};\r\n\r\nexport const isUserActive = async (uid: string): Promise<boolean> => {\r\n  const user = await getUser(uid);\r\n  return user?.isActive === true;\r\n};\r\n\r\nexport const isUserVerified = async (uid: string): Promise<boolean> => {\r\n  const user = await getUser(uid);\r\n  return user?.isVerified === true;\r\n};\r\n\r\nexport const hasAdminPermission = async (\r\n  adminUid: string,\r\n  permission: string\r\n): Promise<boolean> => {\r\n  const admin = await getAdmin(adminUid);\r\n  return admin?.permissions.includes(permission) === true;\r\n};\r\n\r\n// ===== BATCH OPERATIONS =====\r\n\r\nexport const createUserWithRole = async (\r\n  uid: string,\r\n  email: string,\r\n  role: UserDocument['role'],\r\n  additionalData: Partial<UserDocument> = {}\r\n): Promise<void> => {\r\n  const userData: Omit<UserDocument, 'uid' | 'createdAt' | 'updatedAt' | 'lastLoginAt'> = {\r\n    email,\r\n    role,\r\n    isActive: true,\r\n    isVerified: false,\r\n    ...additionalData,\r\n  };\r\n\r\n  await createUser(uid, userData);\r\n};\r\n\r\nexport const promoteUserToAdmin = async (\r\n  uid: string,\r\n  adminData: Omit<AdminDocument, 'userRef' | 'createdAt' | 'updatedAt'>\r\n): Promise<void> => {\r\n  // Update user role\r\n  await updateUser(uid, { role: 'admin' });\r\n\r\n  // Create admin document\r\n  await createAdmin(uid, {\r\n    ...adminData,\r\n    userRef: `users/${uid}`,\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAaA;AACA;;;;AAKO,MAAM,aAAa,OACxB,KACA;IAEA,MAAM,UAAU,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,wIAAA,CAAA,KAAE,EAAE,uJAAA,CAAA,cAAW,CAAC,KAAK,EAAE;IAE3C,MAAM,UAAwB;QAC5B;QACA,GAAG,QAAQ;QACX,WAAW,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD;QACzB,WAAW,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD;IAC3B;IAEA,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,SAAS;AACxB;AAEO,MAAM,UAAU,OAAO;IAC5B,MAAM,UAAU,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,wIAAA,CAAA,KAAE,EAAE,uJAAA,CAAA,cAAW,CAAC,KAAK,EAAE;IAC3C,MAAM,WAAW,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE;IAE9B,IAAI,SAAS,MAAM,IAAI;QACrB,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;AACT;AAEO,MAAM,aAAa,OACxB,KACA;IAEA,MAAM,UAAU,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,wIAAA,CAAA,KAAE,EAAE,uJAAA,CAAA,cAAW,CAAC,KAAK,EAAE;IAE3C,MAAM,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACvB,GAAG,OAAO;QACV,WAAW,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD;IAC3B;AACF;AAEO,MAAM,sBAAsB,OAAO;IACxC,MAAM,UAAU,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,wIAAA,CAAA,KAAE,EAAE,uJAAA,CAAA,cAAW,CAAC,KAAK,EAAE;IAE3C,MAAM,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QACvB,aAAa,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD;QAC3B,WAAW,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD;IAC3B;AACF;AAEO,MAAM,cAAc,OAAO,KAAa;IAC7C,MAAM,OAAO,MAAM,QAAQ;IAC3B,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;IAE3B,MAAM,gBAAgB,KAAK,SAAS,IAAI,EAAE;IAC1C,IAAI,CAAC,cAAc,QAAQ,CAAC,QAAQ;QAClC,MAAM,WAAW,KAAK;YACpB,WAAW;mBAAI;gBAAe;aAAM;QACtC;IACF;AACF;AAEO,MAAM,iBAAiB,OAAO,KAAa;IAChD,MAAM,OAAO,MAAM,QAAQ;IAC3B,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;IAE3B,MAAM,gBAAgB,KAAK,SAAS,IAAI,EAAE;IAC1C,MAAM,WAAW,KAAK;QACpB,WAAW,cAAc,MAAM,CAAC,CAAA,IAAK,MAAM;IAC7C;AACF;AAEO,MAAM,iBAAiB,OAAO;IACnC,MAAM,WAAW,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,wIAAA,CAAA,KAAE,EAAE,uJAAA,CAAA,cAAW,CAAC,KAAK;IACjD,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,QAAQ,MAAM;IAC9C,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;IAEpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;AAC/C;AAEO,MAAM,qBAAqB,OAAO;IACvC,MAAM,WAAW,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,wIAAA,CAAA,KAAE,EAAE,uJAAA,CAAA,cAAW,CAAC,KAAK;IACjD,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,UAAU,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,SAAS,MAAM;IAC/C,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;IAEpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;AAC/C;AAIO,MAAM,cAAc,OACzB,KACA;IAEA,MAAM,WAAW,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,wIAAA,CAAA,KAAE,EAAE,uJAAA,CAAA,cAAW,CAAC,MAAM,EAAE;IAE7C,MAAM,WAA0B;QAC9B,GAAG,SAAS;QACZ,WAAW,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD;QACzB,WAAW,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD;IAC3B;IAEA,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,UAAU;AACzB;AAEO,MAAM,WAAW,OAAO;IAC7B,MAAM,WAAW,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,wIAAA,CAAA,KAAE,EAAE,uJAAA,CAAA,cAAW,CAAC,MAAM,EAAE;IAC7C,MAAM,YAAY,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE;IAE/B,IAAI,UAAU,MAAM,IAAI;QACtB,OAAO,UAAU,IAAI;IACvB;IAEA,OAAO;AACT;AAEO,MAAM,cAAc,OACzB,KACA;IAEA,MAAM,WAAW,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,wIAAA,CAAA,KAAE,EAAE,uJAAA,CAAA,cAAW,CAAC,MAAM,EAAE;IAE7C,MAAM,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,UAAU;QACxB,GAAG,OAAO;QACV,WAAW,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD;IAC3B;AACF;AAEO,MAAM,wBAAwB,OACnC;IAEA,MAAM,YAAY,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,wIAAA,CAAA,KAAE,EAAE,uJAAA,CAAA,cAAW,CAAC,MAAM;IACnD,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,WAAW,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,cAAc,MAAM;IACrD,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;IAEpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;AAC/C;AAEO,MAAM,yBAAyB,OACpC;IAEA,MAAM,YAAY,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,wIAAA,CAAA,KAAE,EAAE,uJAAA,CAAA,cAAW,CAAC,MAAM;IACnD,MAAM,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,WAAW,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,eAAe,MAAM;IACtD,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;IAEpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;AAC/C;AAIO,MAAM,oBAAoB,OAC/B,UACA;IAEA,MAAM,aAAa,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,wIAAA,CAAA,KAAE,EAAE,uJAAA,CAAA,cAAW,CAAC,MAAM,EAAE,UAAU,uJAAA,CAAA,cAAW,CAAC,aAAa;IAEzF,MAAM,YAAmD;QACvD,GAAG,UAAU;QACb,WAAW,CAAA,GAAA,sKAAA,CAAA,kBAAe,AAAD;IAC3B;IAEA,MAAM,SAAS,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,YAAY;IAExC,sCAAsC;IACtC,MAAM,CAAA,GAAA,sKAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;QAAE,UAAU,OAAO,EAAE;IAAC;IAE9C,OAAO,OAAO,EAAE;AAClB;AAEO,MAAM,kBAAkB,OAC7B,UACA;IAEA,MAAM,aAAa,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,wIAAA,CAAA,KAAE,EAAE,uJAAA,CAAA,cAAW,CAAC,MAAM,EAAE,UAAU,uJAAA,CAAA,cAAW,CAAC,aAAa;IAEzF,IAAI,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE;IACd,IAAI,YAAY;QACd,IAAI,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,cAAc,MAAM;IAClD;IAEA,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;IACpC,OAAO,cAAc,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI;AAC/C;AAIO,MAAM,kBAAkB,OAAO;IACpC,MAAM,OAAO,MAAM,QAAQ;IAC3B,OAAO,SAAS;AAClB;AAEO,MAAM,mBAAmB,OAAO;IACrC,MAAM,QAAQ,MAAM,SAAS;IAC7B,OAAO,UAAU;AACnB;AAEO,MAAM,eAAe,OAAO;IACjC,MAAM,OAAO,MAAM,QAAQ;IAC3B,OAAO,MAAM,aAAa;AAC5B;AAEO,MAAM,iBAAiB,OAAO;IACnC,MAAM,OAAO,MAAM,QAAQ;IAC3B,OAAO,MAAM,eAAe;AAC9B;AAEO,MAAM,qBAAqB,OAChC,UACA;IAEA,MAAM,QAAQ,MAAM,SAAS;IAC7B,OAAO,OAAO,YAAY,SAAS,gBAAgB;AACrD;AAIO,MAAM,qBAAqB,OAChC,KACA,OACA,MACA,iBAAwC,CAAC,CAAC;IAE1C,MAAM,WAAkF;QACtF;QACA;QACA,UAAU;QACV,YAAY;QACZ,GAAG,cAAc;IACnB;IAEA,MAAM,WAAW,KAAK;AACxB;AAEO,MAAM,qBAAqB,OAChC,KACA;IAEA,mBAAmB;IACnB,MAAM,WAAW,KAAK;QAAE,MAAM;IAAQ;IAEtC,wBAAwB;IACxB,MAAM,YAAY,KAAK;QACrB,GAAG,SAAS;QACZ,SAAS,CAAC,MAAM,EAAE,KAAK;IACzB;AACF", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react';\r\nimport {\r\n  User as FirebaseUser,\r\n  signInWithEmailAndPassword,\r\n  createUserWithEmailAndPassword,\r\n  signOut as firebaseSignOut,\r\n  onAuthStateChanged,\r\n  update<PERSON>rofile as updateFirebaseProfile,\r\n  onIdTokenChanged,\r\n  getIdToken,\r\n  GoogleAuthProvider,\r\n  signInWithPopup,\r\n  signInWithRedirect,\r\n} from 'firebase/auth';\r\nimport { auth } from '@/lib/firebase';\r\nimport { User, AuthContextType } from '@/types';\r\nimport { createUser, getUser, updateUser, updateUserLastLogin } from '@/lib/database/users';\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\n// Enterprise-grade auth state management\r\nconst AUTH_STATE_KEY = 'tap2go_auth_initialized';\r\nconst TOKEN_REFRESH_INTERVAL = 50 * 60 * 1000; // 50 minutes\r\nconst SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours\r\n\r\n// Secure session storage (httpOnly would be better, but this is client-side)\r\nconst getAuthSession = (): { initialized: boolean; timestamp: number } => {\r\n  if (typeof window === 'undefined') return { initialized: false, timestamp: 0 };\r\n\r\n  try {\r\n    const session = sessionStorage.getItem(AUTH_STATE_KEY);\r\n    if (session) {\r\n      const parsed = JSON.parse(session);\r\n      // Check if session is still valid (not expired)\r\n      if (Date.now() - parsed.timestamp < SESSION_TIMEOUT) {\r\n        return parsed;\r\n      }\r\n    }\r\n  } catch (error) {\r\n    console.error('Error reading auth session:', error);\r\n  }\r\n\r\n  return { initialized: false, timestamp: 0 };\r\n};\r\n\r\nconst setAuthSession = (initialized: boolean) => {\r\n  if (typeof window === 'undefined') return;\r\n\r\n  try {\r\n    if (initialized) {\r\n      sessionStorage.setItem(AUTH_STATE_KEY, JSON.stringify({\r\n        initialized: true,\r\n        timestamp: Date.now()\r\n      }));\r\n    } else {\r\n      sessionStorage.removeItem(AUTH_STATE_KEY);\r\n    }\r\n  } catch (error) {\r\n    console.error('Error setting auth session:', error);\r\n  }\r\n};\r\n\r\n// Multi-tab synchronization\r\nconst broadcastAuthChange = (user: User | null) => {\r\n  if (typeof window === 'undefined') return;\r\n\r\n  try {\r\n    const event = new CustomEvent('tap2go-auth-change', {\r\n      detail: { user, timestamp: Date.now() }\r\n    });\r\n    window.dispatchEvent(event);\r\n  } catch (error) {\r\n    console.error('Error broadcasting auth change:', error);\r\n  }\r\n};\r\n\r\nexport function useAuth() {\r\n  const context = useContext(AuthContext);\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n}\r\n\r\ninterface AuthProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport function AuthProvider({ children }: AuthProviderProps) {\r\n  // Enterprise-grade state management\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [isInitialized, setIsInitialized] = useState(false);\r\n  const [authError, setAuthError] = useState<string | null>(null);\r\n  const [isHydrated, setIsHydrated] = useState(false);\r\n\r\n  // Refs for cleanup and token management\r\n  const tokenRefreshInterval = useRef<NodeJS.Timeout | null>(null);\r\n  const authStateUnsubscribe = useRef<(() => void) | null>(null);\r\n  const tokenChangeUnsubscribe = useRef<(() => void) | null>(null);\r\n\r\n  // SSR-safe optimistic auth state (only set after hydration)\r\n  const [showOptimisticAuth, setShowOptimisticAuth] = useState(false);\r\n\r\n  // Handle hydration and set optimistic state\r\n  useEffect(() => {\r\n    setIsHydrated(true);\r\n    const session = getAuthSession();\r\n    if (session.initialized) {\r\n      setShowOptimisticAuth(true);\r\n    }\r\n  }, []);\r\n\r\n  // Enterprise-grade token refresh\r\n  const setupTokenRefresh = useCallback(async (firebaseUser: FirebaseUser) => {\r\n    if (tokenRefreshInterval.current) {\r\n      clearInterval(tokenRefreshInterval.current);\r\n    }\r\n\r\n    tokenRefreshInterval.current = setInterval(async () => {\r\n      try {\r\n        await getIdToken(firebaseUser, true); // Force refresh\r\n        console.log('Token refreshed successfully');\r\n      } catch (error) {\r\n        console.error('Token refresh failed:', error);\r\n        setAuthError('Session expired. Please sign in again.');\r\n      }\r\n    }, TOKEN_REFRESH_INTERVAL);\r\n  }, []);\r\n\r\n  // Clean up intervals\r\n  const cleanupTokenRefresh = useCallback(() => {\r\n    if (tokenRefreshInterval.current) {\r\n      clearInterval(tokenRefreshInterval.current);\r\n      tokenRefreshInterval.current = null;\r\n    }\r\n  }, []);\r\n\r\n  // Handle user data loading\r\n  const handleUserLoad = useCallback(async (firebaseUser: FirebaseUser): Promise<User | null> => {\r\n    try {\r\n      let userData = await getUser(firebaseUser.uid);\r\n\r\n      // If user doesn't exist, create them (happens with social auth)\r\n      if (!userData) {\r\n        const providerData = firebaseUser.providerData[0];\r\n\r\n        await createUser(firebaseUser.uid, {\r\n          email: firebaseUser.email || '',\r\n          role: 'customer', // Default role for social auth users\r\n          isActive: true,\r\n          isVerified: firebaseUser.emailVerified,\r\n        });\r\n\r\n        // Fetch the newly created user\r\n        userData = await getUser(firebaseUser.uid);\r\n      }\r\n\r\n      if (userData) {\r\n        const userObj: User = {\r\n          id: firebaseUser.uid,\r\n          email: firebaseUser.email!,\r\n          role: userData.role,\r\n          phone: userData.phoneNumber,\r\n          isActive: userData.isActive,\r\n          isVerified: userData.isVerified,\r\n          createdAt: userData.createdAt?.toDate(),\r\n          updatedAt: userData.updatedAt?.toDate(),\r\n        };\r\n\r\n        // Update last login time in background\r\n        updateUserLastLogin(firebaseUser.uid).catch(console.error);\r\n\r\n        return userObj;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading user data:', error);\r\n      setAuthError('Failed to load user data');\r\n    }\r\n    return null;\r\n  }, []);\r\n\r\n  // Main auth state listener\r\n  useEffect(() => {\r\n    let mounted = true;\r\n\r\n    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {\r\n      if (!mounted) return;\r\n\r\n      try {\r\n        setAuthError(null);\r\n\r\n        if (firebaseUser) {\r\n          const userObj = await handleUserLoad(firebaseUser);\r\n\r\n          if (mounted && userObj) {\r\n            setUser(userObj);\r\n            setAuthSession(true);\r\n            setupTokenRefresh(firebaseUser);\r\n            broadcastAuthChange(userObj);\r\n          } else if (mounted) {\r\n            setUser(null);\r\n            setAuthSession(false);\r\n            cleanupTokenRefresh();\r\n            broadcastAuthChange(null);\r\n          }\r\n        } else {\r\n          if (mounted) {\r\n            setUser(null);\r\n            setAuthSession(false);\r\n            cleanupTokenRefresh();\r\n            broadcastAuthChange(null);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Auth state change error:', error);\r\n        if (mounted) {\r\n          setUser(null);\r\n          setAuthError('Authentication error occurred');\r\n          setAuthSession(false);\r\n          cleanupTokenRefresh();\r\n        }\r\n      } finally {\r\n        if (mounted) {\r\n          setLoading(false);\r\n          setIsInitialized(true);\r\n          // Only clear optimistic auth after real auth state is determined\r\n          if (isHydrated) {\r\n            setShowOptimisticAuth(false);\r\n          }\r\n        }\r\n      }\r\n    });\r\n\r\n    authStateUnsubscribe.current = unsubscribe;\r\n\r\n    return () => {\r\n      mounted = false;\r\n      unsubscribe();\r\n      cleanupTokenRefresh();\r\n    };\r\n  }, [handleUserLoad, setupTokenRefresh, cleanupTokenRefresh, isHydrated]);\r\n\r\n  const signIn = async (email: string, password: string) => {\r\n    setLoading(true);\r\n    try {\r\n      await signInWithEmailAndPassword(auth, email, password);\r\n    } catch (error) {\r\n      setLoading(false);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const signUp = async (email: string, password: string, name: string, role: User['role']) => {\r\n    setLoading(true);\r\n    try {\r\n      const { user: firebaseUser } = await createUserWithEmailAndPassword(auth, email, password);\r\n\r\n      // Update Firebase Auth profile\r\n      await updateFirebaseProfile(firebaseUser, { displayName: name });\r\n\r\n      // Create user document in Firestore using new database functions\r\n      await createUser(firebaseUser.uid, {\r\n        email,\r\n        role,\r\n        isActive: true,\r\n        isVerified: false,\r\n      });\r\n    } catch (error) {\r\n      setLoading(false);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  // Multi-tab auth synchronization\r\n  useEffect(() => {\r\n    const handleAuthChange = (event: CustomEvent) => {\r\n      const { user: newUser, timestamp } = event.detail;\r\n      // Only update if this is a newer change (prevent loops)\r\n      if (timestamp > Date.now() - 1000) {\r\n        setUser(newUser);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('tap2go-auth-change', handleAuthChange as EventListener);\r\n\r\n    return () => {\r\n      window.removeEventListener('tap2go-auth-change', handleAuthChange as EventListener);\r\n    };\r\n  }, []);\r\n\r\n  // Enhanced sign out with proper cleanup\r\n  // Google Sign-In\r\n  const signInWithGoogle = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const provider = new GoogleAuthProvider();\r\n      // Optional: Add scopes for additional permissions\r\n      provider.addScope('profile');\r\n      provider.addScope('email');\r\n\r\n      // Use popup for better UX (can also use signInWithRedirect)\r\n      await signInWithPopup(auth, provider);\r\n      // Auth state change will be handled by onAuthStateChanged listener\r\n    } catch (error) {\r\n      setLoading(false);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const signOut = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      cleanupTokenRefresh();\r\n      await firebaseSignOut(auth);\r\n      setAuthSession(false);\r\n      broadcastAuthChange(null);\r\n    } catch (error) {\r\n      console.error('Error signing out:', error);\r\n      throw error;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [cleanupTokenRefresh]);\r\n\r\n  // Enhanced profile update\r\n  const updateProfile = useCallback(async (data: Partial<User>) => {\r\n    if (!user) throw new Error('No user logged in');\r\n\r\n    try {\r\n      await updateUser(user.id, {\r\n        phoneNumber: data.phone,\r\n        profileImageUrl: data.profileImage,\r\n      });\r\n\r\n      const updatedUser = { ...user, ...data, updatedAt: new Date() };\r\n      setUser(updatedUser);\r\n      broadcastAuthChange(updatedUser);\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  }, [user]);\r\n\r\n  // Enhanced context value with error state\r\n  const value: AuthContextType & { authError: string | null; isInitialized: boolean } = {\r\n    user,\r\n    loading,\r\n    signIn,\r\n    signUp,\r\n    signInWithGoogle,\r\n    signOut,\r\n    updateProfile,\r\n    authError,\r\n    isInitialized,\r\n  };\r\n\r\n  // FAST LOADING: Never block the entire app - always render children\r\n  // Let individual components handle their own auth states\r\n  return (\r\n    <AuthContext.Provider value={value}>\r\n      {children}\r\n    </AuthContext.Provider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAEA;;;AAlBA;;;;;AAoBA,MAAM,4BAAc,CAAA,GAAA,4KAAA,CAAA,gBAAa,AAAD,EAA+B;AAE/D,yCAAyC;AACzC,MAAM,iBAAiB;AACvB,MAAM,yBAAyB,KAAK,KAAK,MAAM,aAAa;AAC5D,MAAM,kBAAkB,KAAK,KAAK,KAAK,MAAM,WAAW;AAExD,6EAA6E;AAC7E,MAAM,iBAAiB;IACrB,uCAAmC;;IAA2C;IAE9E,IAAI;QACF,MAAM,UAAU,eAAe,OAAO,CAAC;QACvC,IAAI,SAAS;YACX,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,gDAAgD;YAChD,IAAI,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,iBAAiB;gBACnD,OAAO;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;IAC/C;IAEA,OAAO;QAAE,aAAa;QAAO,WAAW;IAAE;AAC5C;AAEA,MAAM,iBAAiB,CAAC;IACtB,uCAAmC;;IAAM;IAEzC,IAAI;QACF,IAAI,aAAa;YACf,eAAe,OAAO,CAAC,gBAAgB,KAAK,SAAS,CAAC;gBACpD,aAAa;gBACb,WAAW,KAAK,GAAG;YACrB;QACF,OAAO;YACL,eAAe,UAAU,CAAC;QAC5B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;IAC/C;AACF;AAEA,4BAA4B;AAC5B,MAAM,sBAAsB,CAAC;IAC3B,uCAAmC;;IAAM;IAEzC,IAAI;QACF,MAAM,QAAQ,IAAI,YAAY,sBAAsB;YAClD,QAAQ;gBAAE;gBAAM,WAAW,KAAK,GAAG;YAAG;QACxC;QACA,OAAO,aAAa,CAAC;IACvB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;IACnD;AACF;AAEO,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAYT,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAC1D,oCAAoC;IACpC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,wCAAwC;IACxC,MAAM,uBAAuB,CAAA,GAAA,4KAAA,CAAA,SAAM,AAAD,EAAyB;IAC3D,MAAM,uBAAuB,CAAA,GAAA,4KAAA,CAAA,SAAM,AAAD,EAAuB;IACzD,MAAM,yBAAyB,CAAA,GAAA,4KAAA,CAAA,SAAM,AAAD,EAAuB;IAE3D,4DAA4D;IAC5D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,4CAA4C;IAC5C,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;kCAAE;YACR,cAAc;YACd,MAAM,UAAU;YAChB,IAAI,QAAQ,WAAW,EAAE;gBACvB,sBAAsB;YACxB;QACF;iCAAG,EAAE;IAEL,iCAAiC;IACjC,MAAM,oBAAoB,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD;uDAAE,OAAO;YAC3C,IAAI,qBAAqB,OAAO,EAAE;gBAChC,cAAc,qBAAqB,OAAO;YAC5C;YAEA,qBAAqB,OAAO,GAAG;+DAAY;oBACzC,IAAI;wBACF,MAAM,CAAA,GAAA,iNAAA,CAAA,aAAU,AAAD,EAAE,cAAc,OAAO,gBAAgB;wBACtD,QAAQ,GAAG,CAAC;oBACd,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,yBAAyB;wBACvC,aAAa;oBACf;gBACF;8DAAG;QACL;sDAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,sBAAsB,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD;yDAAE;YACtC,IAAI,qBAAqB,OAAO,EAAE;gBAChC,cAAc,qBAAqB,OAAO;gBAC1C,qBAAqB,OAAO,GAAG;YACjC;QACF;wDAAG,EAAE;IAEL,2BAA2B;IAC3B,MAAM,iBAAiB,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD;oDAAE,OAAO;YACxC,IAAI;gBACF,IAAI,WAAW,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,aAAa,GAAG;gBAE7C,gEAAgE;gBAChE,IAAI,CAAC,UAAU;oBACb,MAAM,eAAe,aAAa,YAAY,CAAC,EAAE;oBAEjD,MAAM,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,aAAa,GAAG,EAAE;wBACjC,OAAO,aAAa,KAAK,IAAI;wBAC7B,MAAM;wBACN,UAAU;wBACV,YAAY,aAAa,aAAa;oBACxC;oBAEA,+BAA+B;oBAC/B,WAAW,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,aAAa,GAAG;gBAC3C;gBAEA,IAAI,UAAU;oBACZ,MAAM,UAAgB;wBACpB,IAAI,aAAa,GAAG;wBACpB,OAAO,aAAa,KAAK;wBACzB,MAAM,SAAS,IAAI;wBACnB,OAAO,SAAS,WAAW;wBAC3B,UAAU,SAAS,QAAQ;wBAC3B,YAAY,SAAS,UAAU;wBAC/B,WAAW,SAAS,SAAS,EAAE;wBAC/B,WAAW,SAAS,SAAS,EAAE;oBACjC;oBAEA,uCAAuC;oBACvC,CAAA,GAAA,iJAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,GAAG,EAAE,KAAK,CAAC,QAAQ,KAAK;oBAEzD,OAAO;gBACT;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,aAAa;YACf;YACA,OAAO;QACT;mDAAG,EAAE;IAEL,2BAA2B;IAC3B,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,UAAU;YAEd,MAAM,cAAc,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,wIAAA,CAAA,OAAI;sDAAE,OAAO;oBAClD,IAAI,CAAC,SAAS;oBAEd,IAAI;wBACF,aAAa;wBAEb,IAAI,cAAc;4BAChB,MAAM,UAAU,MAAM,eAAe;4BAErC,IAAI,WAAW,SAAS;gCACtB,QAAQ;gCACR,eAAe;gCACf,kBAAkB;gCAClB,oBAAoB;4BACtB,OAAO,IAAI,SAAS;gCAClB,QAAQ;gCACR,eAAe;gCACf;gCACA,oBAAoB;4BACtB;wBACF,OAAO;4BACL,IAAI,SAAS;gCACX,QAAQ;gCACR,eAAe;gCACf;gCACA,oBAAoB;4BACtB;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,IAAI,SAAS;4BACX,QAAQ;4BACR,aAAa;4BACb,eAAe;4BACf;wBACF;oBACF,SAAU;wBACR,IAAI,SAAS;4BACX,WAAW;4BACX,iBAAiB;4BACjB,iEAAiE;4BACjE,IAAI,YAAY;gCACd,sBAAsB;4BACxB;wBACF;oBACF;gBACF;;YAEA,qBAAqB,OAAO,GAAG;YAE/B;0CAAO;oBACL,UAAU;oBACV;oBACA;gBACF;;QACF;iCAAG;QAAC;QAAgB;QAAmB;QAAqB;KAAW;IAEvE,MAAM,SAAS,OAAO,OAAe;QACnC,WAAW;QACX,IAAI;YACF,MAAM,CAAA,GAAA,iOAAA,CAAA,6BAA0B,AAAD,EAAE,wIAAA,CAAA,OAAI,EAAE,OAAO;QAChD,EAAE,OAAO,OAAO;YACd,WAAW;YACX,MAAM;QACR;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB,MAAc;QACnE,WAAW;QACX,IAAI;YACF,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,CAAA,GAAA,qOAAA,CAAA,iCAA8B,AAAD,EAAE,wIAAA,CAAA,OAAI,EAAE,OAAO;YAEjF,+BAA+B;YAC/B,MAAM,CAAA,GAAA,oNAAA,CAAA,gBAAqB,AAAD,EAAE,cAAc;gBAAE,aAAa;YAAK;YAE9D,iEAAiE;YACjE,MAAM,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,aAAa,GAAG,EAAE;gBACjC;gBACA;gBACA,UAAU;gBACV,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,WAAW;YACX,MAAM;QACR;IACF;IAEA,iCAAiC;IACjC,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;2DAAmB,CAAC;oBACxB,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,MAAM;oBACjD,wDAAwD;oBACxD,IAAI,YAAY,KAAK,GAAG,KAAK,MAAM;wBACjC,QAAQ;oBACV;gBACF;;YAEA,OAAO,gBAAgB,CAAC,sBAAsB;YAE9C;0CAAO;oBACL,OAAO,mBAAmB,CAAC,sBAAsB;gBACnD;;QACF;iCAAG,EAAE;IAEL,wCAAwC;IACxC,iBAAiB;IACjB,MAAM,mBAAmB;QACvB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,IAAI,wNAAA,CAAA,qBAAkB;YACvC,kDAAkD;YAClD,SAAS,QAAQ,CAAC;YAClB,SAAS,QAAQ,CAAC;YAElB,4DAA4D;YAC5D,MAAM,CAAA,GAAA,qNAAA,CAAA,kBAAe,AAAD,EAAE,wIAAA,CAAA,OAAI,EAAE;QAC5B,mEAAmE;QACrE,EAAE,OAAO,OAAO;YACd,WAAW;YACX,MAAM;QACR;IACF;IAEA,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD;6CAAE;YAC1B,IAAI;gBACF,WAAW;gBACX;gBACA,MAAM,CAAA,GAAA,6MAAA,CAAA,UAAe,AAAD,EAAE,wIAAA,CAAA,OAAI;gBAC1B,eAAe;gBACf,oBAAoB;YACtB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,MAAM;YACR,SAAU;gBACR,WAAW;YACb;QACF;4CAAG;QAAC;KAAoB;IAExB,0BAA0B;IAC1B,MAAM,gBAAgB,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD;mDAAE,OAAO;YACvC,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;YAE3B,IAAI;gBACF,MAAM,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,KAAK,EAAE,EAAE;oBACxB,aAAa,KAAK,KAAK;oBACvB,iBAAiB,KAAK,YAAY;gBACpC;gBAEA,MAAM,cAAc;oBAAE,GAAG,IAAI;oBAAE,GAAG,IAAI;oBAAE,WAAW,IAAI;gBAAO;gBAC9D,QAAQ;gBACR,oBAAoB;YACtB,EAAE,OAAO,OAAO;gBACd,MAAM;YACR;QACF;kDAAG;QAAC;KAAK;IAET,0CAA0C;IAC1C,MAAM,QAAgF;QACpF;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,oEAAoE;IACpE,yDAAyD;IACzD,qBACE,4MAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IAnRgB;KAAA", "debugId": null}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/contexts/CartContext.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext, useReducer, useEffect } from 'react';\r\nimport { Cart, CartItem, MenuItem, CartContextType } from '@/types';\r\n\r\nconst CartContext = createContext<CartContextType | undefined>(undefined);\r\n\r\nexport function useCart() {\r\n  const context = useContext(CartContext);\r\n  if (context === undefined) {\r\n    throw new Error('useCart must be used within a CartProvider');\r\n  }\r\n  return context;\r\n}\r\n\r\ntype CartAction =\r\n  | { type: 'ADD_ITEM'; payload: { item: MenuItem; quantity: number; specialInstructions?: string } }\r\n  | { type: 'REMOVE_ITEM'; payload: { itemId: string } }\r\n  | { type: 'UPDATE_QUANTITY'; payload: { itemId: string; quantity: number } }\r\n  | { type: 'CLEAR_CART' }\r\n  | { type: 'LOAD_CART'; payload: Cart | null };\r\n\r\nfunction cartReducer(state: Cart | null, action: CartAction): Cart | null {\r\n  switch (action.type) {\r\n    case 'ADD_ITEM': {\r\n      const { item, quantity, specialInstructions } = action.payload;\r\n      \r\n      if (!state || state.restaurantId !== item.restaurantId) {\r\n        // Create new cart or replace if different restaurant\r\n        const newCartItem: CartItem = {\r\n          id: `${item.id}-${Date.now()}`,\r\n          menuItem: item,\r\n          quantity,\r\n          specialInstructions,\r\n          totalPrice: item.price * quantity,\r\n        };\r\n        \r\n        const subtotal = newCartItem.totalPrice;\r\n        const deliveryFee = 5.99; // Default delivery fee\r\n        const tax = subtotal * 0.08; // 8% tax\r\n        \r\n        return {\r\n          items: [newCartItem],\r\n          restaurantId: item.restaurantId,\r\n          subtotal,\r\n          deliveryFee,\r\n          tax,\r\n          total: subtotal + deliveryFee + tax,\r\n        };\r\n      }\r\n      \r\n      // Add to existing cart\r\n      const existingItemIndex = state.items.findIndex(\r\n        cartItem => cartItem.menuItem.id === item.id && cartItem.specialInstructions === specialInstructions\r\n      );\r\n      \r\n      let newItems: CartItem[];\r\n      \r\n      if (existingItemIndex >= 0) {\r\n        // Update existing item\r\n        newItems = state.items.map((cartItem, index) =>\r\n          index === existingItemIndex\r\n            ? {\r\n                ...cartItem,\r\n                quantity: cartItem.quantity + quantity,\r\n                totalPrice: (cartItem.quantity + quantity) * cartItem.menuItem.price,\r\n              }\r\n            : cartItem\r\n        );\r\n      } else {\r\n        // Add new item\r\n        const newCartItem: CartItem = {\r\n          id: `${item.id}-${Date.now()}`,\r\n          menuItem: item,\r\n          quantity,\r\n          specialInstructions,\r\n          totalPrice: item.price * quantity,\r\n        };\r\n        newItems = [...state.items, newCartItem];\r\n      }\r\n      \r\n      const subtotal = newItems.reduce((sum, item) => sum + item.totalPrice, 0);\r\n      const tax = subtotal * 0.08;\r\n      \r\n      return {\r\n        ...state,\r\n        items: newItems,\r\n        subtotal,\r\n        tax,\r\n        total: subtotal + state.deliveryFee + tax,\r\n      };\r\n    }\r\n    \r\n    case 'REMOVE_ITEM': {\r\n      if (!state) return null;\r\n      \r\n      const newItems = state.items.filter(item => item.id !== action.payload.itemId);\r\n      \r\n      if (newItems.length === 0) {\r\n        return null;\r\n      }\r\n      \r\n      const subtotal = newItems.reduce((sum, item) => sum + item.totalPrice, 0);\r\n      const tax = subtotal * 0.08;\r\n      \r\n      return {\r\n        ...state,\r\n        items: newItems,\r\n        subtotal,\r\n        tax,\r\n        total: subtotal + state.deliveryFee + tax,\r\n      };\r\n    }\r\n    \r\n    case 'UPDATE_QUANTITY': {\r\n      if (!state) return null;\r\n      \r\n      const { itemId, quantity } = action.payload;\r\n      \r\n      if (quantity <= 0) {\r\n        return cartReducer(state, { type: 'REMOVE_ITEM', payload: { itemId } });\r\n      }\r\n      \r\n      const newItems = state.items.map(item =>\r\n        item.id === itemId\r\n          ? {\r\n              ...item,\r\n              quantity,\r\n              totalPrice: item.menuItem.price * quantity,\r\n            }\r\n          : item\r\n      );\r\n      \r\n      const subtotal = newItems.reduce((sum, item) => sum + item.totalPrice, 0);\r\n      const tax = subtotal * 0.08;\r\n      \r\n      return {\r\n        ...state,\r\n        items: newItems,\r\n        subtotal,\r\n        tax,\r\n        total: subtotal + state.deliveryFee + tax,\r\n      };\r\n    }\r\n    \r\n    case 'CLEAR_CART':\r\n      return null;\r\n    \r\n    case 'LOAD_CART':\r\n      return action.payload;\r\n    \r\n    default:\r\n      return state;\r\n  }\r\n}\r\n\r\ninterface CartProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport function CartProvider({ children }: CartProviderProps) {\r\n  const [cart, dispatch] = useReducer(cartReducer, null);\r\n\r\n  // Load cart from localStorage on mount\r\n  useEffect(() => {\r\n    const savedCart = localStorage.getItem('cart');\r\n    if (savedCart) {\r\n      try {\r\n        const parsedCart = JSON.parse(savedCart);\r\n        dispatch({ type: 'LOAD_CART', payload: parsedCart });\r\n      } catch (error) {\r\n        console.error('Error loading cart from localStorage:', error);\r\n      }\r\n    }\r\n  }, []);\r\n\r\n  // Save cart to localStorage whenever it changes\r\n  useEffect(() => {\r\n    if (cart) {\r\n      localStorage.setItem('cart', JSON.stringify(cart));\r\n    } else {\r\n      localStorage.removeItem('cart');\r\n    }\r\n  }, [cart]);\r\n\r\n  const addToCart = (item: MenuItem, quantity: number, specialInstructions?: string) => {\r\n    dispatch({ type: 'ADD_ITEM', payload: { item, quantity, specialInstructions } });\r\n  };\r\n\r\n  const removeFromCart = (itemId: string) => {\r\n    dispatch({ type: 'REMOVE_ITEM', payload: { itemId } });\r\n  };\r\n\r\n  const updateQuantity = (itemId: string, quantity: number) => {\r\n    dispatch({ type: 'UPDATE_QUANTITY', payload: { itemId, quantity } });\r\n  };\r\n\r\n  const clearCart = () => {\r\n    dispatch({ type: 'CLEAR_CART' });\r\n  };\r\n\r\n  const getCartTotal = () => {\r\n    return cart?.total || 0;\r\n  };\r\n\r\n  const value: CartContextType = {\r\n    cart,\r\n    addToCart,\r\n    removeFromCart,\r\n    updateQuantity,\r\n    clearCart,\r\n    getCartTotal,\r\n  };\r\n\r\n  return (\r\n    <CartContext.Provider value={value}>\r\n      {children}\r\n    </CartContext.Provider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAKA,MAAM,4BAAc,CAAA,GAAA,4KAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAehB,SAAS,YAAY,KAAkB,EAAE,MAAkB;IACzD,OAAQ,OAAO,IAAI;QACjB,KAAK;YAAY;gBACf,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,mBAAmB,EAAE,GAAG,OAAO,OAAO;gBAE9D,IAAI,CAAC,SAAS,MAAM,YAAY,KAAK,KAAK,YAAY,EAAE;oBACtD,qDAAqD;oBACrD,MAAM,cAAwB;wBAC5B,IAAI,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;wBAC9B,UAAU;wBACV;wBACA;wBACA,YAAY,KAAK,KAAK,GAAG;oBAC3B;oBAEA,MAAM,WAAW,YAAY,UAAU;oBACvC,MAAM,cAAc,MAAM,uBAAuB;oBACjD,MAAM,MAAM,WAAW,MAAM,SAAS;oBAEtC,OAAO;wBACL,OAAO;4BAAC;yBAAY;wBACpB,cAAc,KAAK,YAAY;wBAC/B;wBACA;wBACA;wBACA,OAAO,WAAW,cAAc;oBAClC;gBACF;gBAEA,uBAAuB;gBACvB,MAAM,oBAAoB,MAAM,KAAK,CAAC,SAAS,CAC7C,CAAA,WAAY,SAAS,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,IAAI,SAAS,mBAAmB,KAAK;gBAGnF,IAAI;gBAEJ,IAAI,qBAAqB,GAAG;oBAC1B,uBAAuB;oBACvB,WAAW,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,UAAU,QACpC,UAAU,oBACN;4BACE,GAAG,QAAQ;4BACX,UAAU,SAAS,QAAQ,GAAG;4BAC9B,YAAY,CAAC,SAAS,QAAQ,GAAG,QAAQ,IAAI,SAAS,QAAQ,CAAC,KAAK;wBACtE,IACA;gBAER,OAAO;oBACL,eAAe;oBACf,MAAM,cAAwB;wBAC5B,IAAI,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;wBAC9B,UAAU;wBACV;wBACA;wBACA,YAAY,KAAK,KAAK,GAAG;oBAC3B;oBACA,WAAW;2BAAI,MAAM,KAAK;wBAAE;qBAAY;gBAC1C;gBAEA,MAAM,WAAW,SAAS,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE;gBACvE,MAAM,MAAM,WAAW;gBAEvB,OAAO;oBACL,GAAG,KAAK;oBACR,OAAO;oBACP;oBACA;oBACA,OAAO,WAAW,MAAM,WAAW,GAAG;gBACxC;YACF;QAEA,KAAK;YAAe;gBAClB,IAAI,CAAC,OAAO,OAAO;gBAEnB,MAAM,WAAW,MAAM,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,OAAO,OAAO,CAAC,MAAM;gBAE7E,IAAI,SAAS,MAAM,KAAK,GAAG;oBACzB,OAAO;gBACT;gBAEA,MAAM,WAAW,SAAS,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE;gBACvE,MAAM,MAAM,WAAW;gBAEvB,OAAO;oBACL,GAAG,KAAK;oBACR,OAAO;oBACP;oBACA;oBACA,OAAO,WAAW,MAAM,WAAW,GAAG;gBACxC;YACF;QAEA,KAAK;YAAmB;gBACtB,IAAI,CAAC,OAAO,OAAO;gBAEnB,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,OAAO;gBAE3C,IAAI,YAAY,GAAG;oBACjB,OAAO,YAAY,OAAO;wBAAE,MAAM;wBAAe,SAAS;4BAAE;wBAAO;oBAAE;gBACvE;gBAEA,MAAM,WAAW,MAAM,KAAK,CAAC,GAAG,CAAC,CAAA,OAC/B,KAAK,EAAE,KAAK,SACR;wBACE,GAAG,IAAI;wBACP;wBACA,YAAY,KAAK,QAAQ,CAAC,KAAK,GAAG;oBACpC,IACA;gBAGN,MAAM,WAAW,SAAS,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE;gBACvE,MAAM,MAAM,WAAW;gBAEvB,OAAO;oBACL,GAAG,KAAK;oBACR,OAAO;oBACP;oBACA;oBACA,OAAO,WAAW,MAAM,WAAW,GAAG;gBACxC;YACF;QAEA,KAAK;YACH,OAAO;QAET,KAAK;YACH,OAAO,OAAO,OAAO;QAEvB;YACE,OAAO;IACX;AACF;AAMO,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAC1D,MAAM,CAAC,MAAM,SAAS,GAAG,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAE,aAAa;IAEjD,uCAAuC;IACvC,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,YAAY,aAAa,OAAO,CAAC;YACvC,IAAI,WAAW;gBACb,IAAI;oBACF,MAAM,aAAa,KAAK,KAAK,CAAC;oBAC9B,SAAS;wBAAE,MAAM;wBAAa,SAAS;oBAAW;gBACpD,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACzD;YACF;QACF;iCAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,MAAM;gBACR,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;YAC9C,OAAO;gBACL,aAAa,UAAU,CAAC;YAC1B;QACF;iCAAG;QAAC;KAAK;IAET,MAAM,YAAY,CAAC,MAAgB,UAAkB;QACnD,SAAS;YAAE,MAAM;YAAY,SAAS;gBAAE;gBAAM;gBAAU;YAAoB;QAAE;IAChF;IAEA,MAAM,iBAAiB,CAAC;QACtB,SAAS;YAAE,MAAM;YAAe,SAAS;gBAAE;YAAO;QAAE;IACtD;IAEA,MAAM,iBAAiB,CAAC,QAAgB;QACtC,SAAS;YAAE,MAAM;YAAmB,SAAS;gBAAE;gBAAQ;YAAS;QAAE;IACpE;IAEA,MAAM,YAAY;QAChB,SAAS;YAAE,MAAM;QAAa;IAChC;IAEA,MAAM,eAAe;QACnB,OAAO,MAAM,SAAS;IACxB;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,4MAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IA3DgB;KAAA", "debugId": null}}, {"offset": {"line": 1038, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/hooks/usePageLoading.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { usePathname } from 'next/navigation';\r\n\r\n/**\r\n * Professional page loading hook like Facebook/YouTube\r\n * - Tracks route changes and initial loads\r\n * - Lightweight and non-blocking\r\n * - Maintains super fast performance\r\n */\r\nexport function usePageLoading() {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isInitialLoad, setIsInitialLoad] = useState(true);\r\n  const pathname = usePathname();\r\n\r\n  // Track initial page load - WAIT FOR AUTH RESOLUTION, NOT ARBITRARY TIMING\r\n  useEffect(() => {\r\n    if (isInitialLoad) {\r\n      setIsLoading(true);\r\n      // Don't set arbitrary timeout - let LoadingProvider control when to hide splash\r\n      // based on actual auth state resolution\r\n    }\r\n  }, [isInitialLoad]);\r\n\r\n  // Method to manually complete initial load (called by LoadingProvider)\r\n  const completeInitialLoad = () => {\r\n    setIsLoading(false);\r\n    setIsInitialLoad(false);\r\n  };\r\n\r\n  // Track route changes - NO LOADING for route changes to maintain speed\r\n  useEffect(() => {\r\n    if (!isInitialLoad) {\r\n      // No loading indicator for route changes - instant navigation\r\n      // This maintains the lightning-fast feel\r\n    }\r\n  }, [pathname, isInitialLoad]);\r\n\r\n  return {\r\n    isLoading,\r\n    isInitialLoad,\r\n    pathname,\r\n    completeInitialLoad\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for manual loading control\r\n */\r\nexport function useManualLoading() {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  const startLoading = () => setIsLoading(true);\r\n  const stopLoading = () => setIsLoading(false);\r\n\r\n  const withLoading = async <T>(asyncFn: () => Promise<T>): Promise<T> => {\r\n    startLoading();\r\n    try {\r\n      const result = await asyncFn();\r\n      return result;\r\n    } finally {\r\n      stopLoading();\r\n    }\r\n  };\r\n\r\n  return {\r\n    isLoading,\r\n    startLoading,\r\n    stopLoading,\r\n    withLoading\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for auth-related loading states\r\n */\r\nexport function useAuthLoading() {\r\n  const [isAuthLoading, setIsAuthLoading] = useState(false);\r\n\r\n  const startAuthLoading = () => setIsAuthLoading(true);\r\n  const stopAuthLoading = () => setIsAuthLoading(false);\r\n\r\n  const withAuthLoading = async <T>(asyncFn: () => Promise<T>): Promise<T> => {\r\n    startAuthLoading();\r\n    try {\r\n      const result = await asyncFn();\r\n      return result;\r\n    } finally {\r\n      // Small delay to show the loading state briefly\r\n      setTimeout(stopAuthLoading, 200);\r\n    }\r\n  };\r\n\r\n  return {\r\n    isAuthLoading,\r\n    startAuthLoading,\r\n    stopAuthLoading,\r\n    withAuthLoading\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;AAHA;;;AAWO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,WAAW,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD;IAE3B,2EAA2E;IAC3E,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,eAAe;gBACjB,aAAa;YACb,gFAAgF;YAChF,wCAAwC;YAC1C;QACF;mCAAG;QAAC;KAAc;IAElB,uEAAuE;IACvE,MAAM,sBAAsB;QAC1B,aAAa;QACb,iBAAiB;IACnB;IAEA,uEAAuE;IACvE,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,eAAe;YAClB,8DAA8D;YAC9D,yCAAyC;YAC3C;QACF;mCAAG;QAAC;QAAU;KAAc;IAE5B,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;GAlCgB;;QAGG,oJAAA,CAAA,cAAW;;;AAoCvB,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,IAAM,aAAa;IACxC,MAAM,cAAc,IAAM,aAAa;IAEvC,MAAM,cAAc,OAAU;QAC5B;QACA,IAAI;YACF,MAAM,SAAS,MAAM;YACrB,OAAO;QACT,SAAU;YACR;QACF;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;IAtBgB;AA2BT,SAAS;;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,mBAAmB,IAAM,iBAAiB;IAChD,MAAM,kBAAkB,IAAM,iBAAiB;IAE/C,MAAM,kBAAkB,OAAU;QAChC;QACA,IAAI;YACF,MAAM,SAAS,MAAM;YACrB,OAAO;QACT,SAAU;YACR,gDAAgD;YAChD,WAAW,iBAAiB;QAC9B;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;IAvBgB", "debugId": null}}, {"offset": {"line": 1149, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/loading/PageLoadingIndicator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\n\r\ninterface FacebookStyleSplashProps {\r\n  isLoading?: boolean;\r\n  duration?: number;\r\n  className?: string;\r\n}\r\n\r\n/**\r\n * Facebook-style splash screen with Tap2Go branding\r\n * - Lightweight and super fast\r\n * - Shows briefly during initial loads only\r\n * - Never blocks app performance\r\n */\r\nexport default function FacebookStyleSplash({\r\n  isLoading = false,\r\n  duration: _duration = 600, // eslint-disable-line @typescript-eslint/no-unused-vars\r\n  className = ''\r\n}: FacebookStyleSplashProps) {\r\n  const [show, setShow] = useState(isLoading);\r\n  const [fadeOut, setFadeOut] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (isLoading) {\r\n      setShow(true);\r\n      setFadeOut(false);\r\n\r\n      // Don't auto-hide - wait for isLoading to become false\r\n      // This ensures we show until auth is fully resolved\r\n    } else {\r\n      // Only hide when loading is explicitly set to false\r\n      setFadeOut(true);\r\n      setTimeout(() => {\r\n        setShow(false);\r\n        setFadeOut(false);\r\n      }, 400); // Slightly longer fade for smoother transition\r\n    }\r\n  }, [isLoading]);\r\n\r\n  if (!show) return null;\r\n\r\n  return (\r\n    <div\r\n      className={`fixed inset-0 z-[9999] flex items-center justify-center transition-opacity duration-300 ${\r\n        fadeOut ? 'opacity-0' : 'opacity-100'\r\n      } ${className}`}\r\n      style={{\r\n        background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)', // Dark gradient like Facebook\r\n      }}\r\n    >\r\n      {/* Tap2Go Logo - Facebook Style */}\r\n      <div className=\"flex flex-col items-center\">\r\n        {/* Main Logo */}\r\n        <div className=\"mb-8\">\r\n          <div\r\n            className=\"w-20 h-20 rounded-2xl flex items-center justify-center shadow-2xl\"\r\n            style={{\r\n              background: 'linear-gradient(135deg, #f3a823 0%, #ef7b06 100%)',\r\n              boxShadow: '0 20px 40px rgba(243, 168, 35, 0.3)'\r\n            }}\r\n          >\r\n            <span className=\"text-white font-bold text-3xl\">T</span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* App Name */}\r\n        <div className=\"text-center mb-12\">\r\n          <h1 className=\"text-white text-2xl font-semibold mb-1\">Tap2Go</h1>\r\n          <p className=\"text-gray-400 text-sm\">Food Delivery</p>\r\n        </div>\r\n\r\n        {/* Loading Indicator - Professional */}\r\n        <div className=\"flex items-center space-x-2\">\r\n          <div className=\"w-2 h-2 bg-orange-500 rounded-full animate-pulse\"></div>\r\n          <div className=\"w-2 h-2 bg-orange-500 rounded-full animate-pulse\" style={{ animationDelay: '0.2s' }}></div>\r\n          <div className=\"w-2 h-2 bg-orange-500 rounded-full animate-pulse\" style={{ animationDelay: '0.4s' }}></div>\r\n        </div>\r\n\r\n        {/* Loading Text */}\r\n        <div className=\"mt-4\">\r\n          <p className=\"text-gray-400 text-sm\">Loading your experience...</p>\r\n        </div>\r\n\r\n        {/* \"from\" text like Facebook */}\r\n        <div className=\"absolute bottom-16 text-center\">\r\n          <p className=\"text-gray-500 text-sm mb-2\">from</p>\r\n          <p className=\"text-gray-400 text-lg font-medium\">Tap2Go Team</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\n/**\r\n * Lightweight top progress bar only (like YouTube/GitHub)\r\n */\r\nexport function TopProgressBar({ \r\n  isLoading = false, \r\n  className = '' \r\n}: { isLoading?: boolean; className?: string }) {\r\n  const [progress, setProgress] = useState(0);\r\n  const [show, setShow] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (isLoading) {\r\n      setShow(true);\r\n      setProgress(0);\r\n\r\n      // Fast progress animation\r\n      const progressInterval = setInterval(() => {\r\n        setProgress(prev => {\r\n          if (prev >= 85) {\r\n            clearInterval(progressInterval);\r\n            return 85;\r\n          }\r\n          return prev + Math.random() * 20;\r\n        });\r\n      }, 100);\r\n\r\n      return () => clearInterval(progressInterval);\r\n    } else {\r\n      setProgress(100);\r\n      const hideTimer = setTimeout(() => {\r\n        setShow(false);\r\n        setProgress(0);\r\n      }, 300);\r\n\r\n      return () => clearTimeout(hideTimer);\r\n    }\r\n  }, [isLoading]);\r\n\r\n  if (!show) return null;\r\n\r\n  return (\r\n    <div className={`fixed top-0 left-0 right-0 z-[9999] h-1 ${className}`}>\r\n      <div \r\n        className=\"h-full bg-gradient-to-r from-orange-500 via-orange-400 to-orange-600 transition-all duration-200 ease-out\"\r\n        style={{ \r\n          width: `${progress}%`,\r\n          boxShadow: '0 0 8px rgba(243, 168, 35, 0.6)'\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\n/**\r\n * Minimal loading dot (like Twitter)\r\n */\r\nexport function LoadingDot({ \r\n  isLoading = false,\r\n  size = 'sm'\r\n}: { isLoading?: boolean; size?: 'sm' | 'md' | 'lg' }) {\r\n  if (!isLoading) return null;\r\n\r\n  const sizeClasses = {\r\n    sm: 'w-4 h-4',\r\n    md: 'w-6 h-6', \r\n    lg: 'w-8 h-8'\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed top-4 right-4 z-[9999]\">\r\n      <div className=\"bg-white/95 backdrop-blur-sm p-2 rounded-full shadow-lg border border-gray-100\">\r\n        <div className={`${sizeClasses[size]} animate-spin rounded-full border-2 border-orange-500 border-t-transparent`} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;;;AAFA;;AAgBe,SAAS,oBAAoB,EAC1C,YAAY,KAAK,EACjB,UAAU,YAAY,GAAG,EACzB,YAAY,EAAE,EACW;;IACzB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,WAAW;gBACb,QAAQ;gBACR,WAAW;YAEX,uDAAuD;YACvD,oDAAoD;YACtD,OAAO;gBACL,oDAAoD;gBACpD,WAAW;gBACX;qDAAW;wBACT,QAAQ;wBACR,WAAW;oBACb;oDAAG,MAAM,+CAA+C;YAC1D;QACF;wCAAG;QAAC;KAAU;IAEd,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,4MAAC;QACC,WAAW,CAAC,wFAAwF,EAClG,UAAU,cAAc,cACzB,CAAC,EAAE,WAAW;QACf,OAAO;YACL,YAAY;QACd;kBAGA,cAAA,4MAAC;YAAI,WAAU;;8BAEb,4MAAC;oBAAI,WAAU;8BACb,cAAA,4MAAC;wBACC,WAAU;wBACV,OAAO;4BACL,YAAY;4BACZ,WAAW;wBACb;kCAEA,cAAA,4MAAC;4BAAK,WAAU;sCAAgC;;;;;;;;;;;;;;;;8BAKpD,4MAAC;oBAAI,WAAU;;sCACb,4MAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,4MAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAIvC,4MAAC;oBAAI,WAAU;;sCACb,4MAAC;4BAAI,WAAU;;;;;;sCACf,4MAAC;4BAAI,WAAU;4BAAmD,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;sCAClG,4MAAC;4BAAI,WAAU;4BAAmD,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;;;;;;;8BAIpG,4MAAC;oBAAI,WAAU;8BACb,cAAA,4MAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;8BAIvC,4MAAC;oBAAI,WAAU;;sCACb,4MAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAC1C,4MAAC;4BAAE,WAAU;sCAAoC;;;;;;;;;;;;;;;;;;;;;;;AAK3D;GA7EwB;KAAA;AAkFjB,SAAS,eAAe,EAC7B,YAAY,KAAK,EACjB,YAAY,EAAE,EAC8B;;IAC5C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,WAAW;gBACb,QAAQ;gBACR,YAAY;gBAEZ,0BAA0B;gBAC1B,MAAM,mBAAmB;iEAAY;wBACnC;yEAAY,CAAA;gCACV,IAAI,QAAQ,IAAI;oCACd,cAAc;oCACd,OAAO;gCACT;gCACA,OAAO,OAAO,KAAK,MAAM,KAAK;4BAChC;;oBACF;gEAAG;gBAEH;gDAAO,IAAM,cAAc;;YAC7B,OAAO;gBACL,YAAY;gBACZ,MAAM,YAAY;0DAAW;wBAC3B,QAAQ;wBACR,YAAY;oBACd;yDAAG;gBAEH;gDAAO,IAAM,aAAa;;YAC5B;QACF;mCAAG;QAAC;KAAU;IAEd,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,4MAAC;QAAI,WAAW,CAAC,wCAAwC,EAAE,WAAW;kBACpE,cAAA,4MAAC;YACC,WAAU;YACV,OAAO;gBACL,OAAO,GAAG,SAAS,CAAC,CAAC;gBACrB,WAAW;YACb;;;;;;;;;;;AAIR;IAhDgB;MAAA;AAqDT,SAAS,WAAW,EACzB,YAAY,KAAK,EACjB,OAAO,IAAI,EACwC;IACnD,IAAI,CAAC,WAAW,OAAO;IAEvB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,4MAAC;QAAI,WAAU;kBACb,cAAA,4MAAC;YAAI,WAAU;sBACb,cAAA,4MAAC;gBAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,0EAA0E,CAAC;;;;;;;;;;;;;;;;AAIxH;MAnBgB", "debugId": null}}, {"offset": {"line": 1442, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/loading/LoadingProvider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { createContext, useContext } from 'react';\r\nimport { usePageLoading, useManualLoading, useAuthLoading } from '@/hooks/usePageLoading';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { AuthContextType } from '@/types';\r\nimport FacebookStyleSplash, { TopProgressBar, LoadingDot } from './PageLoadingIndicator';\r\n\r\ninterface LoadingContextType {\r\n  // Page loading\r\n  isPageLoading: boolean;\r\n  isInitialLoad: boolean;\r\n  \r\n  // Manual loading\r\n  isManualLoading: boolean;\r\n  startLoading: () => void;\r\n  stopLoading: () => void;\r\n  withLoading: <T>(asyncFn: () => Promise<T>) => Promise<T>;\r\n  \r\n  // Auth loading\r\n  isAuthLoading: boolean;\r\n  startAuthLoading: () => void;\r\n  stopAuthLoading: () => void;\r\n  withAuthLoading: <T>(asyncFn: () => Promise<T>) => Promise<T>;\r\n}\r\n\r\nconst LoadingContext = createContext<LoadingContextType | undefined>(undefined);\r\n\r\nexport function useLoading() {\r\n  const context = useContext(LoadingContext);\r\n  if (context === undefined) {\r\n    throw new Error('useLoading must be used within a LoadingProvider');\r\n  }\r\n  return context;\r\n}\r\n\r\ninterface LoadingProviderProps {\r\n  children: React.ReactNode;\r\n  variant?: 'facebook' | 'progress' | 'dot' | 'minimal';\r\n  showInitialLoad?: boolean;\r\n}\r\n\r\n/**\r\n * Professional loading provider with Facebook-style splash screen\r\n * - Manages all loading states\r\n * - Lightweight and super fast\r\n * - Multiple loading variants\r\n */\r\nexport default function LoadingProvider({\r\n  children,\r\n  variant = 'facebook',\r\n  showInitialLoad = true\r\n}: LoadingProviderProps) {\r\n  const pageLoading = usePageLoading();\r\n  const manualLoading = useManualLoading();\r\n  const authLoading = useAuthLoading();\r\n  const auth = useAuth() as AuthContextType & { isInitialized?: boolean };\r\n\r\n  const value: LoadingContextType = {\r\n    // Page loading\r\n    isPageLoading: pageLoading.isLoading,\r\n    isInitialLoad: pageLoading.isInitialLoad,\r\n    \r\n    // Manual loading\r\n    isManualLoading: manualLoading.isLoading,\r\n    startLoading: manualLoading.startLoading,\r\n    stopLoading: manualLoading.stopLoading,\r\n    withLoading: manualLoading.withLoading,\r\n    \r\n    // Auth loading\r\n    isAuthLoading: authLoading.isAuthLoading,\r\n    startAuthLoading: authLoading.startAuthLoading,\r\n    stopAuthLoading: authLoading.stopAuthLoading,\r\n    withAuthLoading: authLoading.withAuthLoading,\r\n  };\r\n\r\n  // PROFESSIONAL AUTH-AWARE SPLASH SCREEN LOGIC\r\n  // Wait for ACTUAL auth resolution, not arbitrary timing\r\n  React.useEffect(() => {\r\n    // Once auth is initialized and we're not in initial load anymore, complete the page loading\r\n    if (auth.isInitialized && !auth.loading && pageLoading.isInitialLoad) {\r\n      // Small delay to ensure smooth transition without flashing\r\n      const timer = setTimeout(() => {\r\n        pageLoading.completeInitialLoad();\r\n      }, 300); // Just enough to prevent flash, but not arbitrary long delay\r\n\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [auth.isInitialized, auth.loading, pageLoading.isInitialLoad, pageLoading.completeInitialLoad, pageLoading]);\r\n\r\n  // Determine if any loading is active\r\n  const isAnyLoading = pageLoading.isLoading || manualLoading.isLoading || authLoading.isAuthLoading;\r\n  const shouldShowInitialLoad = showInitialLoad && pageLoading.isInitialLoad;\r\n\r\n  // FIXED: Show splash screen until auth is ACTUALLY resolved\r\n  // This prevents layout shifts by ensuring we don't show content until auth state is determined\r\n  const shouldShowSplash = shouldShowInitialLoad && (!auth.isInitialized || auth.loading);\r\n\r\n  return (\r\n    <LoadingContext.Provider value={value}>\r\n      {/* Render appropriate loading indicator */}\r\n      {variant === 'facebook' && (\r\n        <FacebookStyleSplash\r\n          isLoading={shouldShowSplash} // Show until auth and page loading complete\r\n          duration={2000} // Fallback duration\r\n        />\r\n      )}\r\n\r\n      {variant === 'progress' && (\r\n        <TopProgressBar isLoading={isAnyLoading} />\r\n      )}\r\n\r\n      {variant === 'dot' && (\r\n        <LoadingDot isLoading={isAnyLoading} size=\"md\" />\r\n      )}\r\n\r\n      {variant === 'minimal' && isAnyLoading && (\r\n        <div className=\"fixed top-4 left-4 z-[9999]\">\r\n          <div className=\"flex items-center space-x-2 bg-white/95 backdrop-blur-sm px-3 py-1.5 rounded-full shadow-sm border border-gray-100\">\r\n            <div className=\"w-3 h-3 bg-orange-500 rounded-full animate-pulse\"></div>\r\n            <span className=\"text-xs text-gray-600 font-medium\">Loading</span>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {children}\r\n    </LoadingContext.Provider>\r\n  );\r\n}\r\n\r\n/**\r\n * Quick loading components for specific use cases\r\n */\r\nexport function QuickProgressBar() {\r\n  const { isPageLoading, isManualLoading } = useLoading();\r\n  return <TopProgressBar isLoading={isPageLoading || isManualLoading} />;\r\n}\r\n\r\nexport function QuickLoadingDot() {\r\n  const { isAuthLoading } = useLoading();\r\n  return <LoadingDot isLoading={isAuthLoading} size=\"sm\" />;\r\n}\r\n\r\n/**\r\n * Loading wrapper for async operations\r\n */\r\nexport function LoadingWrapper({ \r\n  children, \r\n  isLoading,\r\n  variant = 'inline'\r\n}: { \r\n  children: React.ReactNode; \r\n  isLoading: boolean;\r\n  variant?: 'inline' | 'overlay' | 'minimal';\r\n}) {\r\n  if (!isLoading) return <>{children}</>;\r\n\r\n  if (variant === 'overlay') {\r\n    return (\r\n      <div className=\"relative\">\r\n        {children}\r\n        <div className=\"absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <div className=\"w-5 h-5 animate-spin rounded-full border-2 border-orange-500 border-t-transparent\"></div>\r\n            <span className=\"text-sm text-gray-600\">Loading...</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (variant === 'minimal') {\r\n    return (\r\n      <div className=\"flex items-center justify-center py-4\">\r\n        <div className=\"w-4 h-4 animate-spin rounded-full border-2 border-orange-500 border-t-transparent\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Inline variant\r\n  return (\r\n    <div className=\"flex items-center space-x-2 py-2\">\r\n      <div className=\"w-4 h-4 animate-spin rounded-full border-2 border-orange-500 border-t-transparent\"></div>\r\n      <span className=\"text-sm text-gray-600\">Loading...</span>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;AAEA;;;AANA;;;;;AA0BA,MAAM,+BAAiB,CAAA,GAAA,4KAAA,CAAA,gBAAa,AAAD,EAAkC;AAE9D,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAoBD,SAAS,gBAAgB,EACtC,QAAQ,EACR,UAAU,UAAU,EACpB,kBAAkB,IAAI,EACD;;IACrB,MAAM,cAAc,CAAA,GAAA,gJAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,gBAAgB,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD;IACrC,MAAM,cAAc,CAAA,GAAA,gJAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,OAAO,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD;IAEnB,MAAM,QAA4B;QAChC,eAAe;QACf,eAAe,YAAY,SAAS;QACpC,eAAe,YAAY,aAAa;QAExC,iBAAiB;QACjB,iBAAiB,cAAc,SAAS;QACxC,cAAc,cAAc,YAAY;QACxC,aAAa,cAAc,WAAW;QACtC,aAAa,cAAc,WAAW;QAEtC,eAAe;QACf,eAAe,YAAY,aAAa;QACxC,kBAAkB,YAAY,gBAAgB;QAC9C,iBAAiB,YAAY,eAAe;QAC5C,iBAAiB,YAAY,eAAe;IAC9C;IAEA,8CAA8C;IAC9C,wDAAwD;IACxD,4KAAA,CAAA,UAAK,CAAC,SAAS;qCAAC;YACd,4FAA4F;YAC5F,IAAI,KAAK,aAAa,IAAI,CAAC,KAAK,OAAO,IAAI,YAAY,aAAa,EAAE;gBACpE,2DAA2D;gBAC3D,MAAM,QAAQ;uDAAW;wBACvB,YAAY,mBAAmB;oBACjC;sDAAG,MAAM,6DAA6D;gBAEtE;iDAAO,IAAM,aAAa;;YAC5B;QACF;oCAAG;QAAC,KAAK,aAAa;QAAE,KAAK,OAAO;QAAE,YAAY,aAAa;QAAE,YAAY,mBAAmB;QAAE;KAAY;IAE9G,qCAAqC;IACrC,MAAM,eAAe,YAAY,SAAS,IAAI,cAAc,SAAS,IAAI,YAAY,aAAa;IAClG,MAAM,wBAAwB,mBAAmB,YAAY,aAAa;IAE1E,4DAA4D;IAC5D,+FAA+F;IAC/F,MAAM,mBAAmB,yBAAyB,CAAC,CAAC,KAAK,aAAa,IAAI,KAAK,OAAO;IAEtF,qBACE,4MAAC,eAAe,QAAQ;QAAC,OAAO;;YAE7B,YAAY,4BACX,4MAAC,uKAAA,CAAA,UAAmB;gBAClB,WAAW;gBACX,UAAU;;;;;;YAIb,YAAY,4BACX,4MAAC,uKAAA,CAAA,iBAAc;gBAAC,WAAW;;;;;;YAG5B,YAAY,uBACX,4MAAC,uKAAA,CAAA,aAAU;gBAAC,WAAW;gBAAc,MAAK;;;;;;YAG3C,YAAY,aAAa,8BACxB,4MAAC;gBAAI,WAAU;0BACb,cAAA,4MAAC;oBAAI,WAAU;;sCACb,4MAAC;4BAAI,WAAU;;;;;;sCACf,4MAAC;4BAAK,WAAU;sCAAoC;;;;;;;;;;;;;;;;;YAKzD;;;;;;;AAGP;IAhFwB;;QAKF,gJAAA,CAAA,iBAAc;QACZ,gJAAA,CAAA,mBAAgB;QAClB,gJAAA,CAAA,iBAAc;QACrB,iJAAA,CAAA,UAAO;;;KARE;AAqFjB,SAAS;;IACd,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG;IAC3C,qBAAO,4MAAC,uKAAA,CAAA,iBAAc;QAAC,WAAW,iBAAiB;;;;;;AACrD;IAHgB;;QAC6B;;;MAD7B;AAKT,SAAS;;IACd,MAAM,EAAE,aAAa,EAAE,GAAG;IAC1B,qBAAO,4MAAC,uKAAA,CAAA,aAAU;QAAC,WAAW;QAAe,MAAK;;;;;;AACpD;IAHgB;;QACY;;;MADZ;AAQT,SAAS,eAAe,EAC7B,QAAQ,EACR,SAAS,EACT,UAAU,QAAQ,EAKnB;IACC,IAAI,CAAC,WAAW,qBAAO;kBAAG;;IAE1B,IAAI,YAAY,WAAW;QACzB,qBACE,4MAAC;YAAI,WAAU;;gBACZ;8BACD,4MAAC;oBAAI,WAAU;8BACb,cAAA,4MAAC;wBAAI,WAAU;;0CACb,4MAAC;gCAAI,WAAU;;;;;;0CACf,4MAAC;gCAAK,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;IAKlD;IAEA,IAAI,YAAY,WAAW;QACzB,qBACE,4MAAC;YAAI,WAAU;sBACb,cAAA,4MAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,iBAAiB;IACjB,qBACE,4MAAC;QAAI,WAAU;;0BACb,4MAAC;gBAAI,WAAU;;;;;;0BACf,4MAAC;gBAAK,WAAU;0BAAwB;;;;;;;;;;;;AAG9C;MAxCgB", "debugId": null}}, {"offset": {"line": 1735, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/utils/serialization.ts"], "sourcesContent": ["/**\r\n * Serialization utilities for Firebase data in Redux\r\n * Handles Firebase Timestamps and other non-serializable data\r\n */\r\n\r\nimport { Timestamp } from 'firebase/firestore';\r\nimport { User } from '@/types';\r\n\r\n/**\r\n * Serialize Firebase user data for Redux\r\n */\r\nexport const serializeUser = (user: User | null): User | null => {\r\n  if (!user) return null;\r\n\r\n  return {\r\n    ...user,\r\n    createdAt: user.createdAt instanceof Timestamp ? user.createdAt.toDate() : user.createdAt,\r\n    updatedAt: user.updatedAt instanceof Timestamp ? user.updatedAt.toDate() : user.updatedAt,\r\n  } as User;\r\n};\r\n\r\n/**\r\n * Serialize Firebase document data for Redux\r\n */\r\nexport const serializeFirebaseDoc = (doc: Record<string, unknown> | null): Record<string, unknown> | null => {\r\n  if (!doc) return null;\r\n  \r\n  const serialized = { ...doc };\r\n  \r\n  // Convert all Timestamp fields to ISO strings\r\n  Object.keys(serialized).forEach(key => {\r\n    if (serialized[key] instanceof Timestamp) {\r\n      serialized[key] = serialized[key].toDate().toISOString();\r\n    }\r\n  });\r\n  \r\n  return serialized;\r\n};\r\n\r\n/**\r\n * Deserialize ISO strings back to Date objects when needed\r\n */\r\nexport const deserializeTimestamps = (data: Record<string, unknown> | null): Record<string, unknown> | null => {\r\n  if (!data) return null;\r\n  \r\n  const deserialized = { ...data };\r\n  \r\n  // Convert ISO strings back to Date objects for common timestamp fields\r\n  const timestampFields = ['createdAt', 'updatedAt', 'lastLoginAt', 'emailVerified'];\r\n  \r\n  timestampFields.forEach(field => {\r\n    if (deserialized[field] && typeof deserialized[field] === 'string') {\r\n      deserialized[field] = new Date(deserialized[field]);\r\n    }\r\n  });\r\n  \r\n  return deserialized;\r\n};\r\n\r\n/**\r\n * Check if a value is serializable for Redux\r\n */\r\nexport const isSerializable = (value: unknown): boolean => {\r\n  if (value === null || value === undefined) return true;\r\n  if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') return true;\r\n  if (value instanceof Date) return false;\r\n  if (value instanceof Timestamp) return false;\r\n  if (Array.isArray(value)) return value.every(isSerializable);\r\n  if (typeof value === 'object') return Object.values(value).every(isSerializable);\r\n  return false;\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;AAED;AAAA;;AAMO,MAAM,gBAAgB,CAAC;IAC5B,IAAI,CAAC,MAAM,OAAO;IAElB,OAAO;QACL,GAAG,IAAI;QACP,WAAW,KAAK,SAAS,YAAY,sKAAA,CAAA,YAAS,GAAG,KAAK,SAAS,CAAC,MAAM,KAAK,KAAK,SAAS;QACzF,WAAW,KAAK,SAAS,YAAY,sKAAA,CAAA,YAAS,GAAG,KAAK,SAAS,CAAC,MAAM,KAAK,KAAK,SAAS;IAC3F;AACF;AAKO,MAAM,uBAAuB,CAAC;IACnC,IAAI,CAAC,KAAK,OAAO;IAEjB,MAAM,aAAa;QAAE,GAAG,GAAG;IAAC;IAE5B,8CAA8C;IAC9C,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,CAAA;QAC9B,IAAI,UAAU,CAAC,IAAI,YAAY,sKAAA,CAAA,YAAS,EAAE;YACxC,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,WAAW;QACxD;IACF;IAEA,OAAO;AACT;AAKO,MAAM,wBAAwB,CAAC;IACpC,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,eAAe;QAAE,GAAG,IAAI;IAAC;IAE/B,uEAAuE;IACvE,MAAM,kBAAkB;QAAC;QAAa;QAAa;QAAe;KAAgB;IAElF,gBAAgB,OAAO,CAAC,CAAA;QACtB,IAAI,YAAY,CAAC,MAAM,IAAI,OAAO,YAAY,CAAC,MAAM,KAAK,UAAU;YAClE,YAAY,CAAC,MAAM,GAAG,IAAI,KAAK,YAAY,CAAC,MAAM;QACpD;IACF;IAEA,OAAO;AACT;AAKO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;IAClD,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY,OAAO,UAAU,WAAW,OAAO;IACjG,IAAI,iBAAiB,MAAM,OAAO;IAClC,IAAI,iBAAiB,sKAAA,CAAA,YAAS,EAAE,OAAO;IACvC,IAAI,MAAM,OAAO,CAAC,QAAQ,OAAO,MAAM,KAAK,CAAC;IAC7C,IAAI,OAAO,UAAU,UAAU,OAAO,OAAO,MAAM,CAAC,OAAO,KAAK,CAAC;IACjE,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1805, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/slices/authSlice.ts"], "sourcesContent": ["/**\r\n * Auth Slice - Integrates with existing Firebase Auth Context\r\n * Maintains compatibility with your enterprise-grade auth system\r\n */\r\n\r\nimport { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\r\nimport { User } from '@/types';\r\nimport { serializeUser } from '../utils/serialization';\r\n\r\n// Auth state interface\r\nexport interface AuthState {\r\n  user: User | null;\r\n  isAuthenticated: boolean;\r\n  loading: boolean;\r\n  error: string | null;\r\n  isInitialized: boolean;\r\n  // Multi-tab sync state\r\n  lastSyncTimestamp: number;\r\n  // Token management\r\n  tokenRefreshInProgress: boolean;\r\n  // Session management\r\n  sessionExpiry: number | null;\r\n}\r\n\r\n// Initial state\r\nconst initialState: AuthState = {\r\n  user: null,\r\n  isAuthenticated: false,\r\n  loading: true,\r\n  error: null,\r\n  isInitialized: false,\r\n  lastSyncTimestamp: 0,\r\n  tokenRefreshInProgress: false,\r\n  sessionExpiry: null,\r\n};\r\n\r\n// Async thunks for auth operations\r\nexport const initializeAuth = createAsyncThunk(\r\n  'auth/initialize',\r\n  async (_, { rejectWithValue }) => {\r\n    try {\r\n      // This will be called by your existing AuthContext\r\n      // Just return success - the actual auth logic stays in AuthContext\r\n      return { initialized: true };\r\n    } catch {\r\n      return rejectWithValue('Failed to initialize auth');\r\n    }\r\n  }\r\n);\r\n\r\nexport const signInUser = createAsyncThunk(\r\n  'auth/signIn',\r\n  async ({ email }: { email: string; password: string }, { rejectWithValue }) => {\r\n    try {\r\n      // This will be handled by your existing AuthContext\r\n      // Redux just tracks the state changes\r\n      return { email };\r\n    } catch (error) {\r\n      return rejectWithValue(error instanceof Error ? error.message : 'Sign in failed');\r\n    }\r\n  }\r\n);\r\n\r\nexport const signUpUser = createAsyncThunk(\r\n  'auth/signUp',\r\n  async ({\r\n    email,\r\n    name,\r\n    role\r\n  }: {\r\n    email: string;\r\n    password: string;\r\n    name: string;\r\n    role: User['role'];\r\n  }, { rejectWithValue }) => {\r\n    try {\r\n      // This will be handled by your existing AuthContext\r\n      return { email, name, role };\r\n    } catch (error) {\r\n      return rejectWithValue(error instanceof Error ? error.message : 'Sign up failed');\r\n    }\r\n  }\r\n);\r\n\r\nexport const signOutUser = createAsyncThunk(\r\n  'auth/signOut',\r\n  async (_, { rejectWithValue }) => {\r\n    try {\r\n      // This will be handled by your existing AuthContext\r\n      return {};\r\n    } catch (error) {\r\n      return rejectWithValue(error instanceof Error ? error.message : 'Sign out failed');\r\n    }\r\n  }\r\n);\r\n\r\nexport const updateUserProfile = createAsyncThunk(\r\n  'auth/updateProfile',\r\n  async (data: Partial<User>, { rejectWithValue }) => {\r\n    try {\r\n      // This will be handled by your existing AuthContext\r\n      return data;\r\n    } catch (error) {\r\n      return rejectWithValue(error instanceof Error ? error.message : 'Profile update failed');\r\n    }\r\n  }\r\n);\r\n\r\n// Auth slice\r\nconst authSlice = createSlice({\r\n  name: 'auth',\r\n  initialState,\r\n  reducers: {\r\n    // Sync actions called by AuthContext\r\n    setUser: (state, action: PayloadAction<User | null>) => {\r\n      // Serialize user data to handle Firebase Timestamps\r\n      state.user = action.payload ? serializeUser(action.payload) : null;\r\n      state.isAuthenticated = !!action.payload;\r\n      state.error = null;\r\n      state.lastSyncTimestamp = Date.now();\r\n    },\r\n    \r\n    setLoading: (state, action: PayloadAction<boolean>) => {\r\n      state.loading = action.payload;\r\n    },\r\n    \r\n    setError: (state, action: PayloadAction<string | null>) => {\r\n      state.error = action.payload;\r\n    },\r\n    \r\n    setInitialized: (state, action: PayloadAction<boolean>) => {\r\n      state.isInitialized = action.payload;\r\n    },\r\n    \r\n    // Multi-tab synchronization\r\n    syncAuthState: (state, action: PayloadAction<{ user: User | null; timestamp: number }>) => {\r\n      const { user, timestamp } = action.payload;\r\n      // Only update if this is a newer change\r\n      if (timestamp > state.lastSyncTimestamp) {\r\n        // Serialize user data to handle Firebase Timestamps\r\n        state.user = user ? serializeUser(user) : null;\r\n        state.isAuthenticated = !!user;\r\n        state.lastSyncTimestamp = timestamp;\r\n      }\r\n    },\r\n    \r\n    // Token management\r\n    setTokenRefreshInProgress: (state, action: PayloadAction<boolean>) => {\r\n      state.tokenRefreshInProgress = action.payload;\r\n    },\r\n    \r\n    setSessionExpiry: (state, action: PayloadAction<number | null>) => {\r\n      state.sessionExpiry = action.payload;\r\n    },\r\n    \r\n    // Clear auth state\r\n    clearAuth: (state) => {\r\n      state.user = null;\r\n      state.isAuthenticated = false;\r\n      state.error = null;\r\n      state.sessionExpiry = null;\r\n      state.lastSyncTimestamp = Date.now();\r\n    },\r\n    \r\n    // Update user data without full re-auth\r\n    updateUserData: (state, action: PayloadAction<Partial<User>>) => {\r\n      if (state.user) {\r\n        const updatedUser = { ...state.user, ...action.payload };\r\n        state.user = serializeUser(updatedUser);\r\n        state.lastSyncTimestamp = Date.now();\r\n      }\r\n    },\r\n  },\r\n  \r\n  extraReducers: (builder) => {\r\n    // Initialize auth\r\n    builder\r\n      .addCase(initializeAuth.pending, (state) => {\r\n        state.loading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(initializeAuth.fulfilled, (state) => {\r\n        state.loading = false;\r\n        state.isInitialized = true;\r\n      })\r\n      .addCase(initializeAuth.rejected, (state, action) => {\r\n        state.loading = false;\r\n        state.error = action.payload as string;\r\n      });\r\n    \r\n    // Sign in\r\n    builder\r\n      .addCase(signInUser.pending, (state) => {\r\n        state.loading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(signInUser.fulfilled, (state) => {\r\n        state.loading = false;\r\n        // User will be set by AuthContext via setUser action\r\n      })\r\n      .addCase(signInUser.rejected, (state, action) => {\r\n        state.loading = false;\r\n        state.error = action.payload as string;\r\n      });\r\n    \r\n    // Sign up\r\n    builder\r\n      .addCase(signUpUser.pending, (state) => {\r\n        state.loading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(signUpUser.fulfilled, (state) => {\r\n        state.loading = false;\r\n        // User will be set by AuthContext via setUser action\r\n      })\r\n      .addCase(signUpUser.rejected, (state, action) => {\r\n        state.loading = false;\r\n        state.error = action.payload as string;\r\n      });\r\n    \r\n    // Sign out\r\n    builder\r\n      .addCase(signOutUser.pending, (state) => {\r\n        state.loading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(signOutUser.fulfilled, (state) => {\r\n        state.user = null;\r\n        state.isAuthenticated = false;\r\n        state.loading = false;\r\n        state.sessionExpiry = null;\r\n        state.lastSyncTimestamp = Date.now();\r\n      })\r\n      .addCase(signOutUser.rejected, (state, action) => {\r\n        state.loading = false;\r\n        state.error = action.payload as string;\r\n      });\r\n    \r\n    // Update profile\r\n    builder\r\n      .addCase(updateUserProfile.pending, (state) => {\r\n        state.loading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(updateUserProfile.fulfilled, (state, action) => {\r\n        state.loading = false;\r\n        if (state.user) {\r\n          const updatedUser = { ...state.user, ...action.payload };\r\n          state.user = serializeUser(updatedUser);\r\n          state.lastSyncTimestamp = Date.now();\r\n        }\r\n      })\r\n      .addCase(updateUserProfile.rejected, (state, action) => {\r\n        state.loading = false;\r\n        state.error = action.payload as string;\r\n      });\r\n  },\r\n});\r\n\r\n// Export actions\r\nexport const {\r\n  setUser,\r\n  setLoading,\r\n  setError,\r\n  setInitialized,\r\n  syncAuthState,\r\n  setTokenRefreshInProgress,\r\n  setSessionExpiry,\r\n  clearAuth,\r\n  updateUserData,\r\n} = authSlice.actions;\r\n\r\n// Selectors\r\nexport const selectAuth = (state: { auth: AuthState }) => state.auth;\r\nexport const selectUser = (state: { auth: AuthState }) => state.auth.user;\r\nexport const selectIsAuthenticated = (state: { auth: AuthState }) => state.auth.isAuthenticated;\r\nexport const selectAuthLoading = (state: { auth: AuthState }) => state.auth.loading;\r\nexport const selectAuthError = (state: { auth: AuthState }) => state.auth.error;\r\nexport const selectIsInitialized = (state: { auth: AuthState }) => state.auth.isInitialized;\r\n\r\nexport default authSlice;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;AAED;AAEA;;;AAiBA,gBAAgB;AAChB,MAAM,eAA0B;IAC9B,MAAM;IACN,iBAAiB;IACjB,SAAS;IACT,OAAO;IACP,eAAe;IACf,mBAAmB;IACnB,wBAAwB;IACxB,eAAe;AACjB;AAGO,MAAM,iBAAiB,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EAC3C,mBACA,OAAO,GAAG,EAAE,eAAe,EAAE;IAC3B,IAAI;QACF,mDAAmD;QACnD,mEAAmE;QACnE,OAAO;YAAE,aAAa;QAAK;IAC7B,EAAE,OAAM;QACN,OAAO,gBAAgB;IACzB;AACF;AAGK,MAAM,aAAa,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EACvC,eACA,OAAO,EAAE,KAAK,EAAuC,EAAE,EAAE,eAAe,EAAE;IACxE,IAAI;QACF,oDAAoD;QACpD,sCAAsC;QACtC,OAAO;YAAE;QAAM;IACjB,EAAE,OAAO,OAAO;QACd,OAAO,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAClE;AACF;AAGK,MAAM,aAAa,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EACvC,eACA,OAAO,EACL,KAAK,EACL,IAAI,EACJ,IAAI,EAML,EAAE,EAAE,eAAe,EAAE;IACpB,IAAI;QACF,oDAAoD;QACpD,OAAO;YAAE;YAAO;YAAM;QAAK;IAC7B,EAAE,OAAO,OAAO;QACd,OAAO,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAClE;AACF;AAGK,MAAM,cAAc,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EACxC,gBACA,OAAO,GAAG,EAAE,eAAe,EAAE;IAC3B,IAAI;QACF,oDAAoD;QACpD,OAAO,CAAC;IACV,EAAE,OAAO,OAAO;QACd,OAAO,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAClE;AACF;AAGK,MAAM,oBAAoB,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EAC9C,sBACA,OAAO,MAAqB,EAAE,eAAe,EAAE;IAC7C,IAAI;QACF,oDAAoD;QACpD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAClE;AACF;AAGF,aAAa;AACb,MAAM,YAAY,CAAA,GAAA,6MAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN;IACA,UAAU;QACR,qCAAqC;QACrC,SAAS,CAAC,OAAO;YACf,oDAAoD;YACpD,MAAM,IAAI,GAAG,OAAO,OAAO,GAAG,CAAA,GAAA,wJAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,OAAO,IAAI;YAC9D,MAAM,eAAe,GAAG,CAAC,CAAC,OAAO,OAAO;YACxC,MAAM,KAAK,GAAG;YACd,MAAM,iBAAiB,GAAG,KAAK,GAAG;QACpC;QAEA,YAAY,CAAC,OAAO;YAClB,MAAM,OAAO,GAAG,OAAO,OAAO;QAChC;QAEA,UAAU,CAAC,OAAO;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEA,gBAAgB,CAAC,OAAO;YACtB,MAAM,aAAa,GAAG,OAAO,OAAO;QACtC;QAEA,4BAA4B;QAC5B,eAAe,CAAC,OAAO;YACrB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,OAAO;YAC1C,wCAAwC;YACxC,IAAI,YAAY,MAAM,iBAAiB,EAAE;gBACvC,oDAAoD;gBACpD,MAAM,IAAI,GAAG,OAAO,CAAA,GAAA,wJAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;gBAC1C,MAAM,eAAe,GAAG,CAAC,CAAC;gBAC1B,MAAM,iBAAiB,GAAG;YAC5B;QACF;QAEA,mBAAmB;QACnB,2BAA2B,CAAC,OAAO;YACjC,MAAM,sBAAsB,GAAG,OAAO,OAAO;QAC/C;QAEA,kBAAkB,CAAC,OAAO;YACxB,MAAM,aAAa,GAAG,OAAO,OAAO;QACtC;QAEA,mBAAmB;QACnB,WAAW,CAAC;YACV,MAAM,IAAI,GAAG;YACb,MAAM,eAAe,GAAG;YACxB,MAAM,KAAK,GAAG;YACd,MAAM,aAAa,GAAG;YACtB,MAAM,iBAAiB,GAAG,KAAK,GAAG;QACpC;QAEA,wCAAwC;QACxC,gBAAgB,CAAC,OAAO;YACtB,IAAI,MAAM,IAAI,EAAE;gBACd,MAAM,cAAc;oBAAE,GAAG,MAAM,IAAI;oBAAE,GAAG,OAAO,OAAO;gBAAC;gBACvD,MAAM,IAAI,GAAG,CAAA,GAAA,wJAAA,CAAA,gBAAa,AAAD,EAAE;gBAC3B,MAAM,iBAAiB,GAAG,KAAK,GAAG;YACpC;QACF;IACF;IAEA,eAAe,CAAC;QACd,kBAAkB;QAClB,QACG,OAAO,CAAC,eAAe,OAAO,EAAE,CAAC;YAChC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,eAAe,SAAS,EAAE,CAAC;YAClC,MAAM,OAAO,GAAG;YAChB,MAAM,aAAa,GAAG;QACxB,GACC,OAAO,CAAC,eAAe,QAAQ,EAAE,CAAC,OAAO;YACxC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEF,UAAU;QACV,QACG,OAAO,CAAC,WAAW,OAAO,EAAE,CAAC;YAC5B,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,WAAW,SAAS,EAAE,CAAC;YAC9B,MAAM,OAAO,GAAG;QAChB,qDAAqD;QACvD,GACC,OAAO,CAAC,WAAW,QAAQ,EAAE,CAAC,OAAO;YACpC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEF,UAAU;QACV,QACG,OAAO,CAAC,WAAW,OAAO,EAAE,CAAC;YAC5B,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,WAAW,SAAS,EAAE,CAAC;YAC9B,MAAM,OAAO,GAAG;QAChB,qDAAqD;QACvD,GACC,OAAO,CAAC,WAAW,QAAQ,EAAE,CAAC,OAAO;YACpC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEF,WAAW;QACX,QACG,OAAO,CAAC,YAAY,OAAO,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,YAAY,SAAS,EAAE,CAAC;YAC/B,MAAM,IAAI,GAAG;YACb,MAAM,eAAe,GAAG;YACxB,MAAM,OAAO,GAAG;YAChB,MAAM,aAAa,GAAG;YACtB,MAAM,iBAAiB,GAAG,KAAK,GAAG;QACpC,GACC,OAAO,CAAC,YAAY,QAAQ,EAAE,CAAC,OAAO;YACrC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEF,iBAAiB;QACjB,QACG,OAAO,CAAC,kBAAkB,OAAO,EAAE,CAAC;YACnC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,kBAAkB,SAAS,EAAE,CAAC,OAAO;YAC5C,MAAM,OAAO,GAAG;YAChB,IAAI,MAAM,IAAI,EAAE;gBACd,MAAM,cAAc;oBAAE,GAAG,MAAM,IAAI;oBAAE,GAAG,OAAO,OAAO;gBAAC;gBACvD,MAAM,IAAI,GAAG,CAAA,GAAA,wJAAA,CAAA,gBAAa,AAAD,EAAE;gBAC3B,MAAM,iBAAiB,GAAG,KAAK,GAAG;YACpC;QACF,GACC,OAAO,CAAC,kBAAkB,QAAQ,EAAE,CAAC,OAAO;YAC3C,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;IACJ;AACF;AAGO,MAAM,EACX,OAAO,EACP,UAAU,EACV,QAAQ,EACR,cAAc,EACd,aAAa,EACb,yBAAyB,EACzB,gBAAgB,EAChB,SAAS,EACT,cAAc,EACf,GAAG,UAAU,OAAO;AAGd,MAAM,aAAa,CAAC,QAA+B,MAAM,IAAI;AAC7D,MAAM,aAAa,CAAC,QAA+B,MAAM,IAAI,CAAC,IAAI;AAClE,MAAM,wBAAwB,CAAC,QAA+B,MAAM,IAAI,CAAC,eAAe;AACxF,MAAM,oBAAoB,CAAC,QAA+B,MAAM,IAAI,CAAC,OAAO;AAC5E,MAAM,kBAAkB,CAAC,QAA+B,MAAM,IAAI,CAAC,KAAK;AACxE,MAAM,sBAAsB,CAAC,QAA+B,MAAM,IAAI,CAAC,aAAa;uCAE5E", "debugId": null}}, {"offset": {"line": 2041, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/slices/uiSlice.ts"], "sourcesContent": ["/**\r\n * UI Slice - Global UI state management\r\n * Handles theme, modals, notifications, loading states, etc.\r\n */\r\n\r\nimport { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\n\r\n// UI state interfaces\r\nexport interface Notification {\r\n  id: string;\r\n  type: 'success' | 'error' | 'warning' | 'info';\r\n  title: string;\r\n  message: string;\r\n  duration?: number;\r\n  actions?: Array<{\r\n    label: string;\r\n    action: string;\r\n    style?: 'primary' | 'secondary' | 'danger';\r\n  }>;\r\n  createdAt: number;\r\n}\r\n\r\nexport interface Modal {\r\n  id: string;\r\n  type: string;\r\n  props?: Record<string, unknown>;\r\n  isOpen: boolean;\r\n}\r\n\r\nexport interface UIState {\r\n  // Theme\r\n  theme: 'light' | 'dark' | 'system';\r\n  \r\n  // Layout\r\n  sidebarOpen: boolean;\r\n  mobileMenuOpen: boolean;\r\n  \r\n  // Loading states\r\n  globalLoading: boolean;\r\n  pageLoading: boolean;\r\n  \r\n  // Notifications\r\n  notifications: Notification[];\r\n  \r\n  // Modals\r\n  modals: Modal[];\r\n  \r\n  // Search\r\n  searchQuery: string;\r\n  searchResults: Array<{ id: string; type: string; title: string; description?: string }>;\r\n  searchLoading: boolean;\r\n\r\n  // Filters\r\n  activeFilters: Record<string, unknown>;\r\n  \r\n  // Location\r\n  currentLocation: {\r\n    lat: number;\r\n    lng: number;\r\n    address: string;\r\n  } | null;\r\n  \r\n  // App state\r\n  isOnline: boolean;\r\n  lastActivity: number;\r\n  \r\n  // Panel-specific UI state\r\n  adminPanel: {\r\n    activeTab: string;\r\n    selectedItems: string[];\r\n    bulkActions: boolean;\r\n  };\r\n  \r\n  vendorPanel: {\r\n    activeSection: string;\r\n    menuEditMode: boolean;\r\n    selectedOrders: string[];\r\n  };\r\n  \r\n  driverPanel: {\r\n    mapView: 'normal' | 'satellite' | 'terrain';\r\n    showTraffic: boolean;\r\n    autoAcceptOrders: boolean;\r\n  };\r\n  \r\n  // Customer app\r\n  customerApp: {\r\n    viewMode: 'list' | 'grid' | 'map';\r\n    sortBy: 'distance' | 'rating' | 'delivery_time' | 'price';\r\n    showFilters: boolean;\r\n  };\r\n}\r\n\r\n// Initial state\r\nconst initialState: UIState = {\r\n  theme: 'light',\r\n  sidebarOpen: false,\r\n  mobileMenuOpen: false,\r\n  globalLoading: false,\r\n  pageLoading: false,\r\n  notifications: [],\r\n  modals: [],\r\n  searchQuery: '',\r\n  searchResults: [],\r\n  searchLoading: false,\r\n  activeFilters: {},\r\n  currentLocation: null,\r\n  isOnline: true,\r\n  lastActivity: Date.now(),\r\n  adminPanel: {\r\n    activeTab: 'dashboard',\r\n    selectedItems: [],\r\n    bulkActions: false,\r\n  },\r\n  vendorPanel: {\r\n    activeSection: 'dashboard',\r\n    menuEditMode: false,\r\n    selectedOrders: [],\r\n  },\r\n  driverPanel: {\r\n    mapView: 'normal',\r\n    showTraffic: true,\r\n    autoAcceptOrders: false,\r\n  },\r\n  customerApp: {\r\n    viewMode: 'list',\r\n    sortBy: 'distance',\r\n    showFilters: false,\r\n  },\r\n};\r\n\r\n// UI slice\r\nconst uiSlice = createSlice({\r\n  name: 'ui',\r\n  initialState,\r\n  reducers: {\r\n    // Theme\r\n    setTheme: (state, action: PayloadAction<UIState['theme']>) => {\r\n      state.theme = action.payload;\r\n    },\r\n    \r\n    toggleTheme: (state) => {\r\n      state.theme = state.theme === 'light' ? 'dark' : 'light';\r\n    },\r\n    \r\n    // Layout\r\n    setSidebarOpen: (state, action: PayloadAction<boolean>) => {\r\n      state.sidebarOpen = action.payload;\r\n    },\r\n    \r\n    toggleSidebar: (state) => {\r\n      state.sidebarOpen = !state.sidebarOpen;\r\n    },\r\n    \r\n    setMobileMenuOpen: (state, action: PayloadAction<boolean>) => {\r\n      state.mobileMenuOpen = action.payload;\r\n    },\r\n    \r\n    toggleMobileMenu: (state) => {\r\n      state.mobileMenuOpen = !state.mobileMenuOpen;\r\n    },\r\n    \r\n    // Loading states\r\n    setGlobalLoading: (state, action: PayloadAction<boolean>) => {\r\n      state.globalLoading = action.payload;\r\n    },\r\n    \r\n    setPageLoading: (state, action: PayloadAction<boolean>) => {\r\n      state.pageLoading = action.payload;\r\n    },\r\n    \r\n    // Notifications\r\n    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'createdAt'>>) => {\r\n      const notification: Notification = {\r\n        ...action.payload,\r\n        id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\r\n        createdAt: Date.now(),\r\n      };\r\n      state.notifications.unshift(notification);\r\n      \r\n      // Limit to 10 notifications\r\n      if (state.notifications.length > 10) {\r\n        state.notifications = state.notifications.slice(0, 10);\r\n      }\r\n    },\r\n    \r\n    removeNotification: (state, action: PayloadAction<string>) => {\r\n      state.notifications = state.notifications.filter(n => n.id !== action.payload);\r\n    },\r\n    \r\n    clearNotifications: (state) => {\r\n      state.notifications = [];\r\n    },\r\n    \r\n    // Modals\r\n    openModal: (state, action: PayloadAction<{ type: string; props?: Record<string, unknown> }>) => {\r\n      const { type, props } = action.payload;\r\n      const modal: Modal = {\r\n        id: `modal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\r\n        type,\r\n        props,\r\n        isOpen: true,\r\n      };\r\n      state.modals.push(modal);\r\n    },\r\n    \r\n    closeModal: (state, action: PayloadAction<string>) => {\r\n      const modalIndex = state.modals.findIndex(m => m.id === action.payload);\r\n      if (modalIndex >= 0) {\r\n        state.modals[modalIndex].isOpen = false;\r\n      }\r\n    },\r\n    \r\n    removeModal: (state, action: PayloadAction<string>) => {\r\n      state.modals = state.modals.filter(m => m.id !== action.payload);\r\n    },\r\n    \r\n    closeAllModals: (state) => {\r\n      state.modals.forEach(modal => {\r\n        modal.isOpen = false;\r\n      });\r\n    },\r\n    \r\n    // Search\r\n    setSearchQuery: (state, action: PayloadAction<string>) => {\r\n      state.searchQuery = action.payload;\r\n    },\r\n    \r\n    setSearchResults: (state, action: PayloadAction<Array<{ id: string; type: string; title: string; description?: string }>>) => {\r\n      state.searchResults = action.payload;\r\n    },\r\n    \r\n    setSearchLoading: (state, action: PayloadAction<boolean>) => {\r\n      state.searchLoading = action.payload;\r\n    },\r\n    \r\n    clearSearch: (state) => {\r\n      state.searchQuery = '';\r\n      state.searchResults = [];\r\n      state.searchLoading = false;\r\n    },\r\n    \r\n    // Filters\r\n    setFilter: (state, action: PayloadAction<{ key: string; value: unknown }>) => {\r\n      const { key, value } = action.payload;\r\n      state.activeFilters[key] = value;\r\n    },\r\n    \r\n    removeFilter: (state, action: PayloadAction<string>) => {\r\n      delete state.activeFilters[action.payload];\r\n    },\r\n    \r\n    clearFilters: (state) => {\r\n      state.activeFilters = {};\r\n    },\r\n    \r\n    // Location\r\n    setCurrentLocation: (state, action: PayloadAction<UIState['currentLocation']>) => {\r\n      state.currentLocation = action.payload;\r\n    },\r\n    \r\n    // App state\r\n    setOnlineStatus: (state, action: PayloadAction<boolean>) => {\r\n      state.isOnline = action.payload;\r\n    },\r\n    \r\n    updateLastActivity: (state) => {\r\n      state.lastActivity = Date.now();\r\n    },\r\n    \r\n    // Admin panel\r\n    setAdminActiveTab: (state, action: PayloadAction<string>) => {\r\n      state.adminPanel.activeTab = action.payload;\r\n    },\r\n    \r\n    setAdminSelectedItems: (state, action: PayloadAction<string[]>) => {\r\n      state.adminPanel.selectedItems = action.payload;\r\n    },\r\n    \r\n    toggleAdminBulkActions: (state) => {\r\n      state.adminPanel.bulkActions = !state.adminPanel.bulkActions;\r\n      if (!state.adminPanel.bulkActions) {\r\n        state.adminPanel.selectedItems = [];\r\n      }\r\n    },\r\n    \r\n    // Vendor panel\r\n    setVendorActiveSection: (state, action: PayloadAction<string>) => {\r\n      state.vendorPanel.activeSection = action.payload;\r\n    },\r\n    \r\n    setVendorMenuEditMode: (state, action: PayloadAction<boolean>) => {\r\n      state.vendorPanel.menuEditMode = action.payload;\r\n    },\r\n    \r\n    setVendorSelectedOrders: (state, action: PayloadAction<string[]>) => {\r\n      state.vendorPanel.selectedOrders = action.payload;\r\n    },\r\n    \r\n    // Driver panel\r\n    setDriverMapView: (state, action: PayloadAction<UIState['driverPanel']['mapView']>) => {\r\n      state.driverPanel.mapView = action.payload;\r\n    },\r\n    \r\n    toggleDriverTraffic: (state) => {\r\n      state.driverPanel.showTraffic = !state.driverPanel.showTraffic;\r\n    },\r\n    \r\n    setDriverAutoAccept: (state, action: PayloadAction<boolean>) => {\r\n      state.driverPanel.autoAcceptOrders = action.payload;\r\n    },\r\n    \r\n    // Customer app\r\n    setCustomerViewMode: (state, action: PayloadAction<UIState['customerApp']['viewMode']>) => {\r\n      state.customerApp.viewMode = action.payload;\r\n    },\r\n    \r\n    setCustomerSortBy: (state, action: PayloadAction<UIState['customerApp']['sortBy']>) => {\r\n      state.customerApp.sortBy = action.payload;\r\n    },\r\n    \r\n    toggleCustomerFilters: (state) => {\r\n      state.customerApp.showFilters = !state.customerApp.showFilters;\r\n    },\r\n    \r\n    // Utility actions\r\n    showSuccessNotification: (state, action: PayloadAction<{ title: string; message: string }>) => {\r\n      const { title, message } = action.payload;\r\n      uiSlice.caseReducers.addNotification(state, {\r\n        type: 'ui/addNotification',\r\n        payload: {\r\n          type: 'success',\r\n          title,\r\n          message,\r\n          duration: 5000,\r\n        },\r\n      });\r\n    },\r\n    \r\n    showErrorNotification: (state, action: PayloadAction<{ title: string; message: string }>) => {\r\n      const { title, message } = action.payload;\r\n      uiSlice.caseReducers.addNotification(state, {\r\n        type: 'ui/addNotification',\r\n        payload: {\r\n          type: 'error',\r\n          title,\r\n          message,\r\n          duration: 8000,\r\n        },\r\n      });\r\n    },\r\n    \r\n    showWarningNotification: (state, action: PayloadAction<{ title: string; message: string }>) => {\r\n      const { title, message } = action.payload;\r\n      uiSlice.caseReducers.addNotification(state, {\r\n        type: 'ui/addNotification',\r\n        payload: {\r\n          type: 'warning',\r\n          title,\r\n          message,\r\n          duration: 6000,\r\n        },\r\n      });\r\n    },\r\n    \r\n    showInfoNotification: (state, action: PayloadAction<{ title: string; message: string }>) => {\r\n      const { title, message } = action.payload;\r\n      uiSlice.caseReducers.addNotification(state, {\r\n        type: 'ui/addNotification',\r\n        payload: {\r\n          type: 'info',\r\n          title,\r\n          message,\r\n          duration: 5000,\r\n        },\r\n      });\r\n    },\r\n  },\r\n});\r\n\r\n// Export actions\r\nexport const {\r\n  setTheme,\r\n  toggleTheme,\r\n  setSidebarOpen,\r\n  toggleSidebar,\r\n  setMobileMenuOpen,\r\n  toggleMobileMenu,\r\n  setGlobalLoading,\r\n  setPageLoading,\r\n  addNotification,\r\n  removeNotification,\r\n  clearNotifications,\r\n  openModal,\r\n  closeModal,\r\n  removeModal,\r\n  closeAllModals,\r\n  setSearchQuery,\r\n  setSearchResults,\r\n  setSearchLoading,\r\n  clearSearch,\r\n  setFilter,\r\n  removeFilter,\r\n  clearFilters,\r\n  setCurrentLocation,\r\n  setOnlineStatus,\r\n  updateLastActivity,\r\n  setAdminActiveTab,\r\n  setAdminSelectedItems,\r\n  toggleAdminBulkActions,\r\n  setVendorActiveSection,\r\n  setVendorMenuEditMode,\r\n  setVendorSelectedOrders,\r\n  setDriverMapView,\r\n  toggleDriverTraffic,\r\n  setDriverAutoAccept,\r\n  setCustomerViewMode,\r\n  setCustomerSortBy,\r\n  toggleCustomerFilters,\r\n  showSuccessNotification,\r\n  showErrorNotification,\r\n  showWarningNotification,\r\n  showInfoNotification,\r\n} = uiSlice.actions;\r\n\r\n// Selectors\r\nexport const selectTheme = (state: { ui: UIState }) => state.ui.theme;\r\nexport const selectSidebarOpen = (state: { ui: UIState }) => state.ui.sidebarOpen;\r\nexport const selectNotifications = (state: { ui: UIState }) => state.ui.notifications;\r\nexport const selectModals = (state: { ui: UIState }) => state.ui.modals;\r\nexport const selectGlobalLoading = (state: { ui: UIState }) => state.ui.globalLoading;\r\nexport const selectCurrentLocation = (state: { ui: UIState }) => state.ui.currentLocation;\r\nexport const selectIsOnline = (state: { ui: UIState }) => state.ui.isOnline;\r\n\r\nexport default uiSlice;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAED;;AAwFA,gBAAgB;AAChB,MAAM,eAAwB;IAC5B,OAAO;IACP,aAAa;IACb,gBAAgB;IAChB,eAAe;IACf,aAAa;IACb,eAAe,EAAE;IACjB,QAAQ,EAAE;IACV,aAAa;IACb,eAAe,EAAE;IACjB,eAAe;IACf,eAAe,CAAC;IAChB,iBAAiB;IACjB,UAAU;IACV,cAAc,KAAK,GAAG;IACtB,YAAY;QACV,WAAW;QACX,eAAe,EAAE;QACjB,aAAa;IACf;IACA,aAAa;QACX,eAAe;QACf,cAAc;QACd,gBAAgB,EAAE;IACpB;IACA,aAAa;QACX,SAAS;QACT,aAAa;QACb,kBAAkB;IACpB;IACA,aAAa;QACX,UAAU;QACV,QAAQ;QACR,aAAa;IACf;AACF;AAEA,WAAW;AACX,MAAM,UAAU,CAAA,GAAA,6MAAA,CAAA,cAAW,AAAD,EAAE;IAC1B,MAAM;IACN;IACA,UAAU;QACR,QAAQ;QACR,UAAU,CAAC,OAAO;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEA,aAAa,CAAC;YACZ,MAAM,KAAK,GAAG,MAAM,KAAK,KAAK,UAAU,SAAS;QACnD;QAEA,SAAS;QACT,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QAEA,eAAe,CAAC;YACd,MAAM,WAAW,GAAG,CAAC,MAAM,WAAW;QACxC;QAEA,mBAAmB,CAAC,OAAO;YACzB,MAAM,cAAc,GAAG,OAAO,OAAO;QACvC;QAEA,kBAAkB,CAAC;YACjB,MAAM,cAAc,GAAG,CAAC,MAAM,cAAc;QAC9C;QAEA,iBAAiB;QACjB,kBAAkB,CAAC,OAAO;YACxB,MAAM,aAAa,GAAG,OAAO,OAAO;QACtC;QAEA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QAEA,gBAAgB;QAChB,iBAAiB,CAAC,OAAO;YACvB,MAAM,eAA6B;gBACjC,GAAG,OAAO,OAAO;gBACjB,IAAI,CAAC,aAAa,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBAC3E,WAAW,KAAK,GAAG;YACrB;YACA,MAAM,aAAa,CAAC,OAAO,CAAC;YAE5B,4BAA4B;YAC5B,IAAI,MAAM,aAAa,CAAC,MAAM,GAAG,IAAI;gBACnC,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,KAAK,CAAC,GAAG;YACrD;QACF;QAEA,oBAAoB,CAAC,OAAO;YAC1B,MAAM,aAAa,GAAG,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,OAAO;QAC/E;QAEA,oBAAoB,CAAC;YACnB,MAAM,aAAa,GAAG,EAAE;QAC1B;QAEA,SAAS;QACT,WAAW,CAAC,OAAO;YACjB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,OAAO;YACtC,MAAM,QAAe;gBACnB,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;gBACpE;gBACA;gBACA,QAAQ;YACV;YACA,MAAM,MAAM,CAAC,IAAI,CAAC;QACpB;QAEA,YAAY,CAAC,OAAO;YAClB,MAAM,aAAa,MAAM,MAAM,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,OAAO;YACtE,IAAI,cAAc,GAAG;gBACnB,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG;YACpC;QACF;QAEA,aAAa,CAAC,OAAO;YACnB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,OAAO;QACjE;QAEA,gBAAgB,CAAC;YACf,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA;gBACnB,MAAM,MAAM,GAAG;YACjB;QACF;QAEA,SAAS;QACT,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QAEA,kBAAkB,CAAC,OAAO;YACxB,MAAM,aAAa,GAAG,OAAO,OAAO;QACtC;QAEA,kBAAkB,CAAC,OAAO;YACxB,MAAM,aAAa,GAAG,OAAO,OAAO;QACtC;QAEA,aAAa,CAAC;YACZ,MAAM,WAAW,GAAG;YACpB,MAAM,aAAa,GAAG,EAAE;YACxB,MAAM,aAAa,GAAG;QACxB;QAEA,UAAU;QACV,WAAW,CAAC,OAAO;YACjB,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,OAAO,OAAO;YACrC,MAAM,aAAa,CAAC,IAAI,GAAG;QAC7B;QAEA,cAAc,CAAC,OAAO;YACpB,OAAO,MAAM,aAAa,CAAC,OAAO,OAAO,CAAC;QAC5C;QAEA,cAAc,CAAC;YACb,MAAM,aAAa,GAAG,CAAC;QACzB;QAEA,WAAW;QACX,oBAAoB,CAAC,OAAO;YAC1B,MAAM,eAAe,GAAG,OAAO,OAAO;QACxC;QAEA,YAAY;QACZ,iBAAiB,CAAC,OAAO;YACvB,MAAM,QAAQ,GAAG,OAAO,OAAO;QACjC;QAEA,oBAAoB,CAAC;YACnB,MAAM,YAAY,GAAG,KAAK,GAAG;QAC/B;QAEA,cAAc;QACd,mBAAmB,CAAC,OAAO;YACzB,MAAM,UAAU,CAAC,SAAS,GAAG,OAAO,OAAO;QAC7C;QAEA,uBAAuB,CAAC,OAAO;YAC7B,MAAM,UAAU,CAAC,aAAa,GAAG,OAAO,OAAO;QACjD;QAEA,wBAAwB,CAAC;YACvB,MAAM,UAAU,CAAC,WAAW,GAAG,CAAC,MAAM,UAAU,CAAC,WAAW;YAC5D,IAAI,CAAC,MAAM,UAAU,CAAC,WAAW,EAAE;gBACjC,MAAM,UAAU,CAAC,aAAa,GAAG,EAAE;YACrC;QACF;QAEA,eAAe;QACf,wBAAwB,CAAC,OAAO;YAC9B,MAAM,WAAW,CAAC,aAAa,GAAG,OAAO,OAAO;QAClD;QAEA,uBAAuB,CAAC,OAAO;YAC7B,MAAM,WAAW,CAAC,YAAY,GAAG,OAAO,OAAO;QACjD;QAEA,yBAAyB,CAAC,OAAO;YAC/B,MAAM,WAAW,CAAC,cAAc,GAAG,OAAO,OAAO;QACnD;QAEA,eAAe;QACf,kBAAkB,CAAC,OAAO;YACxB,MAAM,WAAW,CAAC,OAAO,GAAG,OAAO,OAAO;QAC5C;QAEA,qBAAqB,CAAC;YACpB,MAAM,WAAW,CAAC,WAAW,GAAG,CAAC,MAAM,WAAW,CAAC,WAAW;QAChE;QAEA,qBAAqB,CAAC,OAAO;YAC3B,MAAM,WAAW,CAAC,gBAAgB,GAAG,OAAO,OAAO;QACrD;QAEA,eAAe;QACf,qBAAqB,CAAC,OAAO;YAC3B,MAAM,WAAW,CAAC,QAAQ,GAAG,OAAO,OAAO;QAC7C;QAEA,mBAAmB,CAAC,OAAO;YACzB,MAAM,WAAW,CAAC,MAAM,GAAG,OAAO,OAAO;QAC3C;QAEA,uBAAuB,CAAC;YACtB,MAAM,WAAW,CAAC,WAAW,GAAG,CAAC,MAAM,WAAW,CAAC,WAAW;QAChE;QAEA,kBAAkB;QAClB,yBAAyB,CAAC,OAAO;YAC/B,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,OAAO,OAAO;YACzC,QAAQ,YAAY,CAAC,eAAe,CAAC,OAAO;gBAC1C,MAAM;gBACN,SAAS;oBACP,MAAM;oBACN;oBACA;oBACA,UAAU;gBACZ;YACF;QACF;QAEA,uBAAuB,CAAC,OAAO;YAC7B,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,OAAO,OAAO;YACzC,QAAQ,YAAY,CAAC,eAAe,CAAC,OAAO;gBAC1C,MAAM;gBACN,SAAS;oBACP,MAAM;oBACN;oBACA;oBACA,UAAU;gBACZ;YACF;QACF;QAEA,yBAAyB,CAAC,OAAO;YAC/B,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,OAAO,OAAO;YACzC,QAAQ,YAAY,CAAC,eAAe,CAAC,OAAO;gBAC1C,MAAM;gBACN,SAAS;oBACP,MAAM;oBACN;oBACA;oBACA,UAAU;gBACZ;YACF;QACF;QAEA,sBAAsB,CAAC,OAAO;YAC5B,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,OAAO,OAAO;YACzC,QAAQ,YAAY,CAAC,eAAe,CAAC,OAAO;gBAC1C,MAAM;gBACN,SAAS;oBACP,MAAM;oBACN;oBACA;oBACA,UAAU;gBACZ;YACF;QACF;IACF;AACF;AAGO,MAAM,EACX,QAAQ,EACR,WAAW,EACX,cAAc,EACd,aAAa,EACb,iBAAiB,EACjB,gBAAgB,EAChB,gBAAgB,EAChB,cAAc,EACd,eAAe,EACf,kBAAkB,EAClB,kBAAkB,EAClB,SAAS,EACT,UAAU,EACV,WAAW,EACX,cAAc,EACd,cAAc,EACd,gBAAgB,EAChB,gBAAgB,EAChB,WAAW,EACX,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,kBAAkB,EAClB,eAAe,EACf,kBAAkB,EAClB,iBAAiB,EACjB,qBAAqB,EACrB,sBAAsB,EACtB,sBAAsB,EACtB,qBAAqB,EACrB,uBAAuB,EACvB,gBAAgB,EAChB,mBAAmB,EACnB,mBAAmB,EACnB,mBAAmB,EACnB,iBAAiB,EACjB,qBAAqB,EACrB,uBAAuB,EACvB,qBAAqB,EACrB,uBAAuB,EACvB,oBAAoB,EACrB,GAAG,QAAQ,OAAO;AAGZ,MAAM,cAAc,CAAC,QAA2B,MAAM,EAAE,CAAC,KAAK;AAC9D,MAAM,oBAAoB,CAAC,QAA2B,MAAM,EAAE,CAAC,WAAW;AAC1E,MAAM,sBAAsB,CAAC,QAA2B,MAAM,EAAE,CAAC,aAAa;AAC9E,MAAM,eAAe,CAAC,QAA2B,MAAM,EAAE,CAAC,MAAM;AAChE,MAAM,sBAAsB,CAAC,QAA2B,MAAM,EAAE,CAAC,aAAa;AAC9E,MAAM,wBAAwB,CAAC,QAA2B,MAAM,EAAE,CAAC,eAAe;AAClF,MAAM,iBAAiB,CAAC,QAA2B,MAAM,EAAE,CAAC,QAAQ;uCAE5D", "debugId": null}}, {"offset": {"line": 2359, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/slices/cartSlice.ts"], "sourcesContent": ["/**\r\n * Cart Slice - Enhanced version of your existing cart logic\r\n * Maintains compatibility with your current cart patterns\r\n */\r\n\r\nimport { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\r\nimport { Cart, CartItem, MenuItem, Address, PaymentMethod } from '@/types';\r\n\r\n// Cart state interface\r\nexport interface CartState {\r\n  cart: Cart | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n  // Enhanced features\r\n  savedCarts: Cart[]; // Multiple saved carts\r\n  recentlyRemoved: CartItem[]; // For undo functionality\r\n  promoCode: string | null;\r\n  promoDiscount: number;\r\n  // Checkout state\r\n  checkoutStep: 'cart' | 'address' | 'payment' | 'confirmation';\r\n  deliveryAddress: Address | null;\r\n  paymentMethod: PaymentMethod | null;\r\n}\r\n\r\n// Initial state\r\nconst initialState: CartState = {\r\n  cart: null,\r\n  loading: false,\r\n  error: null,\r\n  savedCarts: [],\r\n  recentlyRemoved: [],\r\n  promoCode: null,\r\n  promoDiscount: 0,\r\n  checkoutStep: 'cart',\r\n  deliveryAddress: null,\r\n  paymentMethod: null,\r\n};\r\n\r\n// Async thunks\r\nexport const loadCartFromStorage = createAsyncThunk(\r\n  'cart/loadFromStorage',\r\n  async (_, { rejectWithValue }) => {\r\n    try {\r\n      const savedCart = localStorage.getItem('cart');\r\n      if (savedCart) {\r\n        return JSON.parse(savedCart) as Cart;\r\n      }\r\n      return null;\r\n    } catch {\r\n      return rejectWithValue('Failed to load cart from storage');\r\n    }\r\n  }\r\n);\r\n\r\nexport const saveCartToStorage = createAsyncThunk(\r\n  'cart/saveToStorage',\r\n  async (cart: Cart | null, { rejectWithValue }) => {\r\n    try {\r\n      if (cart) {\r\n        localStorage.setItem('cart', JSON.stringify(cart));\r\n      } else {\r\n        localStorage.removeItem('cart');\r\n      }\r\n      return cart;\r\n    } catch {\r\n      return rejectWithValue('Failed to save cart to storage');\r\n    }\r\n  }\r\n);\r\n\r\nexport const validatePromoCode = createAsyncThunk(\r\n  'cart/validatePromoCode',\r\n  async ({ code, cartTotal }: { code: string; cartTotal: number }, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Implement promo code validation API call\r\n      // For now, return mock validation\r\n      const mockPromos: Record<string, number> = {\r\n        'SAVE10': 0.1,\r\n        'SAVE20': 0.2,\r\n        'NEWUSER': 0.15,\r\n      };\r\n      \r\n      const discount = mockPromos[code.toUpperCase()];\r\n      if (discount) {\r\n        return { code, discount, amount: cartTotal * discount };\r\n      } else {\r\n        throw new Error('Invalid promo code');\r\n      }\r\n    } catch (error) {\r\n      return rejectWithValue(error instanceof Error ? error.message : 'Promo code validation failed');\r\n    }\r\n  }\r\n);\r\n\r\n// Helper functions (same logic as your existing cart)\r\nconst calculateCartTotals = (items: CartItem[], deliveryFee: number = 5.99, promoDiscount: number = 0) => {\r\n  const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0);\r\n  const tax = subtotal * 0.08; // 8% tax\r\n  const discountAmount = subtotal * promoDiscount;\r\n  const total = subtotal + deliveryFee + tax - discountAmount;\r\n  \r\n  return {\r\n    subtotal,\r\n    deliveryFee,\r\n    tax,\r\n    discountAmount,\r\n    total,\r\n  };\r\n};\r\n\r\nconst createCartItem = (item: MenuItem, quantity: number, specialInstructions?: string): CartItem => {\r\n  return {\r\n    id: `${item.id}-${Date.now()}`,\r\n    menuItem: item,\r\n    quantity,\r\n    specialInstructions,\r\n    totalPrice: item.price * quantity,\r\n  };\r\n};\r\n\r\n// Cart slice\r\nconst cartSlice = createSlice({\r\n  name: 'cart',\r\n  initialState,\r\n  reducers: {\r\n    // Core cart operations (matching your existing logic)\r\n    addToCart: (state, action: PayloadAction<{ item: MenuItem; quantity: number; specialInstructions?: string }>) => {\r\n      const { item, quantity, specialInstructions } = action.payload;\r\n      \r\n      if (!state.cart || state.cart.restaurantId !== item.restaurantId) {\r\n        // Create new cart or replace if different restaurant\r\n        const newCartItem = createCartItem(item, quantity, specialInstructions);\r\n        const totals = calculateCartTotals([newCartItem], 5.99, state.promoDiscount);\r\n        \r\n        state.cart = {\r\n          items: [newCartItem],\r\n          restaurantId: item.restaurantId,\r\n          ...totals,\r\n        };\r\n      } else {\r\n        // Add to existing cart\r\n        const existingItemIndex = state.cart.items.findIndex(\r\n          cartItem => cartItem.menuItem.id === item.id && \r\n          cartItem.specialInstructions === specialInstructions\r\n        );\r\n        \r\n        if (existingItemIndex >= 0) {\r\n          // Update existing item\r\n          state.cart.items[existingItemIndex].quantity += quantity;\r\n          state.cart.items[existingItemIndex].totalPrice = \r\n            state.cart.items[existingItemIndex].menuItem.price * state.cart.items[existingItemIndex].quantity;\r\n        } else {\r\n          // Add new item\r\n          const newCartItem = createCartItem(item, quantity, specialInstructions);\r\n          state.cart.items.push(newCartItem);\r\n        }\r\n        \r\n        // Recalculate totals\r\n        const totals = calculateCartTotals(state.cart.items, state.cart.deliveryFee, state.promoDiscount);\r\n        Object.assign(state.cart, totals);\r\n      }\r\n      \r\n      state.error = null;\r\n    },\r\n    \r\n    removeFromCart: (state, action: PayloadAction<string>) => {\r\n      if (!state.cart) return;\r\n      \r\n      const itemId = action.payload;\r\n      const itemIndex = state.cart.items.findIndex(item => item.id === itemId);\r\n      \r\n      if (itemIndex >= 0) {\r\n        // Store for undo functionality\r\n        const removedItem = state.cart.items[itemIndex];\r\n        state.recentlyRemoved.unshift(removedItem);\r\n        // Keep only last 5 removed items\r\n        state.recentlyRemoved = state.recentlyRemoved.slice(0, 5);\r\n        \r\n        // Remove item\r\n        state.cart.items.splice(itemIndex, 1);\r\n        \r\n        if (state.cart.items.length === 0) {\r\n          state.cart = null;\r\n          state.promoCode = null;\r\n          state.promoDiscount = 0;\r\n        } else {\r\n          // Recalculate totals\r\n          const totals = calculateCartTotals(state.cart.items, state.cart.deliveryFee, state.promoDiscount);\r\n          Object.assign(state.cart, totals);\r\n        }\r\n      }\r\n    },\r\n    \r\n    updateQuantity: (state, action: PayloadAction<{ itemId: string; quantity: number }>) => {\r\n      if (!state.cart) return;\r\n      \r\n      const { itemId, quantity } = action.payload;\r\n      \r\n      if (quantity <= 0) {\r\n        // Remove item if quantity is 0 or negative\r\n        cartSlice.caseReducers.removeFromCart(state, { type: 'cart/removeFromCart', payload: itemId });\r\n        return;\r\n      }\r\n      \r\n      const itemIndex = state.cart.items.findIndex(item => item.id === itemId);\r\n      if (itemIndex >= 0) {\r\n        state.cart.items[itemIndex].quantity = quantity;\r\n        state.cart.items[itemIndex].totalPrice = \r\n          state.cart.items[itemIndex].menuItem.price * quantity;\r\n        \r\n        // Recalculate totals\r\n        const totals = calculateCartTotals(state.cart.items, state.cart.deliveryFee, state.promoDiscount);\r\n        Object.assign(state.cart, totals);\r\n      }\r\n    },\r\n    \r\n    clearCart: (state) => {\r\n      if (state.cart) {\r\n        // Save current cart to recently removed for potential recovery\r\n        state.recentlyRemoved = [...state.cart.items];\r\n      }\r\n      state.cart = null;\r\n      state.promoCode = null;\r\n      state.promoDiscount = 0;\r\n      state.checkoutStep = 'cart';\r\n      state.deliveryAddress = null;\r\n      state.paymentMethod = null;\r\n    },\r\n    \r\n    // Enhanced features\r\n    undoRemoveItem: (state, action: PayloadAction<number>) => {\r\n      const index = action.payload || 0;\r\n      if (state.recentlyRemoved[index]) {\r\n        const itemToRestore = state.recentlyRemoved[index];\r\n        state.recentlyRemoved.splice(index, 1);\r\n        \r\n        // Add back to cart\r\n        cartSlice.caseReducers.addToCart(state, {\r\n          type: 'cart/addToCart',\r\n          payload: {\r\n            item: itemToRestore.menuItem,\r\n            quantity: itemToRestore.quantity,\r\n            specialInstructions: itemToRestore.specialInstructions,\r\n          },\r\n        });\r\n      }\r\n    },\r\n    \r\n    applyPromoCode: (state, action: PayloadAction<{ code: string; discount: number }>) => {\r\n      const { code, discount } = action.payload;\r\n      state.promoCode = code;\r\n      state.promoDiscount = discount;\r\n      \r\n      if (state.cart) {\r\n        const totals = calculateCartTotals(state.cart.items, state.cart.deliveryFee, discount);\r\n        Object.assign(state.cart, totals);\r\n      }\r\n    },\r\n    \r\n    removePromoCode: (state) => {\r\n      state.promoCode = null;\r\n      state.promoDiscount = 0;\r\n      \r\n      if (state.cart) {\r\n        const totals = calculateCartTotals(state.cart.items, state.cart.deliveryFee, 0);\r\n        Object.assign(state.cart, totals);\r\n      }\r\n    },\r\n    \r\n    // Checkout flow\r\n    setCheckoutStep: (state, action: PayloadAction<CartState['checkoutStep']>) => {\r\n      state.checkoutStep = action.payload;\r\n    },\r\n    \r\n    setDeliveryAddress: (state, action: PayloadAction<Address>) => {\r\n      state.deliveryAddress = action.payload;\r\n    },\r\n\r\n    setPaymentMethod: (state, action: PayloadAction<PaymentMethod>) => {\r\n      state.paymentMethod = action.payload;\r\n    },\r\n    \r\n    // Save cart for later\r\n    saveCurrentCart: (state, action: PayloadAction<string>) => {\r\n      if (state.cart) {\r\n        const savedCart = {\r\n          ...state.cart,\r\n          savedAt: new Date().toISOString(),\r\n          name: action.payload,\r\n        };\r\n        state.savedCarts.push(savedCart as Cart);\r\n      }\r\n    },\r\n    \r\n    loadSavedCart: (state, action: PayloadAction<number>) => {\r\n      const savedCart = state.savedCarts[action.payload];\r\n      if (savedCart) {\r\n        state.cart = { ...savedCart };\r\n        // Remove saved timestamp and name\r\n        delete (state.cart as Cart & { savedAt?: string; name?: string }).savedAt;\r\n        delete (state.cart as Cart & { savedAt?: string; name?: string }).name;\r\n      }\r\n    },\r\n    \r\n    deleteSavedCart: (state, action: PayloadAction<number>) => {\r\n      state.savedCarts.splice(action.payload, 1);\r\n    },\r\n    \r\n    setError: (state, action: PayloadAction<string | null>) => {\r\n      state.error = action.payload;\r\n    },\r\n    \r\n    setLoading: (state, action: PayloadAction<boolean>) => {\r\n      state.loading = action.payload;\r\n    },\r\n  },\r\n  \r\n  extraReducers: (builder) => {\r\n    // Load from storage\r\n    builder\r\n      .addCase(loadCartFromStorage.fulfilled, (state, action) => {\r\n        state.cart = action.payload;\r\n        state.loading = false;\r\n      })\r\n      .addCase(loadCartFromStorage.rejected, (state, action) => {\r\n        state.error = action.payload as string;\r\n        state.loading = false;\r\n      });\r\n    \r\n    // Save to storage\r\n    builder\r\n      .addCase(saveCartToStorage.fulfilled, (state) => {\r\n        state.loading = false;\r\n      })\r\n      .addCase(saveCartToStorage.rejected, (state, action) => {\r\n        state.error = action.payload as string;\r\n        state.loading = false;\r\n      });\r\n    \r\n    // Promo code validation\r\n    builder\r\n      .addCase(validatePromoCode.pending, (state) => {\r\n        state.loading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(validatePromoCode.fulfilled, (state, action) => {\r\n        const { code, discount } = action.payload;\r\n        state.promoCode = code;\r\n        state.promoDiscount = discount;\r\n        state.loading = false;\r\n        \r\n        if (state.cart) {\r\n          const totals = calculateCartTotals(state.cart.items, state.cart.deliveryFee, discount);\r\n          Object.assign(state.cart, totals);\r\n        }\r\n      })\r\n      .addCase(validatePromoCode.rejected, (state, action) => {\r\n        state.error = action.payload as string;\r\n        state.loading = false;\r\n      });\r\n  },\r\n});\r\n\r\n// Export actions\r\nexport const {\r\n  addToCart,\r\n  removeFromCart,\r\n  updateQuantity,\r\n  clearCart,\r\n  undoRemoveItem,\r\n  applyPromoCode,\r\n  removePromoCode,\r\n  setCheckoutStep,\r\n  setDeliveryAddress,\r\n  setPaymentMethod,\r\n  saveCurrentCart,\r\n  loadSavedCart,\r\n  deleteSavedCart,\r\n  setError,\r\n  setLoading,\r\n} = cartSlice.actions;\r\n\r\n// Selectors\r\nexport const selectCart = (state: { cart: CartState }) => state.cart.cart;\r\nexport const selectCartItems = (state: { cart: CartState }) => state.cart.cart?.items || [];\r\nexport const selectCartTotal = (state: { cart: CartState }) => state.cart.cart?.total || 0;\r\nexport const selectCartItemCount = (state: { cart: CartState }) => \r\n  state.cart.cart?.items.reduce((sum, item) => sum + item.quantity, 0) || 0;\r\nexport const selectCartLoading = (state: { cart: CartState }) => state.cart.loading;\r\nexport const selectCartError = (state: { cart: CartState }) => state.cart.error;\r\nexport const selectPromoCode = (state: { cart: CartState }) => state.cart.promoCode;\r\nexport const selectCheckoutStep = (state: { cart: CartState }) => state.cart.checkoutStep;\r\n\r\nexport default cartSlice;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAED;;AAmBA,gBAAgB;AAChB,MAAM,eAA0B;IAC9B,MAAM;IACN,SAAS;IACT,OAAO;IACP,YAAY,EAAE;IACd,iBAAiB,EAAE;IACnB,WAAW;IACX,eAAe;IACf,cAAc;IACd,iBAAiB;IACjB,eAAe;AACjB;AAGO,MAAM,sBAAsB,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EAChD,wBACA,OAAO,GAAG,EAAE,eAAe,EAAE;IAC3B,IAAI;QACF,MAAM,YAAY,aAAa,OAAO,CAAC;QACvC,IAAI,WAAW;YACb,OAAO,KAAK,KAAK,CAAC;QACpB;QACA,OAAO;IACT,EAAE,OAAM;QACN,OAAO,gBAAgB;IACzB;AACF;AAGK,MAAM,oBAAoB,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EAC9C,sBACA,OAAO,MAAmB,EAAE,eAAe,EAAE;IAC3C,IAAI;QACF,IAAI,MAAM;YACR,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;QAC9C,OAAO;YACL,aAAa,UAAU,CAAC;QAC1B;QACA,OAAO;IACT,EAAE,OAAM;QACN,OAAO,gBAAgB;IACzB;AACF;AAGK,MAAM,oBAAoB,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EAC9C,0BACA,OAAO,EAAE,IAAI,EAAE,SAAS,EAAuC,EAAE,EAAE,eAAe,EAAE;IAClF,IAAI;QACF,iDAAiD;QACjD,kCAAkC;QAClC,MAAM,aAAqC;YACzC,UAAU;YACV,UAAU;YACV,WAAW;QACb;QAEA,MAAM,WAAW,UAAU,CAAC,KAAK,WAAW,GAAG;QAC/C,IAAI,UAAU;YACZ,OAAO;gBAAE;gBAAM;gBAAU,QAAQ,YAAY;YAAS;QACxD,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAClE;AACF;AAGF,sDAAsD;AACtD,MAAM,sBAAsB,CAAC,OAAmB,cAAsB,IAAI,EAAE,gBAAwB,CAAC;IACnG,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE;IACpE,MAAM,MAAM,WAAW,MAAM,SAAS;IACtC,MAAM,iBAAiB,WAAW;IAClC,MAAM,QAAQ,WAAW,cAAc,MAAM;IAE7C,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAEA,MAAM,iBAAiB,CAAC,MAAgB,UAAkB;IACxD,OAAO;QACL,IAAI,GAAG,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;QAC9B,UAAU;QACV;QACA;QACA,YAAY,KAAK,KAAK,GAAG;IAC3B;AACF;AAEA,aAAa;AACb,MAAM,YAAY,CAAA,GAAA,6MAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN;IACA,UAAU;QACR,sDAAsD;QACtD,WAAW,CAAC,OAAO;YACjB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,mBAAmB,EAAE,GAAG,OAAO,OAAO;YAE9D,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,YAAY,KAAK,KAAK,YAAY,EAAE;gBAChE,qDAAqD;gBACrD,MAAM,cAAc,eAAe,MAAM,UAAU;gBACnD,MAAM,SAAS,oBAAoB;oBAAC;iBAAY,EAAE,MAAM,MAAM,aAAa;gBAE3E,MAAM,IAAI,GAAG;oBACX,OAAO;wBAAC;qBAAY;oBACpB,cAAc,KAAK,YAAY;oBAC/B,GAAG,MAAM;gBACX;YACF,OAAO;gBACL,uBAAuB;gBACvB,MAAM,oBAAoB,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAClD,CAAA,WAAY,SAAS,QAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,IAC5C,SAAS,mBAAmB,KAAK;gBAGnC,IAAI,qBAAqB,GAAG;oBAC1B,uBAAuB;oBACvB,MAAM,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,QAAQ,IAAI;oBAChD,MAAM,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,UAAU,GAC5C,MAAM,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,QAAQ,CAAC,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,QAAQ;gBACrG,OAAO;oBACL,eAAe;oBACf,MAAM,cAAc,eAAe,MAAM,UAAU;oBACnD,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gBACxB;gBAEA,qBAAqB;gBACrB,MAAM,SAAS,oBAAoB,MAAM,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE,MAAM,aAAa;gBAChG,OAAO,MAAM,CAAC,MAAM,IAAI,EAAE;YAC5B;YAEA,MAAM,KAAK,GAAG;QAChB;QAEA,gBAAgB,CAAC,OAAO;YACtB,IAAI,CAAC,MAAM,IAAI,EAAE;YAEjB,MAAM,SAAS,OAAO,OAAO;YAC7B,MAAM,YAAY,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAEjE,IAAI,aAAa,GAAG;gBAClB,+BAA+B;gBAC/B,MAAM,cAAc,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU;gBAC/C,MAAM,eAAe,CAAC,OAAO,CAAC;gBAC9B,iCAAiC;gBACjC,MAAM,eAAe,GAAG,MAAM,eAAe,CAAC,KAAK,CAAC,GAAG;gBAEvD,cAAc;gBACd,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW;gBAEnC,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;oBACjC,MAAM,IAAI,GAAG;oBACb,MAAM,SAAS,GAAG;oBAClB,MAAM,aAAa,GAAG;gBACxB,OAAO;oBACL,qBAAqB;oBACrB,MAAM,SAAS,oBAAoB,MAAM,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE,MAAM,aAAa;oBAChG,OAAO,MAAM,CAAC,MAAM,IAAI,EAAE;gBAC5B;YACF;QACF;QAEA,gBAAgB,CAAC,OAAO;YACtB,IAAI,CAAC,MAAM,IAAI,EAAE;YAEjB,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO,OAAO;YAE3C,IAAI,YAAY,GAAG;gBACjB,2CAA2C;gBAC3C,UAAU,YAAY,CAAC,cAAc,CAAC,OAAO;oBAAE,MAAM;oBAAuB,SAAS;gBAAO;gBAC5F;YACF;YAEA,MAAM,YAAY,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YACjE,IAAI,aAAa,GAAG;gBAClB,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,GAAG;gBACvC,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,GACpC,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,GAAG;gBAE/C,qBAAqB;gBACrB,MAAM,SAAS,oBAAoB,MAAM,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE,MAAM,aAAa;gBAChG,OAAO,MAAM,CAAC,MAAM,IAAI,EAAE;YAC5B;QACF;QAEA,WAAW,CAAC;YACV,IAAI,MAAM,IAAI,EAAE;gBACd,+DAA+D;gBAC/D,MAAM,eAAe,GAAG;uBAAI,MAAM,IAAI,CAAC,KAAK;iBAAC;YAC/C;YACA,MAAM,IAAI,GAAG;YACb,MAAM,SAAS,GAAG;YAClB,MAAM,aAAa,GAAG;YACtB,MAAM,YAAY,GAAG;YACrB,MAAM,eAAe,GAAG;YACxB,MAAM,aAAa,GAAG;QACxB;QAEA,oBAAoB;QACpB,gBAAgB,CAAC,OAAO;YACtB,MAAM,QAAQ,OAAO,OAAO,IAAI;YAChC,IAAI,MAAM,eAAe,CAAC,MAAM,EAAE;gBAChC,MAAM,gBAAgB,MAAM,eAAe,CAAC,MAAM;gBAClD,MAAM,eAAe,CAAC,MAAM,CAAC,OAAO;gBAEpC,mBAAmB;gBACnB,UAAU,YAAY,CAAC,SAAS,CAAC,OAAO;oBACtC,MAAM;oBACN,SAAS;wBACP,MAAM,cAAc,QAAQ;wBAC5B,UAAU,cAAc,QAAQ;wBAChC,qBAAqB,cAAc,mBAAmB;oBACxD;gBACF;YACF;QACF;QAEA,gBAAgB,CAAC,OAAO;YACtB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,OAAO;YACzC,MAAM,SAAS,GAAG;YAClB,MAAM,aAAa,GAAG;YAEtB,IAAI,MAAM,IAAI,EAAE;gBACd,MAAM,SAAS,oBAAoB,MAAM,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE;gBAC7E,OAAO,MAAM,CAAC,MAAM,IAAI,EAAE;YAC5B;QACF;QAEA,iBAAiB,CAAC;YAChB,MAAM,SAAS,GAAG;YAClB,MAAM,aAAa,GAAG;YAEtB,IAAI,MAAM,IAAI,EAAE;gBACd,MAAM,SAAS,oBAAoB,MAAM,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE;gBAC7E,OAAO,MAAM,CAAC,MAAM,IAAI,EAAE;YAC5B;QACF;QAEA,gBAAgB;QAChB,iBAAiB,CAAC,OAAO;YACvB,MAAM,YAAY,GAAG,OAAO,OAAO;QACrC;QAEA,oBAAoB,CAAC,OAAO;YAC1B,MAAM,eAAe,GAAG,OAAO,OAAO;QACxC;QAEA,kBAAkB,CAAC,OAAO;YACxB,MAAM,aAAa,GAAG,OAAO,OAAO;QACtC;QAEA,sBAAsB;QACtB,iBAAiB,CAAC,OAAO;YACvB,IAAI,MAAM,IAAI,EAAE;gBACd,MAAM,YAAY;oBAChB,GAAG,MAAM,IAAI;oBACb,SAAS,IAAI,OAAO,WAAW;oBAC/B,MAAM,OAAO,OAAO;gBACtB;gBACA,MAAM,UAAU,CAAC,IAAI,CAAC;YACxB;QACF;QAEA,eAAe,CAAC,OAAO;YACrB,MAAM,YAAY,MAAM,UAAU,CAAC,OAAO,OAAO,CAAC;YAClD,IAAI,WAAW;gBACb,MAAM,IAAI,GAAG;oBAAE,GAAG,SAAS;gBAAC;gBAC5B,kCAAkC;gBAClC,OAAO,AAAC,MAAM,IAAI,CAAgD,OAAO;gBACzE,OAAO,AAAC,MAAM,IAAI,CAAgD,IAAI;YACxE;QACF;QAEA,iBAAiB,CAAC,OAAO;YACvB,MAAM,UAAU,CAAC,MAAM,CAAC,OAAO,OAAO,EAAE;QAC1C;QAEA,UAAU,CAAC,OAAO;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEA,YAAY,CAAC,OAAO;YAClB,MAAM,OAAO,GAAG,OAAO,OAAO;QAChC;IACF;IAEA,eAAe,CAAC;QACd,oBAAoB;QACpB,QACG,OAAO,CAAC,oBAAoB,SAAS,EAAE,CAAC,OAAO;YAC9C,MAAM,IAAI,GAAG,OAAO,OAAO;YAC3B,MAAM,OAAO,GAAG;QAClB,GACC,OAAO,CAAC,oBAAoB,QAAQ,EAAE,CAAC,OAAO;YAC7C,MAAM,KAAK,GAAG,OAAO,OAAO;YAC5B,MAAM,OAAO,GAAG;QAClB;QAEF,kBAAkB;QAClB,QACG,OAAO,CAAC,kBAAkB,SAAS,EAAE,CAAC;YACrC,MAAM,OAAO,GAAG;QAClB,GACC,OAAO,CAAC,kBAAkB,QAAQ,EAAE,CAAC,OAAO;YAC3C,MAAM,KAAK,GAAG,OAAO,OAAO;YAC5B,MAAM,OAAO,GAAG;QAClB;QAEF,wBAAwB;QACxB,QACG,OAAO,CAAC,kBAAkB,OAAO,EAAE,CAAC;YACnC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,kBAAkB,SAAS,EAAE,CAAC,OAAO;YAC5C,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,OAAO;YACzC,MAAM,SAAS,GAAG;YAClB,MAAM,aAAa,GAAG;YACtB,MAAM,OAAO,GAAG;YAEhB,IAAI,MAAM,IAAI,EAAE;gBACd,MAAM,SAAS,oBAAoB,MAAM,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE;gBAC7E,OAAO,MAAM,CAAC,MAAM,IAAI,EAAE;YAC5B;QACF,GACC,OAAO,CAAC,kBAAkB,QAAQ,EAAE,CAAC,OAAO;YAC3C,MAAM,KAAK,GAAG,OAAO,OAAO;YAC5B,MAAM,OAAO,GAAG;QAClB;IACJ;AACF;AAGO,MAAM,EACX,SAAS,EACT,cAAc,EACd,cAAc,EACd,SAAS,EACT,cAAc,EACd,cAAc,EACd,eAAe,EACf,eAAe,EACf,kBAAkB,EAClB,gBAAgB,EAChB,eAAe,EACf,aAAa,EACb,eAAe,EACf,QAAQ,EACR,UAAU,EACX,GAAG,UAAU,OAAO;AAGd,MAAM,aAAa,CAAC,QAA+B,MAAM,IAAI,CAAC,IAAI;AAClE,MAAM,kBAAkB,CAAC,QAA+B,MAAM,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE;AACpF,MAAM,kBAAkB,CAAC,QAA+B,MAAM,IAAI,CAAC,IAAI,EAAE,SAAS;AAClF,MAAM,sBAAsB,CAAC,QAClC,MAAM,IAAI,CAAC,IAAI,EAAE,MAAM,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE,MAAM;AACnE,MAAM,oBAAoB,CAAC,QAA+B,MAAM,IAAI,CAAC,OAAO;AAC5E,MAAM,kBAAkB,CAAC,QAA+B,MAAM,IAAI,CAAC,KAAK;AACxE,MAAM,kBAAkB,CAAC,QAA+B,MAAM,IAAI,CAAC,SAAS;AAC5E,MAAM,qBAAqB,CAAC,QAA+B,MAAM,IAAI,CAAC,YAAY;uCAE1E", "debugId": null}}, {"offset": {"line": 2701, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/slices/ordersSlice.ts"], "sourcesContent": ["/**\r\n * Orders Slice - Comprehensive order management\r\n * Handles orders across all user roles (<PERSON>er, Vendor, Driver, Admin)\r\n */\r\n\r\nimport { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\r\n\r\n// Order interfaces\r\nexport interface Order {\r\n  id: string;\r\n  customerId: string;\r\n  vendorId: string;\r\n  driverId?: string;\r\n  restaurantId: string;\r\n  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'picked_up' | 'delivered' | 'cancelled';\r\n  items: Array<{\r\n    id: string;\r\n    menuItemId: string;\r\n    name: string;\r\n    price: number;\r\n    quantity: number;\r\n    specialInstructions?: string;\r\n    modifiers?: Array<{\r\n      name: string;\r\n      price: number;\r\n    }>;\r\n  }>;\r\n  pricing: {\r\n    subtotal: number;\r\n    tax: number;\r\n    deliveryFee: number;\r\n    platformFee: number;\r\n    discount: number;\r\n    total: number;\r\n  };\r\n  deliveryAddress: {\r\n    street: string;\r\n    city: string;\r\n    state: string;\r\n    zipCode: string;\r\n    coordinates: {\r\n      lat: number;\r\n      lng: number;\r\n    };\r\n    instructions?: string;\r\n  };\r\n  paymentMethod: {\r\n    type: 'card' | 'gcash' | 'grab_pay' | 'paymaya' | 'cash';\r\n    details?: Record<string, unknown>;\r\n  };\r\n  timeline: Array<{\r\n    status: string;\r\n    timestamp: string;\r\n    note?: string;\r\n  }>;\r\n  estimatedDeliveryTime: string;\r\n  actualDeliveryTime?: string;\r\n  rating?: {\r\n    food: number;\r\n    delivery: number;\r\n    overall: number;\r\n    comment?: string;\r\n  };\r\n  createdAt: string;\r\n  updatedAt: string;\r\n}\r\n\r\nexport interface OrderFilters {\r\n  status?: string[];\r\n  dateRange?: {\r\n    start: string;\r\n    end: string;\r\n  };\r\n  restaurantId?: string;\r\n  customerId?: string;\r\n  driverId?: string;\r\n  minAmount?: number;\r\n  maxAmount?: number;\r\n}\r\n\r\nexport interface OrdersState {\r\n  // Orders data\r\n  orders: Order[];\r\n  currentOrder: Order | null;\r\n  \r\n  // Loading states\r\n  loading: boolean;\r\n  creating: boolean;\r\n  updating: boolean;\r\n  \r\n  // Error handling\r\n  error: string | null;\r\n  \r\n  // Filters and pagination\r\n  filters: OrderFilters;\r\n  sortBy: 'createdAt' | 'total' | 'status' | 'estimatedDeliveryTime';\r\n  sortOrder: 'asc' | 'desc';\r\n  currentPage: number;\r\n  totalPages: number;\r\n  totalCount: number;\r\n  \r\n  // Real-time updates\r\n  liveUpdates: boolean;\r\n  lastUpdate: number;\r\n  \r\n  // Role-specific views\r\n  customerOrders: Order[];\r\n  vendorOrders: Order[];\r\n  driverOrders: Order[];\r\n  adminOrders: Order[];\r\n  \r\n  // Statistics\r\n  stats: {\r\n    total: number;\r\n    pending: number;\r\n    confirmed: number;\r\n    preparing: number;\r\n    ready: number;\r\n    pickedUp: number;\r\n    delivered: number;\r\n    cancelled: number;\r\n    todayRevenue: number;\r\n    averageOrderValue: number;\r\n  };\r\n}\r\n\r\n// Initial state\r\nconst initialState: OrdersState = {\r\n  orders: [],\r\n  currentOrder: null,\r\n  loading: false,\r\n  creating: false,\r\n  updating: false,\r\n  error: null,\r\n  filters: {},\r\n  sortBy: 'createdAt',\r\n  sortOrder: 'desc',\r\n  currentPage: 1,\r\n  totalPages: 1,\r\n  totalCount: 0,\r\n  liveUpdates: true,\r\n  lastUpdate: 0,\r\n  customerOrders: [],\r\n  vendorOrders: [],\r\n  driverOrders: [],\r\n  adminOrders: [],\r\n  stats: {\r\n    total: 0,\r\n    pending: 0,\r\n    confirmed: 0,\r\n    preparing: 0,\r\n    ready: 0,\r\n    pickedUp: 0,\r\n    delivered: 0,\r\n    cancelled: 0,\r\n    todayRevenue: 0,\r\n    averageOrderValue: 0,\r\n  },\r\n};\r\n\r\n// Async thunks\r\nexport const fetchOrders = createAsyncThunk(\r\n  'orders/fetchOrders',\r\n  async ({ \r\n    filters, \r\n    page = 1, \r\n    limit = 20, \r\n    role \r\n  }: { \r\n    filters?: OrderFilters; \r\n    page?: number; \r\n    limit?: number; \r\n    role?: 'customer' | 'vendor' | 'driver' | 'admin';\r\n  }, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Implement API call to fetch orders\r\n      // This will integrate with your existing Firebase/API patterns\r\n      const response = await fetch('/api/orders', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ filters, page, limit, role }),\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        throw new Error('Failed to fetch orders');\r\n      }\r\n      \r\n      return await response.json();\r\n    } catch (error) {\r\n      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch orders');\r\n    }\r\n  }\r\n);\r\n\r\nexport const createOrder = createAsyncThunk(\r\n  'orders/createOrder',\r\n  async (orderData: Partial<Order>, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Implement order creation API call\r\n      const response = await fetch('/api/orders', {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify(orderData),\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        throw new Error('Failed to create order');\r\n      }\r\n      \r\n      return await response.json();\r\n    } catch (error) {\r\n      return rejectWithValue(error instanceof Error ? error.message : 'Failed to create order');\r\n    }\r\n  }\r\n);\r\n\r\nexport const updateOrderStatus = createAsyncThunk(\r\n  'orders/updateStatus',\r\n  async ({ \r\n    orderId, \r\n    status, \r\n    note \r\n  }: { \r\n    orderId: string; \r\n    status: Order['status']; \r\n    note?: string; \r\n  }, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Implement status update API call\r\n      const response = await fetch(`/api/orders/${orderId}/status`, {\r\n        method: 'PATCH',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ status, note }),\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        throw new Error('Failed to update order status');\r\n      }\r\n      \r\n      return await response.json();\r\n    } catch (error) {\r\n      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update order status');\r\n    }\r\n  }\r\n);\r\n\r\nexport const assignDriver = createAsyncThunk(\r\n  'orders/assignDriver',\r\n  async ({ orderId, driverId }: { orderId: string; driverId: string }, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Implement driver assignment API call\r\n      const response = await fetch(`/api/orders/${orderId}/assign-driver`, {\r\n        method: 'PATCH',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ driverId }),\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        throw new Error('Failed to assign driver');\r\n      }\r\n      \r\n      return await response.json();\r\n    } catch (error) {\r\n      return rejectWithValue(error instanceof Error ? error.message : 'Failed to assign driver');\r\n    }\r\n  }\r\n);\r\n\r\nexport const rateOrder = createAsyncThunk(\r\n  'orders/rateOrder',\r\n  async ({ \r\n    orderId, \r\n    rating \r\n  }: { \r\n    orderId: string; \r\n    rating: Order['rating']; \r\n  }, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Implement rating API call\r\n      const response = await fetch(`/api/orders/${orderId}/rate`, {\r\n        method: 'PATCH',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ rating }),\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        throw new Error('Failed to rate order');\r\n      }\r\n      \r\n      return await response.json();\r\n    } catch (error) {\r\n      return rejectWithValue(error instanceof Error ? error.message : 'Failed to rate order');\r\n    }\r\n  }\r\n);\r\n\r\n// Orders slice\r\nconst ordersSlice = createSlice({\r\n  name: 'orders',\r\n  initialState,\r\n  reducers: {\r\n    // Real-time order updates\r\n    updateOrderRealTime: (state, action: PayloadAction<Order>) => {\r\n      const updatedOrder = action.payload;\r\n      const index = state.orders.findIndex(order => order.id === updatedOrder.id);\r\n      \r\n      if (index >= 0) {\r\n        state.orders[index] = updatedOrder;\r\n      } else {\r\n        state.orders.unshift(updatedOrder);\r\n      }\r\n      \r\n      // Update role-specific arrays\r\n      if (updatedOrder.customerId) {\r\n        const customerIndex = state.customerOrders.findIndex(o => o.id === updatedOrder.id);\r\n        if (customerIndex >= 0) {\r\n          state.customerOrders[customerIndex] = updatedOrder;\r\n        }\r\n      }\r\n      \r\n      if (updatedOrder.vendorId) {\r\n        const vendorIndex = state.vendorOrders.findIndex(o => o.id === updatedOrder.id);\r\n        if (vendorIndex >= 0) {\r\n          state.vendorOrders[vendorIndex] = updatedOrder;\r\n        }\r\n      }\r\n      \r\n      if (updatedOrder.driverId) {\r\n        const driverIndex = state.driverOrders.findIndex(o => o.id === updatedOrder.id);\r\n        if (driverIndex >= 0) {\r\n          state.driverOrders[driverIndex] = updatedOrder;\r\n        }\r\n      }\r\n      \r\n      state.lastUpdate = Date.now();\r\n    },\r\n    \r\n    // Set current order\r\n    setCurrentOrder: (state, action: PayloadAction<Order | null>) => {\r\n      state.currentOrder = action.payload;\r\n    },\r\n    \r\n    // Filters\r\n    setFilters: (state, action: PayloadAction<OrderFilters>) => {\r\n      state.filters = action.payload;\r\n      state.currentPage = 1; // Reset to first page when filters change\r\n    },\r\n    \r\n    updateFilter: (state, action: PayloadAction<{ key: keyof OrderFilters; value: OrderFilters[keyof OrderFilters] }>) => {\r\n      const { key, value } = action.payload;\r\n      if (value !== undefined) {\r\n        // Type assertion is safe here as we're using the correct key-value pair\r\n        (state.filters as Record<string, OrderFilters[keyof OrderFilters]>)[key] = value;\r\n      } else {\r\n        delete state.filters[key];\r\n      }\r\n      state.currentPage = 1;\r\n    },\r\n    \r\n    clearFilters: (state) => {\r\n      state.filters = {};\r\n      state.currentPage = 1;\r\n    },\r\n    \r\n    // Sorting\r\n    setSorting: (state, action: PayloadAction<{ sortBy: OrdersState['sortBy']; sortOrder: OrdersState['sortOrder'] }>) => {\r\n      const { sortBy, sortOrder } = action.payload;\r\n      state.sortBy = sortBy;\r\n      state.sortOrder = sortOrder;\r\n    },\r\n    \r\n    // Pagination\r\n    setCurrentPage: (state, action: PayloadAction<number>) => {\r\n      state.currentPage = action.payload;\r\n    },\r\n    \r\n    // Live updates\r\n    setLiveUpdates: (state, action: PayloadAction<boolean>) => {\r\n      state.liveUpdates = action.payload;\r\n    },\r\n    \r\n    // Statistics\r\n    updateStats: (state, action: PayloadAction<Partial<OrdersState['stats']>>) => {\r\n      state.stats = { ...state.stats, ...action.payload };\r\n    },\r\n    \r\n    // Error handling\r\n    setError: (state, action: PayloadAction<string | null>) => {\r\n      state.error = action.payload;\r\n    },\r\n    \r\n    clearError: (state) => {\r\n      state.error = null;\r\n    },\r\n    \r\n    // Loading states\r\n    setLoading: (state, action: PayloadAction<boolean>) => {\r\n      state.loading = action.payload;\r\n    },\r\n    \r\n    setCreating: (state, action: PayloadAction<boolean>) => {\r\n      state.creating = action.payload;\r\n    },\r\n    \r\n    setUpdating: (state, action: PayloadAction<boolean>) => {\r\n      state.updating = action.payload;\r\n    },\r\n  },\r\n  \r\n  extraReducers: (builder) => {\r\n    // Fetch orders\r\n    builder\r\n      .addCase(fetchOrders.pending, (state) => {\r\n        state.loading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(fetchOrders.fulfilled, (state, action) => {\r\n        const { orders, totalCount, totalPages, role } = action.payload;\r\n        \r\n        state.orders = orders;\r\n        state.totalCount = totalCount;\r\n        state.totalPages = totalPages;\r\n        state.loading = false;\r\n        \r\n        // Update role-specific arrays\r\n        if (role === 'customer') {\r\n          state.customerOrders = orders;\r\n        } else if (role === 'vendor') {\r\n          state.vendorOrders = orders;\r\n        } else if (role === 'driver') {\r\n          state.driverOrders = orders;\r\n        } else if (role === 'admin') {\r\n          state.adminOrders = orders;\r\n        }\r\n      })\r\n      .addCase(fetchOrders.rejected, (state, action) => {\r\n        state.loading = false;\r\n        state.error = action.payload as string;\r\n      });\r\n    \r\n    // Create order\r\n    builder\r\n      .addCase(createOrder.pending, (state) => {\r\n        state.creating = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(createOrder.fulfilled, (state, action) => {\r\n        const newOrder = action.payload;\r\n        state.orders.unshift(newOrder);\r\n        state.currentOrder = newOrder;\r\n        state.creating = false;\r\n      })\r\n      .addCase(createOrder.rejected, (state, action) => {\r\n        state.creating = false;\r\n        state.error = action.payload as string;\r\n      });\r\n    \r\n    // Update order status\r\n    builder\r\n      .addCase(updateOrderStatus.pending, (state) => {\r\n        state.updating = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(updateOrderStatus.fulfilled, (state, action) => {\r\n        const updatedOrder = action.payload;\r\n        ordersSlice.caseReducers.updateOrderRealTime(state, { type: 'orders/updateOrderRealTime', payload: updatedOrder });\r\n        state.updating = false;\r\n      })\r\n      .addCase(updateOrderStatus.rejected, (state, action) => {\r\n        state.updating = false;\r\n        state.error = action.payload as string;\r\n      });\r\n    \r\n    // Assign driver\r\n    builder\r\n      .addCase(assignDriver.fulfilled, (state, action) => {\r\n        const updatedOrder = action.payload;\r\n        ordersSlice.caseReducers.updateOrderRealTime(state, { type: 'orders/updateOrderRealTime', payload: updatedOrder });\r\n      });\r\n    \r\n    // Rate order\r\n    builder\r\n      .addCase(rateOrder.fulfilled, (state, action) => {\r\n        const updatedOrder = action.payload;\r\n        ordersSlice.caseReducers.updateOrderRealTime(state, { type: 'orders/updateOrderRealTime', payload: updatedOrder });\r\n      });\r\n  },\r\n});\r\n\r\n// Export actions\r\nexport const {\r\n  updateOrderRealTime,\r\n  setCurrentOrder,\r\n  setFilters,\r\n  updateFilter,\r\n  clearFilters,\r\n  setSorting,\r\n  setCurrentPage,\r\n  setLiveUpdates,\r\n  updateStats,\r\n  setError,\r\n  clearError,\r\n  setLoading,\r\n  setCreating,\r\n  setUpdating,\r\n} = ordersSlice.actions;\r\n\r\n// Selectors\r\nexport const selectOrders = (state: { orders: OrdersState }) => state.orders.orders;\r\nexport const selectCurrentOrder = (state: { orders: OrdersState }) => state.orders.currentOrder;\r\nexport const selectOrdersLoading = (state: { orders: OrdersState }) => state.orders.loading;\r\nexport const selectOrdersError = (state: { orders: OrdersState }) => state.orders.error;\r\nexport const selectOrderFilters = (state: { orders: OrdersState }) => state.orders.filters;\r\nexport const selectOrderStats = (state: { orders: OrdersState }) => state.orders.stats;\r\n\r\nexport default ordersSlice;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAED;;AAyHA,gBAAgB;AAChB,MAAM,eAA4B;IAChC,QAAQ,EAAE;IACV,cAAc;IACd,SAAS;IACT,UAAU;IACV,UAAU;IACV,OAAO;IACP,SAAS,CAAC;IACV,QAAQ;IACR,WAAW;IACX,aAAa;IACb,YAAY;IACZ,YAAY;IACZ,aAAa;IACb,YAAY;IACZ,gBAAgB,EAAE;IAClB,cAAc,EAAE;IAChB,cAAc,EAAE;IAChB,aAAa,EAAE;IACf,OAAO;QACL,OAAO;QACP,SAAS;QACT,WAAW;QACX,WAAW;QACX,OAAO;QACP,UAAU;QACV,WAAW;QACX,WAAW;QACX,cAAc;QACd,mBAAmB;IACrB;AACF;AAGO,MAAM,cAAc,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EACxC,sBACA,OAAO,EACL,OAAO,EACP,OAAO,CAAC,EACR,QAAQ,EAAE,EACV,IAAI,EAML,EAAE,EAAE,eAAe,EAAE;IACpB,IAAI;QACF,2CAA2C;QAC3C,+DAA+D;QAC/D,MAAM,WAAW,MAAM,MAAM,eAAe;YAC1C,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;YAC9C,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAS;gBAAM;gBAAO;YAAK;QACpD;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,OAAO,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAClE;AACF;AAGK,MAAM,cAAc,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EACxC,sBACA,OAAO,WAA2B,EAAE,eAAe,EAAE;IACnD,IAAI;QACF,0CAA0C;QAC1C,MAAM,WAAW,MAAM,MAAM,eAAe;YAC1C,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;YAC9C,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,OAAO,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAClE;AACF;AAGK,MAAM,oBAAoB,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EAC9C,uBACA,OAAO,EACL,OAAO,EACP,MAAM,EACN,IAAI,EAKL,EAAE,EAAE,eAAe,EAAE;IACpB,IAAI;QACF,yCAAyC;QACzC,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,QAAQ,OAAO,CAAC,EAAE;YAC5D,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;YAC9C,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAQ;YAAK;QACtC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,OAAO,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAClE;AACF;AAGK,MAAM,eAAe,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EACzC,uBACA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAyC,EAAE,EAAE,eAAe,EAAE;IACtF,IAAI;QACF,6CAA6C;QAC7C,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,QAAQ,cAAc,CAAC,EAAE;YACnE,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;YAC9C,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAS;QAClC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,OAAO,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAClE;AACF;AAGK,MAAM,YAAY,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EACtC,oBACA,OAAO,EACL,OAAO,EACP,MAAM,EAIP,EAAE,EAAE,eAAe,EAAE;IACpB,IAAI;QACF,kCAAkC;QAClC,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,QAAQ,KAAK,CAAC,EAAE;YAC1D,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;YAC9C,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAO;QAChC;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,MAAM,SAAS,IAAI;IAC5B,EAAE,OAAO,OAAO;QACd,OAAO,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAClE;AACF;AAGF,eAAe;AACf,MAAM,cAAc,CAAA,GAAA,6MAAA,CAAA,cAAW,AAAD,EAAE;IAC9B,MAAM;IACN;IACA,UAAU;QACR,0BAA0B;QAC1B,qBAAqB,CAAC,OAAO;YAC3B,MAAM,eAAe,OAAO,OAAO;YACnC,MAAM,QAAQ,MAAM,MAAM,CAAC,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,aAAa,EAAE;YAE1E,IAAI,SAAS,GAAG;gBACd,MAAM,MAAM,CAAC,MAAM,GAAG;YACxB,OAAO;gBACL,MAAM,MAAM,CAAC,OAAO,CAAC;YACvB;YAEA,8BAA8B;YAC9B,IAAI,aAAa,UAAU,EAAE;gBAC3B,MAAM,gBAAgB,MAAM,cAAc,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,EAAE;gBAClF,IAAI,iBAAiB,GAAG;oBACtB,MAAM,cAAc,CAAC,cAAc,GAAG;gBACxC;YACF;YAEA,IAAI,aAAa,QAAQ,EAAE;gBACzB,MAAM,cAAc,MAAM,YAAY,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,EAAE;gBAC9E,IAAI,eAAe,GAAG;oBACpB,MAAM,YAAY,CAAC,YAAY,GAAG;gBACpC;YACF;YAEA,IAAI,aAAa,QAAQ,EAAE;gBACzB,MAAM,cAAc,MAAM,YAAY,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,aAAa,EAAE;gBAC9E,IAAI,eAAe,GAAG;oBACpB,MAAM,YAAY,CAAC,YAAY,GAAG;gBACpC;YACF;YAEA,MAAM,UAAU,GAAG,KAAK,GAAG;QAC7B;QAEA,oBAAoB;QACpB,iBAAiB,CAAC,OAAO;YACvB,MAAM,YAAY,GAAG,OAAO,OAAO;QACrC;QAEA,UAAU;QACV,YAAY,CAAC,OAAO;YAClB,MAAM,OAAO,GAAG,OAAO,OAAO;YAC9B,MAAM,WAAW,GAAG,GAAG,0CAA0C;QACnE;QAEA,cAAc,CAAC,OAAO;YACpB,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,OAAO,OAAO;YACrC,IAAI,UAAU,WAAW;gBACvB,wEAAwE;gBACvE,MAAM,OAAO,AAAqD,CAAC,IAAI,GAAG;YAC7E,OAAO;gBACL,OAAO,MAAM,OAAO,CAAC,IAAI;YAC3B;YACA,MAAM,WAAW,GAAG;QACtB;QAEA,cAAc,CAAC;YACb,MAAM,OAAO,GAAG,CAAC;YACjB,MAAM,WAAW,GAAG;QACtB;QAEA,UAAU;QACV,YAAY,CAAC,OAAO;YAClB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,OAAO;YAC5C,MAAM,MAAM,GAAG;YACf,MAAM,SAAS,GAAG;QACpB;QAEA,aAAa;QACb,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QAEA,eAAe;QACf,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QAEA,aAAa;QACb,aAAa,CAAC,OAAO;YACnB,MAAM,KAAK,GAAG;gBAAE,GAAG,MAAM,KAAK;gBAAE,GAAG,OAAO,OAAO;YAAC;QACpD;QAEA,iBAAiB;QACjB,UAAU,CAAC,OAAO;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEA,YAAY,CAAC;YACX,MAAM,KAAK,GAAG;QAChB;QAEA,iBAAiB;QACjB,YAAY,CAAC,OAAO;YAClB,MAAM,OAAO,GAAG,OAAO,OAAO;QAChC;QAEA,aAAa,CAAC,OAAO;YACnB,MAAM,QAAQ,GAAG,OAAO,OAAO;QACjC;QAEA,aAAa,CAAC,OAAO;YACnB,MAAM,QAAQ,GAAG,OAAO,OAAO;QACjC;IACF;IAEA,eAAe,CAAC;QACd,eAAe;QACf,QACG,OAAO,CAAC,YAAY,OAAO,EAAE,CAAC;YAC7B,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,YAAY,SAAS,EAAE,CAAC,OAAO;YACtC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,OAAO,OAAO;YAE/D,MAAM,MAAM,GAAG;YACf,MAAM,UAAU,GAAG;YACnB,MAAM,UAAU,GAAG;YACnB,MAAM,OAAO,GAAG;YAEhB,8BAA8B;YAC9B,IAAI,SAAS,YAAY;gBACvB,MAAM,cAAc,GAAG;YACzB,OAAO,IAAI,SAAS,UAAU;gBAC5B,MAAM,YAAY,GAAG;YACvB,OAAO,IAAI,SAAS,UAAU;gBAC5B,MAAM,YAAY,GAAG;YACvB,OAAO,IAAI,SAAS,SAAS;gBAC3B,MAAM,WAAW,GAAG;YACtB;QACF,GACC,OAAO,CAAC,YAAY,QAAQ,EAAE,CAAC,OAAO;YACrC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEF,eAAe;QACf,QACG,OAAO,CAAC,YAAY,OAAO,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG;YACjB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,YAAY,SAAS,EAAE,CAAC,OAAO;YACtC,MAAM,WAAW,OAAO,OAAO;YAC/B,MAAM,MAAM,CAAC,OAAO,CAAC;YACrB,MAAM,YAAY,GAAG;YACrB,MAAM,QAAQ,GAAG;QACnB,GACC,OAAO,CAAC,YAAY,QAAQ,EAAE,CAAC,OAAO;YACrC,MAAM,QAAQ,GAAG;YACjB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEF,sBAAsB;QACtB,QACG,OAAO,CAAC,kBAAkB,OAAO,EAAE,CAAC;YACnC,MAAM,QAAQ,GAAG;YACjB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,kBAAkB,SAAS,EAAE,CAAC,OAAO;YAC5C,MAAM,eAAe,OAAO,OAAO;YACnC,YAAY,YAAY,CAAC,mBAAmB,CAAC,OAAO;gBAAE,MAAM;gBAA8B,SAAS;YAAa;YAChH,MAAM,QAAQ,GAAG;QACnB,GACC,OAAO,CAAC,kBAAkB,QAAQ,EAAE,CAAC,OAAO;YAC3C,MAAM,QAAQ,GAAG;YACjB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEF,gBAAgB;QAChB,QACG,OAAO,CAAC,aAAa,SAAS,EAAE,CAAC,OAAO;YACvC,MAAM,eAAe,OAAO,OAAO;YACnC,YAAY,YAAY,CAAC,mBAAmB,CAAC,OAAO;gBAAE,MAAM;gBAA8B,SAAS;YAAa;QAClH;QAEF,aAAa;QACb,QACG,OAAO,CAAC,UAAU,SAAS,EAAE,CAAC,OAAO;YACpC,MAAM,eAAe,OAAO,OAAO;YACnC,YAAY,YAAY,CAAC,mBAAmB,CAAC,OAAO;gBAAE,MAAM;gBAA8B,SAAS;YAAa;QAClH;IACJ;AACF;AAGO,MAAM,EACX,mBAAmB,EACnB,eAAe,EACf,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,UAAU,EACV,cAAc,EACd,cAAc,EACd,WAAW,EACX,QAAQ,EACR,UAAU,EACV,UAAU,EACV,WAAW,EACX,WAAW,EACZ,GAAG,YAAY,OAAO;AAGhB,MAAM,eAAe,CAAC,QAAmC,MAAM,MAAM,CAAC,MAAM;AAC5E,MAAM,qBAAqB,CAAC,QAAmC,MAAM,MAAM,CAAC,YAAY;AACxF,MAAM,sBAAsB,CAAC,QAAmC,MAAM,MAAM,CAAC,OAAO;AACpF,MAAM,oBAAoB,CAAC,QAAmC,MAAM,MAAM,CAAC,KAAK;AAChF,MAAM,qBAAqB,CAAC,QAAmC,MAAM,MAAM,CAAC,OAAO;AACnF,MAAM,mBAAmB,CAAC,QAAmC,MAAM,MAAM,CAAC,KAAK;uCAEvE", "debugId": null}}, {"offset": {"line": 3055, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/slices/restaurantsSlice.ts"], "sourcesContent": ["/**\r\n * Restaurants Slice - Restaurant and menu management\r\n */\r\n\r\nimport { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\r\nimport { Restaurant, MenuItem } from '@/types';\r\n\r\nexport interface RestaurantsState {\r\n  restaurants: Restaurant[];\r\n  currentRestaurant: Restaurant | null;\r\n  menu: MenuItem[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  filters: {\r\n    category?: string;\r\n    cuisine?: string;\r\n    priceRange?: [number, number];\r\n    rating?: number;\r\n    deliveryTime?: number;\r\n    isOpen?: boolean;\r\n  };\r\n  searchQuery: string;\r\n  sortBy: 'distance' | 'rating' | 'delivery_time' | 'price';\r\n}\r\n\r\nconst initialState: RestaurantsState = {\r\n  restaurants: [],\r\n  currentRestaurant: null,\r\n  menu: [],\r\n  loading: false,\r\n  error: null,\r\n  filters: {},\r\n  searchQuery: '',\r\n  sortBy: 'distance',\r\n};\r\n\r\ninterface FetchRestaurantsParams {\r\n  location?: {\r\n    lat: number;\r\n    lng: number;\r\n    radius?: number;\r\n  };\r\n  filters?: RestaurantsState['filters'];\r\n  search?: string;\r\n  sortBy?: RestaurantsState['sortBy'];\r\n  page?: number;\r\n  limit?: number;\r\n}\r\n\r\nexport const fetchRestaurants = createAsyncThunk(\r\n  'restaurants/fetchRestaurants',\r\n  async (params: FetchRestaurantsParams, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Implement API call\r\n      return [];\r\n    } catch (error) {\r\n      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch restaurants');\r\n    }\r\n  }\r\n);\r\n\r\nconst restaurantsSlice = createSlice({\r\n  name: 'restaurants',\r\n  initialState,\r\n  reducers: {\r\n    setCurrentRestaurant: (state, action: PayloadAction<Restaurant | null>) => {\r\n      state.currentRestaurant = action.payload;\r\n    },\r\n    setFilters: (state, action: PayloadAction<RestaurantsState['filters']>) => {\r\n      state.filters = action.payload;\r\n    },\r\n    setSearchQuery: (state, action: PayloadAction<string>) => {\r\n      state.searchQuery = action.payload;\r\n    },\r\n    setSortBy: (state, action: PayloadAction<RestaurantsState['sortBy']>) => {\r\n      state.sortBy = action.payload;\r\n    },\r\n  },\r\n  extraReducers: (builder) => {\r\n    builder\r\n      .addCase(fetchRestaurants.pending, (state) => {\r\n        state.loading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(fetchRestaurants.fulfilled, (state, action) => {\r\n        state.restaurants = action.payload;\r\n        state.loading = false;\r\n      })\r\n      .addCase(fetchRestaurants.rejected, (state, action) => {\r\n        state.loading = false;\r\n        state.error = action.payload as string;\r\n      });\r\n  },\r\n});\r\n\r\nexport const { setCurrentRestaurant, setFilters, setSearchQuery, setSortBy } = restaurantsSlice.actions;\r\nexport default restaurantsSlice;\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;AAED;;AAqBA,MAAM,eAAiC;IACrC,aAAa,EAAE;IACf,mBAAmB;IACnB,MAAM,EAAE;IACR,SAAS;IACT,OAAO;IACP,SAAS,CAAC;IACV,aAAa;IACb,QAAQ;AACV;AAeO,MAAM,mBAAmB,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EAC7C,gCACA,OAAO,QAAgC,EAAE,eAAe,EAAE;IACxD,IAAI;QACF,2BAA2B;QAC3B,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,OAAO,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAClE;AACF;AAGF,MAAM,mBAAmB,CAAA,GAAA,6MAAA,CAAA,cAAW,AAAD,EAAE;IACnC,MAAM;IACN;IACA,UAAU;QACR,sBAAsB,CAAC,OAAO;YAC5B,MAAM,iBAAiB,GAAG,OAAO,OAAO;QAC1C;QACA,YAAY,CAAC,OAAO;YAClB,MAAM,OAAO,GAAG,OAAO,OAAO;QAChC;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;QACA,WAAW,CAAC,OAAO;YACjB,MAAM,MAAM,GAAG,OAAO,OAAO;QAC/B;IACF;IACA,eAAe,CAAC;QACd,QACG,OAAO,CAAC,iBAAiB,OAAO,EAAE,CAAC;YAClC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,iBAAiB,SAAS,EAAE,CAAC,OAAO;YAC3C,MAAM,WAAW,GAAG,OAAO,OAAO;YAClC,MAAM,OAAO,GAAG;QAClB,GACC,OAAO,CAAC,iBAAiB,QAAQ,EAAE,CAAC,OAAO;YAC1C,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;IACJ;AACF;AAEO,MAAM,EAAE,oBAAoB,EAAE,UAAU,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,iBAAiB,OAAO;uCACxF", "debugId": null}}, {"offset": {"line": 3126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/slices/driversSlice.ts"], "sourcesContent": ["/**\r\n * Drivers Slice - Driver management and tracking\r\n */\r\n\r\nimport { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\r\n\r\nexport interface Driver {\r\n  id: string;\r\n  name: string;\r\n  email: string;\r\n  phone: string;\r\n  status: 'pending' | 'approved' | 'active' | 'suspended';\r\n  isOnline: boolean;\r\n  location: {\r\n    lat: number;\r\n    lng: number;\r\n    address: string;\r\n    lastUpdated: string;\r\n  };\r\n  vehicle: {\r\n    type: 'motorcycle' | 'bicycle' | 'car';\r\n    model: string;\r\n    plateNumber: string;\r\n  };\r\n  rating: number;\r\n  totalDeliveries: number;\r\n  earnings: {\r\n    today: number;\r\n    week: number;\r\n    month: number;\r\n    total: number;\r\n  };\r\n}\r\n\r\nexport interface DriversState {\r\n  drivers: Driver[];\r\n  availableDrivers: Driver[];\r\n  currentDriver: Driver | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n  realTimeTracking: boolean;\r\n}\r\n\r\nconst initialState: DriversState = {\r\n  drivers: [],\r\n  availableDrivers: [],\r\n  currentDriver: null,\r\n  loading: false,\r\n  error: null,\r\n  realTimeTracking: false,\r\n};\r\n\r\ninterface FetchDriversParams {\r\n  status?: Driver['status'];\r\n  isOnline?: boolean;\r\n  location?: {\r\n    lat: number;\r\n    lng: number;\r\n    radius: number; // in kilometers\r\n  };\r\n}\r\n\r\nexport const fetchDrivers = createAsyncThunk(\r\n  'drivers/fetchDrivers',\r\n  async (params: FetchDriversParams, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Implement API call\r\n      return [];\r\n    } catch (error) {\r\n      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch drivers');\r\n    }\r\n  }\r\n);\r\n\r\nconst driversSlice = createSlice({\r\n  name: 'drivers',\r\n  initialState,\r\n  reducers: {\r\n    updateDriverLocation: (state, action: PayloadAction<{ driverId: string; location: Driver['location'] }>) => {\r\n      const { driverId, location } = action.payload;\r\n      const driver = state.drivers.find(d => d.id === driverId);\r\n      if (driver) {\r\n        driver.location = location;\r\n      }\r\n    },\r\n    setDriverOnlineStatus: (state, action: PayloadAction<{ driverId: string; isOnline: boolean }>) => {\r\n      const { driverId, isOnline } = action.payload;\r\n      const driver = state.drivers.find(d => d.id === driverId);\r\n      if (driver) {\r\n        driver.isOnline = isOnline;\r\n      }\r\n    },\r\n    setRealTimeTracking: (state, action: PayloadAction<boolean>) => {\r\n      state.realTimeTracking = action.payload;\r\n    },\r\n  },\r\n  extraReducers: (builder) => {\r\n    builder\r\n      .addCase(fetchDrivers.pending, (state) => {\r\n        state.loading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(fetchDrivers.fulfilled, (state, action) => {\r\n        state.drivers = action.payload;\r\n        state.availableDrivers = action.payload.filter((d: Driver) => d.isOnline && d.status === 'active');\r\n        state.loading = false;\r\n      })\r\n      .addCase(fetchDrivers.rejected, (state, action) => {\r\n        state.loading = false;\r\n        state.error = action.payload as string;\r\n      });\r\n  },\r\n});\r\n\r\nexport const { updateDriverLocation, setDriverOnlineStatus, setRealTimeTracking } = driversSlice.actions;\r\nexport default driversSlice;\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;AAED;;AAuCA,MAAM,eAA6B;IACjC,SAAS,EAAE;IACX,kBAAkB,EAAE;IACpB,eAAe;IACf,SAAS;IACT,OAAO;IACP,kBAAkB;AACpB;AAYO,MAAM,eAAe,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EACzC,wBACA,OAAO,QAA4B,EAAE,eAAe,EAAE;IACpD,IAAI;QACF,2BAA2B;QAC3B,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,OAAO,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAClE;AACF;AAGF,MAAM,eAAe,CAAA,GAAA,6MAAA,CAAA,cAAW,AAAD,EAAE;IAC/B,MAAM;IACN;IACA,UAAU;QACR,sBAAsB,CAAC,OAAO;YAC5B,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,OAAO;YAC7C,MAAM,SAAS,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAChD,IAAI,QAAQ;gBACV,OAAO,QAAQ,GAAG;YACpB;QACF;QACA,uBAAuB,CAAC,OAAO;YAC7B,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,OAAO;YAC7C,MAAM,SAAS,MAAM,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAChD,IAAI,QAAQ;gBACV,OAAO,QAAQ,GAAG;YACpB;QACF;QACA,qBAAqB,CAAC,OAAO;YAC3B,MAAM,gBAAgB,GAAG,OAAO,OAAO;QACzC;IACF;IACA,eAAe,CAAC;QACd,QACG,OAAO,CAAC,aAAa,OAAO,EAAE,CAAC;YAC9B,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,aAAa,SAAS,EAAE,CAAC,OAAO;YACvC,MAAM,OAAO,GAAG,OAAO,OAAO;YAC9B,MAAM,gBAAgB,GAAG,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,IAAc,EAAE,QAAQ,IAAI,EAAE,MAAM,KAAK;YACzF,MAAM,OAAO,GAAG;QAClB,GACC,OAAO,CAAC,aAAa,QAAQ,EAAE,CAAC,OAAO;YACtC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;IACJ;AACF;AAEO,MAAM,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,mBAAmB,EAAE,GAAG,aAAa,OAAO;uCACzF", "debugId": null}}, {"offset": {"line": 3200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/slices/customersSlice.ts"], "sourcesContent": ["/**\r\n * Customers Slice - Customer management\r\n */\r\n\r\nimport { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\r\nimport { Address } from '@/types';\r\n\r\nexport interface CustomerPreferences {\r\n  dietaryRestrictions: string[];\r\n  favoriteRestaurants: string[];\r\n  defaultPaymentMethod?: string;\r\n  notifications: {\r\n    orderUpdates: boolean;\r\n    promotions: boolean;\r\n    newsletter: boolean;\r\n  };\r\n}\r\n\r\nexport interface Customer {\r\n  id: string;\r\n  name: string;\r\n  email: string;\r\n  phone: string;\r\n  addresses: Address[];\r\n  orderHistory: string[];\r\n  preferences: CustomerPreferences;\r\n  loyaltyPoints: number;\r\n}\r\n\r\nexport interface CustomersState {\r\n  customers: Customer[];\r\n  loading: boolean;\r\n  error: string | null;\r\n}\r\n\r\nconst initialState: CustomersState = {\r\n  customers: [],\r\n  loading: false,\r\n  error: null,\r\n};\r\n\r\ninterface FetchCustomersParams {\r\n  page?: number;\r\n  limit?: number;\r\n  search?: string;\r\n  sortBy?: 'name' | 'email' | 'loyaltyPoints' | 'createdAt';\r\n  sortOrder?: 'asc' | 'desc';\r\n}\r\n\r\nexport const fetchCustomers = createAsyncThunk(\r\n  'customers/fetchCustomers',\r\n  async (params: FetchCustomersParams, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Implement API call\r\n      return [];\r\n    } catch (error) {\r\n      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch customers');\r\n    }\r\n  }\r\n);\r\n\r\nconst customersSlice = createSlice({\r\n  name: 'customers',\r\n  initialState,\r\n  reducers: {},\r\n  extraReducers: (builder) => {\r\n    builder\r\n      .addCase(fetchCustomers.pending, (state) => {\r\n        state.loading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(fetchCustomers.fulfilled, (state, action) => {\r\n        state.customers = action.payload;\r\n        state.loading = false;\r\n      })\r\n      .addCase(fetchCustomers.rejected, (state, action) => {\r\n        state.loading = false;\r\n        state.error = action.payload as string;\r\n      });\r\n  },\r\n});\r\n\r\nexport default customersSlice;\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAED;;AA+BA,MAAM,eAA+B;IACnC,WAAW,EAAE;IACb,SAAS;IACT,OAAO;AACT;AAUO,MAAM,iBAAiB,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EAC3C,4BACA,OAAO,QAA8B,EAAE,eAAe,EAAE;IACtD,IAAI;QACF,2BAA2B;QAC3B,OAAO,EAAE;IACX,EAAE,OAAO,OAAO;QACd,OAAO,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAClE;AACF;AAGF,MAAM,iBAAiB,CAAA,GAAA,6MAAA,CAAA,cAAW,AAAD,EAAE;IACjC,MAAM;IACN;IACA,UAAU,CAAC;IACX,eAAe,CAAC;QACd,QACG,OAAO,CAAC,eAAe,OAAO,EAAE,CAAC;YAChC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,eAAe,SAAS,EAAE,CAAC,OAAO;YACzC,MAAM,SAAS,GAAG,OAAO,OAAO;YAChC,MAAM,OAAO,GAAG;QAClB,GACC,OAAO,CAAC,eAAe,QAAQ,EAAE,CAAC,OAAO;YACxC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;IACJ;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 3248, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/slices/realTimeSlice.ts"], "sourcesContent": ["/**\r\n * Real-time Slice - Real-time updates and connections\r\n */\r\n\r\nimport { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\n\r\nexport interface OrderUpdate {\r\n  orderId: string;\r\n  status: string;\r\n  timestamp: number;\r\n  message?: string;\r\n}\r\n\r\nexport interface RealTimeState {\r\n  connections: Record<string, boolean>;\r\n  orderUpdates: Record<string, OrderUpdate>;\r\n  driverLocations: Record<string, { lat: number; lng: number; timestamp: number }>;\r\n  isConnected: boolean;\r\n  lastHeartbeat: number;\r\n}\r\n\r\nconst initialState: RealTimeState = {\r\n  connections: {},\r\n  orderUpdates: {},\r\n  driverLocations: {},\r\n  isConnected: false,\r\n  lastHeartbeat: 0,\r\n};\r\n\r\nconst realTimeSlice = createSlice({\r\n  name: 'realTime',\r\n  initialState,\r\n  reducers: {\r\n    setConnection: (state, action: PayloadAction<{ id: string; connected: boolean }>) => {\r\n      const { id, connected } = action.payload;\r\n      state.connections[id] = connected;\r\n    },\r\n    updateOrderRealTime: (state, action: PayloadAction<{ orderId: string; update: OrderUpdate }>) => {\r\n      const { orderId, update } = action.payload;\r\n      state.orderUpdates[orderId] = update;\r\n    },\r\n    updateDriverLocation: (state, action: PayloadAction<{ driverId: string; location: { lat: number; lng: number } }>) => {\r\n      const { driverId, location } = action.payload;\r\n      state.driverLocations[driverId] = {\r\n        ...location,\r\n        timestamp: Date.now(),\r\n      };\r\n    },\r\n    setConnected: (state, action: PayloadAction<boolean>) => {\r\n      state.isConnected = action.payload;\r\n      state.lastHeartbeat = Date.now();\r\n    },\r\n  },\r\n});\r\n\r\nexport const { setConnection, updateOrderRealTime, updateDriverLocation, setConnected } = realTimeSlice.actions;\r\nexport default realTimeSlice;\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;AAED;;AAiBA,MAAM,eAA8B;IAClC,aAAa,CAAC;IACd,cAAc,CAAC;IACf,iBAAiB,CAAC;IAClB,aAAa;IACb,eAAe;AACjB;AAEA,MAAM,gBAAgB,CAAA,GAAA,6MAAA,CAAA,cAAW,AAAD,EAAE;IAChC,MAAM;IACN;IACA,UAAU;QACR,eAAe,CAAC,OAAO;YACrB,MAAM,EAAE,EAAE,EAAE,SAAS,EAAE,GAAG,OAAO,OAAO;YACxC,MAAM,WAAW,CAAC,GAAG,GAAG;QAC1B;QACA,qBAAqB,CAAC,OAAO;YAC3B,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,OAAO,OAAO;YAC1C,MAAM,YAAY,CAAC,QAAQ,GAAG;QAChC;QACA,sBAAsB,CAAC,OAAO;YAC5B,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,OAAO;YAC7C,MAAM,eAAe,CAAC,SAAS,GAAG;gBAChC,GAAG,QAAQ;gBACX,WAAW,KAAK,GAAG;YACrB;QACF;QACA,cAAc,CAAC,OAAO;YACpB,MAAM,WAAW,GAAG,OAAO,OAAO;YAClC,MAAM,aAAa,GAAG,KAAK,GAAG;QAChC;IACF;AACF;AAEO,MAAM,EAAE,aAAa,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,YAAY,EAAE,GAAG,cAAc,OAAO;uCAChG", "debugId": null}}, {"offset": {"line": 3302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/slices/analyticsSlice.ts"], "sourcesContent": ["/**\r\n * Analytics Slice - Analytics and reporting\r\n */\r\n\r\nimport { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\r\n\r\nexport interface AnalyticsMetrics {\r\n  totalRevenue: number;\r\n  totalOrders: number;\r\n  averageOrderValue: number;\r\n  customerCount: number;\r\n  [key: string]: number | string | boolean;\r\n}\r\n\r\nexport interface AnalyticsReport {\r\n  id: string;\r\n  title: string;\r\n  type: 'revenue' | 'orders' | 'customers' | 'performance';\r\n  data: Record<string, unknown>;\r\n  generatedAt: string;\r\n}\r\n\r\nexport interface AnalyticsState {\r\n  metrics: AnalyticsMetrics;\r\n  reports: AnalyticsReport[];\r\n  loading: boolean;\r\n  error: string | null;\r\n}\r\n\r\nconst initialState: AnalyticsState = {\r\n  metrics: {\r\n    totalRevenue: 0,\r\n    totalOrders: 0,\r\n    averageOrderValue: 0,\r\n    customerCount: 0,\r\n  },\r\n  reports: [],\r\n  loading: false,\r\n  error: null,\r\n};\r\n\r\ninterface FetchAnalyticsParams {\r\n  dateRange?: {\r\n    start: string;\r\n    end: string;\r\n  };\r\n  type?: 'revenue' | 'orders' | 'customers' | 'performance';\r\n}\r\n\r\nexport const fetchAnalytics = createAsyncThunk(\r\n  'analytics/fetchAnalytics',\r\n  async (params: FetchAnalyticsParams, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Implement actual analytics API call\r\n      console.log('Fetching analytics with params:', params);\r\n      return {\r\n        metrics: {\r\n          totalRevenue: 0,\r\n          totalOrders: 0,\r\n          averageOrderValue: 0,\r\n          customerCount: 0,\r\n        },\r\n        reports: []\r\n      };\r\n    } catch (error) {\r\n      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch analytics');\r\n    }\r\n  }\r\n);\r\n\r\nconst analyticsSlice = createSlice({\r\n  name: 'analytics',\r\n  initialState,\r\n  reducers: {},\r\n  extraReducers: (builder) => {\r\n    builder\r\n      .addCase(fetchAnalytics.pending, (state) => {\r\n        state.loading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(fetchAnalytics.fulfilled, (state, action) => {\r\n        state.metrics = action.payload.metrics;\r\n        state.reports = action.payload.reports;\r\n        state.loading = false;\r\n      })\r\n      .addCase(fetchAnalytics.rejected, (state, action) => {\r\n        state.loading = false;\r\n        state.error = action.payload as string;\r\n      });\r\n  },\r\n});\r\n\r\nexport default analyticsSlice;\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AAED;;AAyBA,MAAM,eAA+B;IACnC,SAAS;QACP,cAAc;QACd,aAAa;QACb,mBAAmB;QACnB,eAAe;IACjB;IACA,SAAS,EAAE;IACX,SAAS;IACT,OAAO;AACT;AAUO,MAAM,iBAAiB,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EAC3C,4BACA,OAAO,QAA8B,EAAE,eAAe,EAAE;IACtD,IAAI;QACF,4CAA4C;QAC5C,QAAQ,GAAG,CAAC,mCAAmC;QAC/C,OAAO;YACL,SAAS;gBACP,cAAc;gBACd,aAAa;gBACb,mBAAmB;gBACnB,eAAe;YACjB;YACA,SAAS,EAAE;QACb;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAClE;AACF;AAGF,MAAM,iBAAiB,CAAA,GAAA,6MAAA,CAAA,cAAW,AAAD,EAAE;IACjC,MAAM;IACN;IACA,UAAU,CAAC;IACX,eAAe,CAAC;QACd,QACG,OAAO,CAAC,eAAe,OAAO,EAAE,CAAC;YAChC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,eAAe,SAAS,EAAE,CAAC,OAAO;YACzC,MAAM,OAAO,GAAG,OAAO,OAAO,CAAC,OAAO;YACtC,MAAM,OAAO,GAAG,OAAO,OAAO,CAAC,OAAO;YACtC,MAAM,OAAO,GAAG;QAClB,GACC,OAAO,CAAC,eAAe,QAAQ,EAAE,CAAC,OAAO;YACxC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;IACJ;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 3366, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/slices/notificationsSlice.ts"], "sourcesContent": ["/**\r\n * Notifications Slice - Push notifications and alerts\r\n */\r\n\r\nimport { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\n\r\nexport interface Notification {\r\n  id: string;\r\n  type: 'order' | 'payment' | 'system' | 'promotion';\r\n  title: string;\r\n  message: string;\r\n  read: boolean;\r\n  createdAt: string;\r\n  data?: Record<string, unknown>;\r\n}\r\n\r\nexport interface NotificationsState {\r\n  notifications: Notification[];\r\n  unreadCount: number;\r\n  loading: boolean;\r\n  error: string | null;\r\n}\r\n\r\nconst initialState: NotificationsState = {\r\n  notifications: [],\r\n  unreadCount: 0,\r\n  loading: false,\r\n  error: null,\r\n};\r\n\r\nconst notificationsSlice = createSlice({\r\n  name: 'notifications',\r\n  initialState,\r\n  reducers: {\r\n    addNotification: (state, action: PayloadAction<Notification>) => {\r\n      state.notifications.unshift(action.payload);\r\n      if (!action.payload.read) {\r\n        state.unreadCount += 1;\r\n      }\r\n    },\r\n    markAsRead: (state, action: PayloadAction<string>) => {\r\n      const notification = state.notifications.find(n => n.id === action.payload);\r\n      if (notification && !notification.read) {\r\n        notification.read = true;\r\n        state.unreadCount -= 1;\r\n      }\r\n    },\r\n    markAllAsRead: (state) => {\r\n      state.notifications.forEach(n => n.read = true);\r\n      state.unreadCount = 0;\r\n    },\r\n    removeNotification: (state, action: PayloadAction<string>) => {\r\n      const index = state.notifications.findIndex(n => n.id === action.payload);\r\n      if (index >= 0) {\r\n        const notification = state.notifications[index];\r\n        if (!notification.read) {\r\n          state.unreadCount -= 1;\r\n        }\r\n        state.notifications.splice(index, 1);\r\n      }\r\n    },\r\n  },\r\n});\r\n\r\nexport const { addNotification, markAsRead, markAllAsRead, removeNotification } = notificationsSlice.actions;\r\nexport default notificationsSlice;\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;AAED;;AAmBA,MAAM,eAAmC;IACvC,eAAe,EAAE;IACjB,aAAa;IACb,SAAS;IACT,OAAO;AACT;AAEA,MAAM,qBAAqB,CAAA,GAAA,6MAAA,CAAA,cAAW,AAAD,EAAE;IACrC,MAAM;IACN;IACA,UAAU;QACR,iBAAiB,CAAC,OAAO;YACvB,MAAM,aAAa,CAAC,OAAO,CAAC,OAAO,OAAO;YAC1C,IAAI,CAAC,OAAO,OAAO,CAAC,IAAI,EAAE;gBACxB,MAAM,WAAW,IAAI;YACvB;QACF;QACA,YAAY,CAAC,OAAO;YAClB,MAAM,eAAe,MAAM,aAAa,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,OAAO;YAC1E,IAAI,gBAAgB,CAAC,aAAa,IAAI,EAAE;gBACtC,aAAa,IAAI,GAAG;gBACpB,MAAM,WAAW,IAAI;YACvB;QACF;QACA,eAAe,CAAC;YACd,MAAM,aAAa,CAAC,OAAO,CAAC,CAAA,IAAK,EAAE,IAAI,GAAG;YAC1C,MAAM,WAAW,GAAG;QACtB;QACA,oBAAoB,CAAC,OAAO;YAC1B,MAAM,QAAQ,MAAM,aAAa,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,OAAO,OAAO;YACxE,IAAI,SAAS,GAAG;gBACd,MAAM,eAAe,MAAM,aAAa,CAAC,MAAM;gBAC/C,IAAI,CAAC,aAAa,IAAI,EAAE;oBACtB,MAAM,WAAW,IAAI;gBACvB;gBACA,MAAM,aAAa,CAAC,MAAM,CAAC,OAAO;YACpC;QACF;IACF;AACF;AAEO,MAAM,EAAE,eAAe,EAAE,UAAU,EAAE,aAAa,EAAE,kBAAkB,EAAE,GAAG,mBAAmB,OAAO;uCAC7F", "debugId": null}}, {"offset": {"line": 3427, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/slices/adminSlice.ts"], "sourcesContent": ["/**\r\n * Admin Slice - Admin panel state management\r\n */\r\n\r\nimport { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\r\nimport type { User, VendorProfile } from '@/types';\r\n\r\nexport interface AdminState {\r\n  dashboardStats: {\r\n    totalUsers: number;\r\n    totalVendors: number;\r\n    totalDrivers: number;\r\n    totalOrders: number;\r\n    totalRevenue: number;\r\n    activeOrders: number;\r\n    pendingVendors: number;\r\n    pendingDrivers: number;\r\n  };\r\n  users: User[];\r\n  vendors: VendorProfile[];\r\n  drivers: User[]; // Will be replaced with Driver type when implemented\r\n  loading: boolean;\r\n  error: string | null;\r\n  selectedItems: string[];\r\n  bulkActions: boolean;\r\n}\r\n\r\nconst initialState: AdminState = {\r\n  dashboardStats: {\r\n    totalUsers: 0,\r\n    totalVendors: 0,\r\n    totalDrivers: 0,\r\n    totalOrders: 0,\r\n    totalRevenue: 0,\r\n    activeOrders: 0,\r\n    pendingVendors: 0,\r\n    pendingDrivers: 0,\r\n  },\r\n  users: [],\r\n  vendors: [],\r\n  drivers: [],\r\n  loading: false,\r\n  error: null,\r\n  selectedItems: [],\r\n  bulkActions: false,\r\n};\r\n\r\nexport const fetchDashboardStats = createAsyncThunk(\r\n  'admin/fetchDashboardStats',\r\n  async (_, { rejectWithValue }) => {\r\n    try {\r\n      // TODO: Implement API call\r\n      return {\r\n        totalUsers: 0,\r\n        totalVendors: 0,\r\n        totalDrivers: 0,\r\n        totalOrders: 0,\r\n        totalRevenue: 0,\r\n        activeOrders: 0,\r\n        pendingVendors: 0,\r\n        pendingDrivers: 0,\r\n      };\r\n    } catch (error) {\r\n      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch dashboard stats');\r\n    }\r\n  }\r\n);\r\n\r\nconst adminSlice = createSlice({\r\n  name: 'admin',\r\n  initialState,\r\n  reducers: {\r\n    setSelectedItems: (state, action: PayloadAction<string[]>) => {\r\n      state.selectedItems = action.payload;\r\n    },\r\n    toggleBulkActions: (state) => {\r\n      state.bulkActions = !state.bulkActions;\r\n      if (!state.bulkActions) {\r\n        state.selectedItems = [];\r\n      }\r\n    },\r\n  },\r\n  extraReducers: (builder) => {\r\n    builder\r\n      .addCase(fetchDashboardStats.pending, (state) => {\r\n        state.loading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(fetchDashboardStats.fulfilled, (state, action) => {\r\n        state.dashboardStats = action.payload;\r\n        state.loading = false;\r\n      })\r\n      .addCase(fetchDashboardStats.rejected, (state, action) => {\r\n        state.loading = false;\r\n        state.error = action.payload as string;\r\n      });\r\n  },\r\n});\r\n\r\nexport const { setSelectedItems, toggleBulkActions } = adminSlice.actions;\r\nexport default adminSlice;\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;AAED;;AAuBA,MAAM,eAA2B;IAC/B,gBAAgB;QACd,YAAY;QACZ,cAAc;QACd,cAAc;QACd,aAAa;QACb,cAAc;QACd,cAAc;QACd,gBAAgB;QAChB,gBAAgB;IAClB;IACA,OAAO,EAAE;IACT,SAAS,EAAE;IACX,SAAS,EAAE;IACX,SAAS;IACT,OAAO;IACP,eAAe,EAAE;IACjB,aAAa;AACf;AAEO,MAAM,sBAAsB,CAAA,GAAA,6MAAA,CAAA,mBAAgB,AAAD,EAChD,6BACA,OAAO,GAAG,EAAE,eAAe,EAAE;IAC3B,IAAI;QACF,2BAA2B;QAC3B,OAAO;YACL,YAAY;YACZ,cAAc;YACd,cAAc;YACd,aAAa;YACb,cAAc;YACd,cAAc;YACd,gBAAgB;YAChB,gBAAgB;QAClB;IACF,EAAE,OAAO,OAAO;QACd,OAAO,gBAAgB,iBAAiB,QAAQ,MAAM,OAAO,GAAG;IAClE;AACF;AAGF,MAAM,aAAa,CAAA,GAAA,6MAAA,CAAA,cAAW,AAAD,EAAE;IAC7B,MAAM;IACN;IACA,UAAU;QACR,kBAAkB,CAAC,OAAO;YACxB,MAAM,aAAa,GAAG,OAAO,OAAO;QACtC;QACA,mBAAmB,CAAC;YAClB,MAAM,WAAW,GAAG,CAAC,MAAM,WAAW;YACtC,IAAI,CAAC,MAAM,WAAW,EAAE;gBACtB,MAAM,aAAa,GAAG,EAAE;YAC1B;QACF;IACF;IACA,eAAe,CAAC;QACd,QACG,OAAO,CAAC,oBAAoB,OAAO,EAAE,CAAC;YACrC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,oBAAoB,SAAS,EAAE,CAAC,OAAO;YAC9C,MAAM,cAAc,GAAG,OAAO,OAAO;YACrC,MAAM,OAAO,GAAG;QAClB,GACC,OAAO,CAAC,oBAAoB,QAAQ,EAAE,CAAC,OAAO;YAC7C,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;IACJ;AACF;AAEO,MAAM,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,GAAG,WAAW,OAAO;uCAC1D", "debugId": null}}, {"offset": {"line": 3511, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/slices/vendorSlice.ts"], "sourcesContent": ["/**\r\n * Vendor Slice - Vendor panel state management\r\n */\r\n\r\nimport { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\nimport { Restaurant, MenuItem, Order, VendorAnalytics } from '@/types';\r\n\r\nexport interface VendorState {\r\n  restaurant: Restaurant | null;\r\n  menu: MenuItem[];\r\n  orders: Order[];\r\n  analytics: VendorAnalytics | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n  menuEditMode: boolean;\r\n  selectedOrders: string[];\r\n}\r\n\r\nconst initialState: VendorState = {\r\n  restaurant: null,\r\n  menu: [],\r\n  orders: [],\r\n  analytics: null,\r\n  loading: false,\r\n  error: null,\r\n  menuEditMode: false,\r\n  selectedOrders: [],\r\n};\r\n\r\nconst vendorSlice = createSlice({\r\n  name: 'vendor',\r\n  initialState,\r\n  reducers: {\r\n    setRestaurant: (state, action: PayloadAction<Restaurant>) => {\r\n      state.restaurant = action.payload;\r\n    },\r\n    setMenu: (state, action: PayloadAction<MenuItem[]>) => {\r\n      state.menu = action.payload;\r\n    },\r\n    setMenuEditMode: (state, action: PayloadAction<boolean>) => {\r\n      state.menuEditMode = action.payload;\r\n    },\r\n    setSelectedOrders: (state, action: PayloadAction<string[]>) => {\r\n      state.selectedOrders = action.payload;\r\n    },\r\n  },\r\n});\r\n\r\nexport const { setRestaurant, setMenu, setMenuEditMode, setSelectedOrders } = vendorSlice.actions;\r\nexport default vendorSlice;\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;AAED;;AAcA,MAAM,eAA4B;IAChC,YAAY;IACZ,MAAM,EAAE;IACR,QAAQ,EAAE;IACV,WAAW;IACX,SAAS;IACT,OAAO;IACP,cAAc;IACd,gBAAgB,EAAE;AACpB;AAEA,MAAM,cAAc,CAAA,GAAA,6MAAA,CAAA,cAAW,AAAD,EAAE;IAC9B,MAAM;IACN;IACA,UAAU;QACR,eAAe,CAAC,OAAO;YACrB,MAAM,UAAU,GAAG,OAAO,OAAO;QACnC;QACA,SAAS,CAAC,OAAO;YACf,MAAM,IAAI,GAAG,OAAO,OAAO;QAC7B;QACA,iBAAiB,CAAC,OAAO;YACvB,MAAM,YAAY,GAAG,OAAO,OAAO;QACrC;QACA,mBAAmB,CAAC,OAAO;YACzB,MAAM,cAAc,GAAG,OAAO,OAAO;QACvC;IACF;AACF;AAEO,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,eAAe,EAAE,iBAAiB,EAAE,GAAG,YAAY,OAAO;uCAClF", "debugId": null}}, {"offset": {"line": 3561, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/slices/driverPanelSlice.ts"], "sourcesContent": ["/**\r\n * Driver Panel Slice - Driver panel state management\r\n */\r\n\r\nimport { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\nimport { User, Order } from '@/types';\r\n\r\nexport interface DriverProfile extends User {\r\n  role: 'driver';\r\n  vehicle: {\r\n    type: 'motorcycle' | 'bicycle' | 'car';\r\n    model: string;\r\n    plateNumber: string;\r\n  };\r\n  rating: number;\r\n  totalDeliveries: number;\r\n  status: 'pending' | 'approved' | 'active' | 'suspended';\r\n}\r\n\r\nexport interface DriverPanelState {\r\n  profile: DriverProfile | null;\r\n  currentDelivery: Order | null;\r\n  earnings: {\r\n    today: number;\r\n    week: number;\r\n    month: number;\r\n    total: number;\r\n  };\r\n  isOnline: boolean;\r\n  location: {\r\n    lat: number;\r\n    lng: number;\r\n    address: string;\r\n  } | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n}\r\n\r\nconst initialState: DriverPanelState = {\r\n  profile: null,\r\n  currentDelivery: null,\r\n  earnings: {\r\n    today: 0,\r\n    week: 0,\r\n    month: 0,\r\n    total: 0,\r\n  },\r\n  isOnline: false,\r\n  location: null,\r\n  loading: false,\r\n  error: null,\r\n};\r\n\r\nconst driverPanelSlice = createSlice({\r\n  name: 'driverPanel',\r\n  initialState,\r\n  reducers: {\r\n    setProfile: (state, action: PayloadAction<DriverProfile>) => {\r\n      state.profile = action.payload;\r\n    },\r\n    setCurrentDelivery: (state, action: PayloadAction<Order>) => {\r\n      state.currentDelivery = action.payload;\r\n    },\r\n    setOnlineStatus: (state, action: PayloadAction<boolean>) => {\r\n      state.isOnline = action.payload;\r\n    },\r\n    updateLocation: (state, action: PayloadAction<DriverPanelState['location']>) => {\r\n      state.location = action.payload;\r\n    },\r\n    updateEarnings: (state, action: PayloadAction<Partial<DriverPanelState['earnings']>>) => {\r\n      state.earnings = { ...state.earnings, ...action.payload };\r\n    },\r\n  },\r\n});\r\n\r\nexport const { setProfile, setCurrentDelivery, setOnlineStatus, updateLocation, updateEarnings } = driverPanelSlice.actions;\r\nexport default driverPanelSlice;\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;AAED;;AAkCA,MAAM,eAAiC;IACrC,SAAS;IACT,iBAAiB;IACjB,UAAU;QACR,OAAO;QACP,MAAM;QACN,OAAO;QACP,OAAO;IACT;IACA,UAAU;IACV,UAAU;IACV,SAAS;IACT,OAAO;AACT;AAEA,MAAM,mBAAmB,CAAA,GAAA,6MAAA,CAAA,cAAW,AAAD,EAAE;IACnC,MAAM;IACN;IACA,UAAU;QACR,YAAY,CAAC,OAAO;YAClB,MAAM,OAAO,GAAG,OAAO,OAAO;QAChC;QACA,oBAAoB,CAAC,OAAO;YAC1B,MAAM,eAAe,GAAG,OAAO,OAAO;QACxC;QACA,iBAAiB,CAAC,OAAO;YACvB,MAAM,QAAQ,GAAG,OAAO,OAAO;QACjC;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,QAAQ,GAAG,OAAO,OAAO;QACjC;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,QAAQ,GAAG;gBAAE,GAAG,MAAM,QAAQ;gBAAE,GAAG,OAAO,OAAO;YAAC;QAC1D;IACF;AACF;AAEO,MAAM,EAAE,UAAU,EAAE,kBAAkB,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,iBAAiB,OAAO;uCAC5G", "debugId": null}}, {"offset": {"line": 3622, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/slices/cmsSliceSimple.ts"], "sourcesContent": ["/**\r\n * Simple CMS Redux Slice - Test Version\r\n */\r\n\r\nimport { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\n\r\n// Simple types\r\nexport interface BlogPost {\r\n  id: number;\r\n  title: string;\r\n  slug: string;\r\n  content: string;\r\n  excerpt?: string;\r\n  status: 'draft' | 'published' | 'private' | 'scheduled' | 'trash';\r\n  author_name: string;\r\n  is_featured?: boolean;\r\n  view_count: number;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string;\r\n}\r\n\r\nexport interface StaticPage {\r\n  id: number;\r\n  title: string;\r\n  slug: string;\r\n  content: string;\r\n  excerpt?: string;\r\n  status: 'draft' | 'published' | 'private' | 'trash';\r\n  author_name: string;\r\n  show_in_navigation?: boolean;\r\n  navigation_label?: string;\r\n  menu_order?: number;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string;\r\n}\r\n\r\nexport interface Category {\r\n  id: number;\r\n  name: string;\r\n  slug: string;\r\n  description?: string;\r\n  post_count: number;\r\n}\r\n\r\nexport interface Tag {\r\n  id: number;\r\n  name: string;\r\n  slug: string;\r\n  description?: string;\r\n  post_count: number;\r\n}\r\n\r\nexport interface CMSStats {\r\n  totalPosts: number;\r\n  publishedPosts: number;\r\n  draftPosts: number;\r\n  totalPages: number;\r\n  publishedPages: number;\r\n  totalCategories: number;\r\n  totalTags: number;\r\n  totalViews: number;\r\n  trashedPosts: number;\r\n  trashedPages: number;\r\n}\r\n\r\nexport interface CMSState {\r\n  posts: BlogPost[];\r\n  pages: StaticPage[];\r\n  categories: Category[];\r\n  tags: Tag[];\r\n  stats: CMSStats;\r\n  loading: {\r\n    global: boolean;\r\n    posts: boolean;\r\n    pages: boolean;\r\n    categories: boolean;\r\n    tags: boolean;\r\n  };\r\n  activeTab: 'posts' | 'pages' | 'categories' | 'tags';\r\n  viewMode: 'all' | 'published' | 'draft' | 'trash';\r\n  error: string | null;\r\n  lastUpdated: string | null;\r\n}\r\n\r\nconst initialState: CMSState = {\r\n  posts: [],\r\n  pages: [],\r\n  categories: [],\r\n  tags: [],\r\n  stats: {\r\n    totalPosts: 0,\r\n    publishedPosts: 0,\r\n    draftPosts: 0,\r\n    totalPages: 0,\r\n    publishedPages: 0,\r\n    totalCategories: 0,\r\n    totalTags: 0,\r\n    totalViews: 0,\r\n    trashedPosts: 0,\r\n    trashedPages: 0,\r\n  },\r\n  loading: {\r\n    global: false,\r\n    posts: false,\r\n    pages: false,\r\n    categories: false,\r\n    tags: false,\r\n  },\r\n  activeTab: 'posts',\r\n  viewMode: 'all',\r\n  error: null,\r\n  lastUpdated: null,\r\n};\r\n\r\nconst cmsSlice = createSlice({\r\n  name: 'cms',\r\n  initialState,\r\n  reducers: {\r\n    setActiveTab: (state, action: PayloadAction<'posts' | 'pages' | 'categories' | 'tags'>) => {\r\n      state.activeTab = action.payload;\r\n      state.error = null;\r\n    },\r\n    \r\n    setViewMode: (state, action: PayloadAction<'all' | 'published' | 'draft' | 'trash'>) => {\r\n      state.viewMode = action.payload;\r\n      state.error = null;\r\n    },\r\n    \r\n    setLoading: (state, action: PayloadAction<boolean>) => {\r\n      state.loading.global = action.payload;\r\n    },\r\n    \r\n    setPosts: (state, action: PayloadAction<BlogPost[]>) => {\r\n      state.posts = action.payload;\r\n    },\r\n    \r\n    setPages: (state, action: PayloadAction<StaticPage[]>) => {\r\n      state.pages = action.payload;\r\n    },\r\n    \r\n    setCategories: (state, action: PayloadAction<Category[]>) => {\r\n      state.categories = action.payload;\r\n    },\r\n    \r\n    setTags: (state, action: PayloadAction<Tag[]>) => {\r\n      state.tags = action.payload;\r\n    },\r\n    \r\n    setStats: (state, action: PayloadAction<CMSStats>) => {\r\n      state.stats = action.payload;\r\n    },\r\n    \r\n    setError: (state, action: PayloadAction<string | null>) => {\r\n      state.error = action.payload;\r\n    },\r\n    \r\n    clearError: (state) => {\r\n      state.error = null;\r\n    },\r\n  },\r\n});\r\n\r\nexport const { \r\n  setActiveTab, \r\n  setViewMode, \r\n  setLoading, \r\n  setPosts, \r\n  setPages, \r\n  setCategories, \r\n  setTags, \r\n  setStats, \r\n  setError, \r\n  clearError \r\n} = cmsSlice.actions;\r\n\r\n// Selectors\r\nexport const selectCMSState = (state: { cms: CMSState }) => state.cms;\r\nexport const selectCMSPosts = (state: { cms: CMSState }) => state.cms.posts;\r\nexport const selectCMSPages = (state: { cms: CMSState }) => state.cms.pages;\r\nexport const selectCMSCategories = (state: { cms: CMSState }) => state.cms.categories;\r\nexport const selectCMSTags = (state: { cms: CMSState }) => state.cms.tags;\r\nexport const selectCMSStats = (state: { cms: CMSState }) => state.cms.stats;\r\nexport const selectCMSLoading = (state: { cms: CMSState }) => state.cms.loading;\r\nexport const selectIsLoading = (state: { cms: CMSState }) => {\r\n  const loading = state.cms.loading;\r\n  return loading.global || loading.posts || loading.pages || loading.categories || loading.tags;\r\n};\r\nexport const selectCMSActiveTab = (state: { cms: CMSState }) => state.cms.activeTab;\r\nexport const selectCMSViewMode = (state: { cms: CMSState }) => state.cms.viewMode;\r\nexport const selectCMSError = (state: { cms: CMSState }) => state.cms.error;\r\n\r\n\r\n\r\nexport default cmsSlice.reducer;\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;;;;;;;;;;;;;;;;;AAED;;AAkFA,MAAM,eAAyB;IAC7B,OAAO,EAAE;IACT,OAAO,EAAE;IACT,YAAY,EAAE;IACd,MAAM,EAAE;IACR,OAAO;QACL,YAAY;QACZ,gBAAgB;QAChB,YAAY;QACZ,YAAY;QACZ,gBAAgB;QAChB,iBAAiB;QACjB,WAAW;QACX,YAAY;QACZ,cAAc;QACd,cAAc;IAChB;IACA,SAAS;QACP,QAAQ;QACR,OAAO;QACP,OAAO;QACP,YAAY;QACZ,MAAM;IACR;IACA,WAAW;IACX,UAAU;IACV,OAAO;IACP,aAAa;AACf;AAEA,MAAM,WAAW,CAAA,GAAA,6MAAA,CAAA,cAAW,AAAD,EAAE;IAC3B,MAAM;IACN;IACA,UAAU;QACR,cAAc,CAAC,OAAO;YACpB,MAAM,SAAS,GAAG,OAAO,OAAO;YAChC,MAAM,KAAK,GAAG;QAChB;QAEA,aAAa,CAAC,OAAO;YACnB,MAAM,QAAQ,GAAG,OAAO,OAAO;YAC/B,MAAM,KAAK,GAAG;QAChB;QAEA,YAAY,CAAC,OAAO;YAClB,MAAM,OAAO,CAAC,MAAM,GAAG,OAAO,OAAO;QACvC;QAEA,UAAU,CAAC,OAAO;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEA,UAAU,CAAC,OAAO;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEA,eAAe,CAAC,OAAO;YACrB,MAAM,UAAU,GAAG,OAAO,OAAO;QACnC;QAEA,SAAS,CAAC,OAAO;YACf,MAAM,IAAI,GAAG,OAAO,OAAO;QAC7B;QAEA,UAAU,CAAC,OAAO;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEA,UAAU,CAAC,OAAO;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;QAEA,YAAY,CAAC;YACX,MAAM,KAAK,GAAG;QAChB;IACF;AACF;AAEO,MAAM,EACX,YAAY,EACZ,WAAW,EACX,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,aAAa,EACb,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACX,GAAG,SAAS,OAAO;AAGb,MAAM,iBAAiB,CAAC,QAA6B,MAAM,GAAG;AAC9D,MAAM,iBAAiB,CAAC,QAA6B,MAAM,GAAG,CAAC,KAAK;AACpE,MAAM,iBAAiB,CAAC,QAA6B,MAAM,GAAG,CAAC,KAAK;AACpE,MAAM,sBAAsB,CAAC,QAA6B,MAAM,GAAG,CAAC,UAAU;AAC9E,MAAM,gBAAgB,CAAC,QAA6B,MAAM,GAAG,CAAC,IAAI;AAClE,MAAM,iBAAiB,CAAC,QAA6B,MAAM,GAAG,CAAC,KAAK;AACpE,MAAM,mBAAmB,CAAC,QAA6B,MAAM,GAAG,CAAC,OAAO;AACxE,MAAM,kBAAkB,CAAC;IAC9B,MAAM,UAAU,MAAM,GAAG,CAAC,OAAO;IACjC,OAAO,QAAQ,MAAM,IAAI,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,QAAQ,UAAU,IAAI,QAAQ,IAAI;AAC/F;AACO,MAAM,qBAAqB,CAAC,QAA6B,MAAM,GAAG,CAAC,SAAS;AAC5E,MAAM,oBAAoB,CAAC,QAA6B,MAAM,GAAG,CAAC,QAAQ;AAC1E,MAAM,iBAAiB,CAAC,QAA6B,MAAM,GAAG,CAAC,KAAK;uCAI5D,SAAS,OAAO", "debugId": null}}, {"offset": {"line": 3742, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/api/apiSlice.ts"], "sourcesContent": ["/**\r\n * API Slice - RTK Query configuration\r\n * Centralized API management for all Tap2Go endpoints\r\n */\r\n\r\nimport { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';\r\n\r\n// Simple base query\r\nconst baseQuery = fetchBaseQuery({\r\n  baseUrl: '/api',\r\n  prepareHeaders: (headers) => {\r\n    headers.set('content-type', 'application/json');\r\n    return headers;\r\n  },\r\n});\r\n\r\n// Main API slice\r\nexport const apiSlice = createApi({\r\n  reducerPath: 'api',\r\n  baseQuery,\r\n  tagTypes: [\r\n    'User',\r\n    'Restaurant',\r\n    'MenuItem',\r\n    'Order',\r\n    'Driver',\r\n    'Customer',\r\n    'Vendor',\r\n    'Admin',\r\n    'Analytics',\r\n    'Notification',\r\n  ],\r\n  endpoints: (builder) => ({\r\n    // Basic endpoints - will be expanded later\r\n    getRestaurants: builder.query<unknown[], unknown>({\r\n      query: () => '/restaurants',\r\n      providesTags: ['Restaurant'],\r\n    }),\r\n\r\n    getOrders: builder.query<unknown[], unknown>({\r\n      query: () => '/orders',\r\n      providesTags: ['Order'],\r\n    }),\r\n  }),\r\n});\r\n\r\n// Export hooks for usage in components\r\nexport const {\r\n  useGetRestaurantsQuery,\r\n  useGetOrdersQuery,\r\n} = apiSlice;\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;AAED;AAAA;;AAEA,oBAAoB;AACpB,MAAM,YAAY,CAAA,GAAA,kMAAA,CAAA,iBAAc,AAAD,EAAE;IAC/B,SAAS;IACT,gBAAgB,CAAC;QACf,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,OAAO;IACT;AACF;AAGO,MAAM,WAAW,CAAA,GAAA,oOAAA,CAAA,YAAS,AAAD,EAAE;IAChC,aAAa;IACb;IACA,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW,CAAC,UAAY,CAAC;YACvB,2CAA2C;YAC3C,gBAAgB,QAAQ,KAAK,CAAqB;gBAChD,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAa;YAC9B;YAEA,WAAW,QAAQ,KAAK,CAAqB;gBAC3C,OAAO,IAAM;gBACb,cAAc;oBAAC;iBAAQ;YACzB;QACF,CAAC;AACH;AAGO,MAAM,EACX,sBAAsB,EACtB,iBAAiB,EAClB,GAAG", "debugId": null}}, {"offset": {"line": 3802, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/middleware/analyticsMiddleware.ts"], "sourcesContent": ["/**\r\n * Analytics Middleware - Track user actions and events\r\n */\r\n\r\nimport { Middleware, AnyAction } from '@reduxjs/toolkit';\r\n\r\nexport const analyticsMiddleware: Middleware = (store) => (next) => (action) => {\r\n  // Type assertion for action\r\n  const typedAction = action as AnyAction;\r\n\r\n  // Track specific actions for analytics\r\n  const trackableActions = [\r\n    'cart/addToCart',\r\n    'cart/removeFromCart',\r\n    'orders/createOrder',\r\n    'auth/signIn',\r\n    'auth/signUp',\r\n    'restaurants/setCurrentRestaurant',\r\n  ];\r\n\r\n  if (trackableActions.includes(typedAction.type)) {\r\n    // Send analytics event\r\n    try {\r\n      // TODO: Integrate with your analytics service (Google Analytics, Mixpanel, etc.)\r\n      console.log('Analytics Event:', {\r\n        action: typedAction.type,\r\n        payload: typedAction.payload,\r\n        timestamp: new Date().toISOString(),\r\n        userId: store.getState().auth.user?.id,\r\n      });\r\n    } catch (error) {\r\n      console.error('Analytics tracking error:', error);\r\n    }\r\n  }\r\n\r\n  return next(action);\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAIM,MAAM,sBAAkC,CAAC,QAAU,CAAC,OAAS,CAAC;YACnE,4BAA4B;YAC5B,MAAM,cAAc;YAEpB,uCAAuC;YACvC,MAAM,mBAAmB;gBACvB;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,IAAI,iBAAiB,QAAQ,CAAC,YAAY,IAAI,GAAG;gBAC/C,uBAAuB;gBACvB,IAAI;oBACF,iFAAiF;oBACjF,QAAQ,GAAG,CAAC,oBAAoB;wBAC9B,QAAQ,YAAY,IAAI;wBACxB,SAAS,YAAY,OAAO;wBAC5B,WAAW,IAAI,OAAO,WAAW;wBACjC,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,EAAE;oBACtC;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC7C;YACF;YAEA,OAAO,KAAK;QACd", "debugId": null}}, {"offset": {"line": 3844, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/middleware/realTimeMiddleware.ts"], "sourcesContent": ["/**\r\n * Real-time Middleware - Handle Firebase real-time updates\r\n */\r\n\r\nimport { Middleware, AnyAction, MiddlewareAPI, Dispatch, UnknownAction } from '@reduxjs/toolkit';\r\nimport type { User } from '@/types';\r\nimport type { RootState } from '../index';\r\n\r\nexport const realTimeMiddleware: Middleware = (store) => (next) => (action) => {\r\n  // Type assertion for action\r\n  const typedAction = action as AnyAction;\r\n\r\n  // Handle real-time connection setup\r\n  if (typedAction.type === 'auth/setUser' && typedAction.payload) {\r\n    // Set up real-time listeners when user logs in\r\n    setupRealTimeListeners(store as MiddlewareAPI<Dispatch<UnknownAction>, RootState>, typedAction.payload);\r\n  }\r\n\r\n  // Handle real-time disconnection\r\n  if (typedAction.type === 'auth/clearAuth') {\r\n    // Clean up real-time listeners when user logs out\r\n    cleanupRealTimeListeners();\r\n  }\r\n\r\n  return next(action);\r\n};\r\n\r\nlet realTimeListeners: Array<() => void> = [];\r\n\r\nfunction setupRealTimeListeners(store: MiddlewareAPI<Dispatch<UnknownAction>, RootState>, user: User) {\r\n  // TODO: Set up Firebase real-time listeners based on user role\r\n  console.log('Setting up real-time listeners for user:', user.id);\r\n\r\n  // Example: Listen to order updates for vendors\r\n  if (user.role === 'vendor') {\r\n    // Set up vendor-specific listeners\r\n  }\r\n\r\n  // Example: Listen to delivery updates for drivers\r\n  if (user.role === 'driver') {\r\n    // Set up driver-specific listeners\r\n  }\r\n\r\n  // Example: Listen to order status for customers\r\n  if (user.role === 'customer') {\r\n    // Set up customer-specific listeners\r\n  }\r\n}\r\n\r\nfunction cleanupRealTimeListeners() {\r\n  realTimeListeners.forEach(unsubscribe => unsubscribe());\r\n  realTimeListeners = [];\r\n  console.log('Cleaned up real-time listeners');\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAMM,MAAM,qBAAiC,CAAC,QAAU,CAAC,OAAS,CAAC;YAClE,4BAA4B;YAC5B,MAAM,cAAc;YAEpB,oCAAoC;YACpC,IAAI,YAAY,IAAI,KAAK,kBAAkB,YAAY,OAAO,EAAE;gBAC9D,+CAA+C;gBAC/C,uBAAuB,OAA4D,YAAY,OAAO;YACxG;YAEA,iCAAiC;YACjC,IAAI,YAAY,IAAI,KAAK,kBAAkB;gBACzC,kDAAkD;gBAClD;YACF;YAEA,OAAO,KAAK;QACd;AAEA,IAAI,oBAAuC,EAAE;AAE7C,SAAS,uBAAuB,KAAwD,EAAE,IAAU;IAClG,+DAA+D;IAC/D,QAAQ,GAAG,CAAC,4CAA4C,KAAK,EAAE;IAE/D,+CAA+C;IAC/C,IAAI,KAAK,IAAI,KAAK,UAAU;IAC1B,mCAAmC;IACrC;IAEA,kDAAkD;IAClD,IAAI,KAAK,IAAI,KAAK,UAAU;IAC1B,mCAAmC;IACrC;IAEA,gDAAgD;IAChD,IAAI,KAAK,IAAI,KAAK,YAAY;IAC5B,qCAAqC;IACvC;AACF;AAEA,SAAS;IACP,kBAAkB,OAAO,CAAC,CAAA,cAAe;IACzC,oBAAoB,EAAE;IACtB,QAAQ,GAAG,CAAC;AACd", "debugId": null}}, {"offset": {"line": 3895, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/middleware/errorMiddleware.ts"], "sourcesContent": ["/**\r\n * Error Middleware - Centralized error handling\r\n */\r\n\r\nimport { Middleware, AnyAction } from '@reduxjs/toolkit';\r\nimport { showErrorNotification } from '../slices/uiSlice';\r\n\r\nexport const errorMiddleware: Middleware = (store) => (next) => (action) => {\r\n  // Type assertion for action\r\n  const typedAction = action as AnyAction;\r\n\r\n  // <PERSON>le rejected async thunks\r\n  if (typedAction.type.endsWith('/rejected')) {\r\n    const error = typedAction.payload || typedAction.error?.message || 'An error occurred';\r\n\r\n    // Show error notification\r\n    store.dispatch(showErrorNotification({\r\n      title: 'Error',\r\n      message: error,\r\n    }));\r\n\r\n    // Log error for debugging\r\n    console.error('Redux Error:', {\r\n      action: typedAction.type,\r\n      error: error,\r\n      timestamp: new Date().toISOString(),\r\n    });\r\n  }\r\n\r\n  return next(action);\r\n};\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAGD;;AAEO,MAAM,kBAA8B,CAAC,QAAU,CAAC,OAAS,CAAC;YAC/D,4BAA4B;YAC5B,MAAM,cAAc;YAEpB,+BAA+B;YAC/B,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,cAAc;gBAC1C,MAAM,QAAQ,YAAY,OAAO,IAAI,YAAY,KAAK,EAAE,WAAW;gBAEnE,0BAA0B;gBAC1B,MAAM,QAAQ,CAAC,CAAA,GAAA,mJAAA,CAAA,wBAAqB,AAAD,EAAE;oBACnC,OAAO;oBACP,SAAS;gBACX;gBAEA,0BAA0B;gBAC1B,QAAQ,KAAK,CAAC,gBAAgB;oBAC5B,QAAQ,YAAY,IAAI;oBACxB,OAAO;oBACP,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;YAEA,OAAO,KAAK;QACd", "debugId": null}}, {"offset": {"line": 3931, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/middleware/serializationMiddleware.ts"], "sourcesContent": ["/**\r\n * Serialization Middleware - Automatically handles Firebase Timestamps\r\n * Converts all Firebase Timestamps to ISO strings before they reach Redux\r\n */\r\n\r\nimport { Middleware, AnyAction } from '@reduxjs/toolkit';\r\nimport { Timestamp } from 'firebase/firestore';\r\n\r\n/**\r\n * Recursively serialize Firebase Timestamps in any object\r\n */\r\nconst serializeFirebaseData = (obj: unknown): unknown => {\r\n  if (obj === null || obj === undefined) {\r\n    return obj;\r\n  }\r\n\r\n  // Handle Firebase Timestamp\r\n  if (obj instanceof Timestamp) {\r\n    return obj.toDate().toISOString();\r\n  }\r\n\r\n  // Handle Date objects\r\n  if (obj instanceof Date) {\r\n    return obj.toISOString();\r\n  }\r\n\r\n  // Handle arrays\r\n  if (Array.isArray(obj)) {\r\n    return obj.map(serializeFirebaseData);\r\n  }\r\n\r\n  // Handle objects\r\n  if (typeof obj === 'object' && obj !== null && obj.constructor === Object) {\r\n    const serialized: Record<string, unknown> = {};\r\n    for (const key in obj) {\r\n      if (Object.prototype.hasOwnProperty.call(obj, key)) {\r\n        serialized[key] = serializeFirebaseData((obj as Record<string, unknown>)[key]);\r\n      }\r\n    }\r\n    return serialized;\r\n  }\r\n\r\n  // Return primitive values as-is\r\n  return obj;\r\n};\r\n\r\n/**\r\n * Middleware that automatically serializes Firebase data in actions\r\n */\r\nexport const serializationMiddleware: Middleware = () => (next) => (action) => {\r\n  // Type assertion for action\r\n  const typedAction = action as AnyAction;\r\n\r\n  // List of actions that might contain Firebase data\r\n  const actionsToSerialize = [\r\n    'auth/setUser',\r\n    'auth/syncAuthState',\r\n    'auth/updateUserData',\r\n    'auth/signIn/fulfilled',\r\n    'auth/signUp/fulfilled',\r\n    'auth/updateUserProfile/fulfilled',\r\n  ];\r\n\r\n  // Check if this action needs serialization\r\n  if (actionsToSerialize.includes(typedAction.type)) {\r\n    // Create a new action with serialized payload\r\n    const serializedAction: AnyAction = {\r\n      ...typedAction,\r\n      payload: serializeFirebaseData(typedAction.payload),\r\n    };\r\n\r\n    return next(serializedAction);\r\n  }\r\n\r\n  // For other actions, just pass them through\r\n  return next(action);\r\n};\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAGD;AAAA;;AAEA;;CAEC,GACD,MAAM,wBAAwB,CAAC;IAC7B,IAAI,QAAQ,QAAQ,QAAQ,WAAW;QACrC,OAAO;IACT;IAEA,4BAA4B;IAC5B,IAAI,eAAe,sKAAA,CAAA,YAAS,EAAE;QAC5B,OAAO,IAAI,MAAM,GAAG,WAAW;IACjC;IAEA,sBAAsB;IACtB,IAAI,eAAe,MAAM;QACvB,OAAO,IAAI,WAAW;IACxB;IAEA,gBAAgB;IAChB,IAAI,MAAM,OAAO,CAAC,MAAM;QACtB,OAAO,IAAI,GAAG,CAAC;IACjB;IAEA,iBAAiB;IACjB,IAAI,OAAO,QAAQ,YAAY,QAAQ,QAAQ,IAAI,WAAW,KAAK,QAAQ;QACzE,MAAM,aAAsC,CAAC;QAC7C,IAAK,MAAM,OAAO,IAAK;YACrB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,MAAM;gBAClD,UAAU,CAAC,IAAI,GAAG,sBAAsB,AAAC,GAA+B,CAAC,IAAI;YAC/E;QACF;QACA,OAAO;IACT;IAEA,gCAAgC;IAChC,OAAO;AACT;AAKO,MAAM,0BAAsC,IAAM,CAAC,OAAS,CAAC;YAClE,4BAA4B;YAC5B,MAAM,cAAc;YAEpB,mDAAmD;YACnD,MAAM,qBAAqB;gBACzB;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,2CAA2C;YAC3C,IAAI,mBAAmB,QAAQ,CAAC,YAAY,IAAI,GAAG;gBACjD,8CAA8C;gBAC9C,MAAM,mBAA8B;oBAClC,GAAG,WAAW;oBACd,SAAS,sBAAsB,YAAY,OAAO;gBACpD;gBAEA,OAAO,KAAK;YACd;YAEA,4CAA4C;YAC5C,OAAO,KAAK;QACd", "debugId": null}}, {"offset": {"line": 4004, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/index.ts"], "sourcesContent": ["/**\r\n * Tap2Go Redux Store Configuration\r\n * Enterprise-grade state management for FoodPanda-level complexity\r\n * \r\n * Architecture:\r\n * - Respects existing Firebase/Auth patterns\r\n * - Integrates with existing service layer\r\n * - Supports multi-panel architecture (Admin, Vendor, Driver, Customer)\r\n * - Optimized for real-time updates and scalability\r\n */\r\n\r\nimport { configureStore, combineReducers } from '@reduxjs/toolkit';\r\nimport { persistStore, persistReducer, FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER } from 'redux-persist';\r\nimport storage from 'redux-persist/lib/storage';\r\n\r\n// Core slices\r\nimport authSlice from './slices/authSlice';\r\nimport uiSlice from './slices/uiSlice';\r\nimport cartSlice from './slices/cartSlice';\r\n\r\n// Business logic slices\r\nimport ordersSlice from './slices/ordersSlice';\r\nimport restaurantsSlice from './slices/restaurantsSlice';\r\nimport driversSlice from './slices/driversSlice';\r\nimport customersSlice from './slices/customersSlice';\r\n\r\n// Advanced features\r\nimport realTimeSlice from './slices/realTimeSlice';\r\nimport analyticsSlice from './slices/analyticsSlice';\r\nimport notificationsSlice from './slices/notificationsSlice';\r\n\r\n// Panel-specific slices\r\nimport adminSlice from './slices/adminSlice';\r\nimport vendorSlice from './slices/vendorSlice';\r\nimport driverPanelSlice from './slices/driverPanelSlice';\r\n\r\n// CMS slice\r\nimport cmsReducer from './slices/cmsSliceSimple';\r\n\r\n// API slice for RTK Query\r\nimport { apiSlice } from './api/apiSlice';\r\n\r\n// Middleware\r\nimport { analyticsMiddleware } from './middleware/analyticsMiddleware';\r\nimport { realTimeMiddleware } from './middleware/realTimeMiddleware';\r\nimport { errorMiddleware } from './middleware/errorMiddleware';\r\nimport { serializationMiddleware } from './middleware/serializationMiddleware';\r\n\r\n// Root reducer configuration\r\nconst rootReducer = combineReducers({\r\n  // Core state\r\n  auth: authSlice.reducer,\r\n  ui: uiSlice.reducer,\r\n  cart: cartSlice.reducer,\r\n  \r\n  // Business logic\r\n  orders: ordersSlice.reducer,\r\n  restaurants: restaurantsSlice.reducer,\r\n  drivers: driversSlice.reducer,\r\n  customers: customersSlice.reducer,\r\n  \r\n  // Advanced features\r\n  realTime: realTimeSlice.reducer,\r\n  analytics: analyticsSlice.reducer,\r\n  notifications: notificationsSlice.reducer,\r\n  \r\n  // Panel-specific\r\n  admin: adminSlice.reducer,\r\n  vendor: vendorSlice.reducer,\r\n  driverPanel: driverPanelSlice.reducer,\r\n\r\n  // CMS\r\n  cms: cmsReducer,\r\n\r\n  // API\r\n  api: apiSlice.reducer,\r\n});\r\n\r\n// Persistence configuration\r\nconst persistConfig = {\r\n  key: 'tap2go-root',\r\n  version: 1,\r\n  storage,\r\n  // Only persist essential data for performance\r\n  whitelist: ['cart', 'ui', 'auth'], // Don't persist real-time data\r\n  blacklist: ['api', 'realTime', 'analytics'], // Exclude API cache and real-time data\r\n};\r\n\r\nconst persistedReducer = persistReducer(persistConfig, rootReducer);\r\n\r\n// Store configuration\r\nexport const store = configureStore({\r\n  reducer: persistedReducer,\r\n  middleware: (getDefaultMiddleware) => {\r\n    const middleware = getDefaultMiddleware({\r\n      serializableCheck: process.env.NODE_ENV === 'development' ? false : {\r\n        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],\r\n        // In production, we can be more specific about what to ignore\r\n        ignoredActionPaths: ['meta.arg', 'meta.baseQueryMeta'],\r\n        ignoredPaths: ['meta.arg', 'meta.baseQueryMeta'],\r\n      },\r\n      // Enable immutability checks in development\r\n      immutableCheck: process.env.NODE_ENV === 'development',\r\n    });\r\n\r\n    return middleware\r\n      .concat(serializationMiddleware)\r\n      .concat(apiSlice.middleware)\r\n      .concat(analyticsMiddleware)\r\n      .concat(realTimeMiddleware)\r\n      .concat(errorMiddleware);\r\n  },\r\n\r\n  // Enable Redux DevTools in development\r\n  devTools: process.env.NODE_ENV === 'development',\r\n});\r\n\r\n// Persistor for redux-persist\r\nexport const persistor = persistStore(store);\r\n\r\n// Type definitions for TypeScript\r\nexport type RootState = ReturnType<typeof store.getState>;\r\nexport type AppDispatch = typeof store.dispatch;\r\n\r\n// Typed hooks (will be created in hooks file)\r\nexport type AppStore = typeof store;\r\n\r\n// Export all state types for proper TypeScript inference\r\nexport type { AuthState } from './slices/authSlice';\r\nexport type { UIState, Notification, Modal } from './slices/uiSlice';\r\nexport type { CartState } from './slices/cartSlice';\r\nexport type { OrdersState, Order, OrderFilters } from './slices/ordersSlice';\r\nexport type { RestaurantsState } from './slices/restaurantsSlice';\r\nexport type { DriversState, Driver } from './slices/driversSlice';\r\nexport type { CustomersState, Customer, CustomerPreferences } from './slices/customersSlice';\r\nexport type { RealTimeState, OrderUpdate } from './slices/realTimeSlice';\r\nexport type { AnalyticsState, AnalyticsMetrics, AnalyticsReport } from './slices/analyticsSlice';\r\nexport type { NotificationsState, Notification as NotificationItem } from './slices/notificationsSlice';\r\nexport type { AdminState } from './slices/adminSlice';\r\nexport type { VendorState } from './slices/vendorSlice';\r\nexport type { DriverPanelState, DriverProfile } from './slices/driverPanelSlice';\r\n\r\n// Store setup listener for RTK Query\r\nimport { setupListeners } from '@reduxjs/toolkit/query';\r\nsetupListeners(store.dispatch);\r\n\r\n// Export for use in components\r\nexport default store;\r\n\r\n/**\r\n * Store Architecture Notes:\r\n * \r\n * 1. **Scalability**: Each slice handles specific domain logic\r\n * 2. **Performance**: Selective persistence and memoized selectors\r\n * 3. **Real-time**: Dedicated middleware for Firebase real-time updates\r\n * 4. **Multi-panel**: Separate slices for different user roles\r\n * 5. **Analytics**: Built-in analytics tracking middleware\r\n * 6. **Error handling**: Centralized error management\r\n * 7. **API management**: RTK Query for all server communication\r\n * 8. **Persistence**: Smart persistence strategy for offline support\r\n */\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;CASC;;;;;AAsFwB;AApFzB;AAAA;AACA;AAAA;AAAA;AACA;AAEA,cAAc;AACd;AACA;AACA;AAEA,wBAAwB;AACxB;AACA;AACA;AACA;AAEA,oBAAoB;AACpB;AACA;AACA;AAEA,wBAAwB;AACxB;AACA;AACA;AAEA,YAAY;AACZ;AAEA,0BAA0B;AAC1B;AAEA,aAAa;AACb;AACA;AACA;AACA;AAgGA,qCAAqC;AACrC;;;;;;;;;;;;;;;;;;;;;;;AA/FA,6BAA6B;AAC7B,MAAM,cAAc,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD,EAAE;IAClC,aAAa;IACb,MAAM,qJAAA,CAAA,UAAS,CAAC,OAAO;IACvB,IAAI,mJAAA,CAAA,UAAO,CAAC,OAAO;IACnB,MAAM,qJAAA,CAAA,UAAS,CAAC,OAAO;IAEvB,iBAAiB;IACjB,QAAQ,uJAAA,CAAA,UAAW,CAAC,OAAO;IAC3B,aAAa,4JAAA,CAAA,UAAgB,CAAC,OAAO;IACrC,SAAS,wJAAA,CAAA,UAAY,CAAC,OAAO;IAC7B,WAAW,0JAAA,CAAA,UAAc,CAAC,OAAO;IAEjC,oBAAoB;IACpB,UAAU,yJAAA,CAAA,UAAa,CAAC,OAAO;IAC/B,WAAW,0JAAA,CAAA,UAAc,CAAC,OAAO;IACjC,eAAe,8JAAA,CAAA,UAAkB,CAAC,OAAO;IAEzC,iBAAiB;IACjB,OAAO,sJAAA,CAAA,UAAU,CAAC,OAAO;IACzB,QAAQ,uJAAA,CAAA,UAAW,CAAC,OAAO;IAC3B,aAAa,4JAAA,CAAA,UAAgB,CAAC,OAAO;IAErC,MAAM;IACN,KAAK,0JAAA,CAAA,UAAU;IAEf,MAAM;IACN,KAAK,iJAAA,CAAA,WAAQ,CAAC,OAAO;AACvB;AAEA,4BAA4B;AAC5B,MAAM,gBAAgB;IACpB,KAAK;IACL,SAAS;IACT,SAAA,6KAAA,CAAA,UAAO;IACP,8CAA8C;IAC9C,WAAW;QAAC;QAAQ;QAAM;KAAO;IACjC,WAAW;QAAC;QAAO;QAAY;KAAY;AAC7C;AAEA,MAAM,mBAAmB,CAAA,GAAA,uNAAA,CAAA,iBAAc,AAAD,EAAE,eAAe;AAGhD,MAAM,QAAQ,CAAA,GAAA,6MAAA,CAAA,iBAAc,AAAD,EAAE;IAClC,SAAS;IACT,YAAY,CAAC;QACX,MAAM,aAAa,qBAAqB;YACtC,mBAAmB,uCAAyC;YAM5D,4CAA4C;YAC5C,gBAAgB,oDAAyB;QAC3C;QAEA,OAAO,WACJ,MAAM,CAAC,uKAAA,CAAA,0BAAuB,EAC9B,MAAM,CAAC,iJAAA,CAAA,WAAQ,CAAC,UAAU,EAC1B,MAAM,CAAC,mKAAA,CAAA,sBAAmB,EAC1B,MAAM,CAAC,kKAAA,CAAA,qBAAkB,EACzB,MAAM,CAAC,+JAAA,CAAA,kBAAe;IAC3B;IAEA,uCAAuC;IACvC,UAAU,oDAAyB;AACrC;AAGO,MAAM,YAAY,CAAA,GAAA,mNAAA,CAAA,eAAY,AAAD,EAAE;;AA0BtC,CAAA,GAAA,kMAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,QAAQ;uCAGd;;;;CAEf;;;;;;;;;;;CAWC", "debugId": null}}, {"offset": {"line": 4148, "column": 4}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/ReduxProvider.tsx"], "sourcesContent": ["/**\r\n * Redux Provider - Wraps the app with Redux store and persistence\r\n * Integrates seamlessly with your existing provider structure\r\n */\r\n\r\n'use client';\r\n\r\nimport React from 'react';\r\nimport { Provider } from 'react-redux';\r\nimport { PersistGate } from 'redux-persist/integration/react';\r\nimport { store, persistor } from './index';\r\n\r\ninterface ReduxProviderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\n/**\r\n * Redux Provider component that wraps the application\r\n * - Provides Redux store to all components\r\n * - <PERSON>les persistence with PersistGate\r\n * - Shows loading state while rehydrating\r\n */\r\nexport default function ReduxProvider({ children }: ReduxProviderProps) {\r\n  return (\r\n    <Provider store={store}>\r\n      <PersistGate\r\n        loading={\r\n          <div className=\"fixed inset-0 z-[9999] flex items-center justify-center bg-white\">\r\n            <div className=\"flex flex-col items-center space-y-4\">\r\n              <div className=\"w-12 h-12 border-4 border-orange-500 border-t-transparent rounded-full animate-spin\"></div>\r\n              <p className=\"text-gray-600 font-medium\">Loading Tap2Go...</p>\r\n            </div>\r\n          </div>\r\n        }\r\n        persistor={persistor}\r\n      >\r\n        {children as any}\r\n      </PersistGate>\r\n    </Provider>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AAKD;AACA;AACA;AALA;;;;;AAiBe,SAAS,cAAc,EAAE,QAAQ,EAAsB;IACpE,qBACE,4MAAC,2KAAA,CAAA,WAAQ;QAAC,OAAO,uIAAA,CAAA,QAAK;kBACpB,cAAA,4MAAC,gLAAA,CAAA,cAAW;YACV,uBACE,4MAAC;gBAAI,WAAU;0BACb,cAAA,4MAAC;oBAAI,WAAU;;sCACb,4MAAC;4BAAI,WAAU;;;;;;sCACf,4MAAC;4BAAE,WAAU;sCAA4B;;;;;;;;;;;;;;;;;YAI/C,WAAW,uIAAA,CAAA,YAAS;sBAEnB;;;;;;;;;;;AAIT;KAlBwB", "debugId": null}}, {"offset": {"line": 4229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/hooks/useChatbot.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\r\n\r\n// Types\r\nexport interface ChatMessage {\r\n  id: string;\r\n  role: 'user' | 'assistant';\r\n  content: string;\r\n  timestamp: Date | string;\r\n  quickReplies?: string[];\r\n  metadata?: {\r\n    intent?: string;\r\n    confidence?: number;\r\n    escalated?: boolean;\r\n  };\r\n}\r\n\r\ninterface UseChatbotState {\r\n  messages: ChatMessage[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  isTyping: boolean;\r\n}\r\n\r\n// Main chatbot hook\r\nexport function useChatbot() {\r\n  const [state, setState] = useState<UseChatbotState>({\r\n    messages: [],\r\n    loading: false,\r\n    error: null,\r\n    isTyping: false,\r\n  });\r\n\r\n  // Add welcome message on first load\r\n  const [hasWelcomed, setHasWelcomed] = useState(false);\r\n\r\n  if (!hasWelcomed && state.messages.length === 0) {\r\n    setState(prev => ({\r\n      ...prev,\r\n      messages: [{\r\n        id: `welcome_${Date.now()}`,\r\n        role: 'assistant',\r\n        content: \"Hi! I'm an AI assistant. I can help you with any questions or topics you'd like to discuss. How can I help you today?\",\r\n        timestamp: new Date().toISOString(),\r\n      }]\r\n    }));\r\n    setHasWelcomed(true);\r\n  }\r\n\r\n  // Send message - No sessions needed!\r\n  const sendMessage = useCallback(async (message: string) => {\r\n    if (!message.trim()) return;\r\n\r\n    // Add user message immediately\r\n    const userMessage: ChatMessage = {\r\n      id: `user_${Date.now()}`,\r\n      role: 'user',\r\n      content: message.trim(),\r\n      timestamp: new Date().toISOString(),\r\n    };\r\n\r\n    setState(prev => ({\r\n      ...prev,\r\n      messages: [...prev.messages, userMessage],\r\n      isTyping: true,\r\n      error: null,\r\n    }));\r\n\r\n    try {\r\n      // Send to simple chat API\r\n      const response = await fetch('/api/chatbot/chat', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          message: message.trim(),\r\n          conversationHistory: state.messages.slice(-6), // Last 6 messages for context\r\n        }),\r\n      });\r\n\r\n      const data = await response.json();\r\n\r\n      if (!response.ok) {\r\n        throw new Error(data.error || 'Failed to send message');\r\n      }\r\n\r\n      // Add AI response\r\n      setState(prev => ({\r\n        ...prev,\r\n        messages: [...prev.messages, data.message],\r\n        isTyping: false,\r\n      }));\r\n\r\n      return data.message;\r\n\r\n    } catch (error) {\r\n      console.error('Error sending message:', error);\r\n\r\n      setState(prev => ({\r\n        ...prev,\r\n        isTyping: false,\r\n        error: error instanceof Error ? error.message : 'Failed to send message',\r\n      }));\r\n\r\n      throw error;\r\n    }\r\n  }, [state.messages]);\r\n\r\n  // Clear chat\r\n  const clearChat = useCallback(() => {\r\n    setState({\r\n      messages: [],\r\n      loading: false,\r\n      error: null,\r\n      isTyping: false,\r\n    });\r\n    setHasWelcomed(false);\r\n  }, []);\r\n\r\n  return {\r\n    // State\r\n    messages: Array.isArray(state.messages) ? state.messages : [],\r\n    loading: state.loading,\r\n    error: state.error,\r\n    isTyping: state.isTyping,\r\n\r\n    // Actions\r\n    sendMessage,\r\n    clearChat,\r\n  };\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;AAAA;;;AAwBO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAmB;QAClD,UAAU,EAAE;QACZ,SAAS;QACT,OAAO;QACP,UAAU;IACZ;IAEA,oCAAoC;IACpC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,IAAI,CAAC,eAAe,MAAM,QAAQ,CAAC,MAAM,KAAK,GAAG;QAC/C,SAAS,CAAA,OAAQ,CAAC;gBAChB,GAAG,IAAI;gBACP,UAAU;oBAAC;wBACT,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAI;wBAC3B,MAAM;wBACN,SAAS;wBACT,WAAW,IAAI,OAAO,WAAW;oBACnC;iBAAE;YACJ,CAAC;QACD,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,cAAc,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD;+CAAE,OAAO;YACrC,IAAI,CAAC,QAAQ,IAAI,IAAI;YAErB,+BAA+B;YAC/B,MAAM,cAA2B;gBAC/B,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;gBACxB,MAAM;gBACN,SAAS,QAAQ,IAAI;gBACrB,WAAW,IAAI,OAAO,WAAW;YACnC;YAEA;uDAAS,CAAA,OAAQ,CAAC;wBAChB,GAAG,IAAI;wBACP,UAAU;+BAAI,KAAK,QAAQ;4BAAE;yBAAY;wBACzC,UAAU;wBACV,OAAO;oBACT,CAAC;;YAED,IAAI;gBACF,0BAA0B;gBAC1B,MAAM,WAAW,MAAM,MAAM,qBAAqB;oBAChD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,SAAS,QAAQ,IAAI;wBACrB,qBAAqB,MAAM,QAAQ,CAAC,KAAK,CAAC,CAAC;oBAC7C;gBACF;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;gBAChC;gBAEA,kBAAkB;gBAClB;2DAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,UAAU;mCAAI,KAAK,QAAQ;gCAAE,KAAK,OAAO;6BAAC;4BAC1C,UAAU;wBACZ,CAAC;;gBAED,OAAO,KAAK,OAAO;YAErB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBAExC;2DAAS,CAAA,OAAQ,CAAC;4BAChB,GAAG,IAAI;4BACP,UAAU;4BACV,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,CAAC;;gBAED,MAAM;YACR;QACF;8CAAG;QAAC,MAAM,QAAQ;KAAC;IAEnB,aAAa;IACb,MAAM,YAAY,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD;6CAAE;YAC5B,SAAS;gBACP,UAAU,EAAE;gBACZ,SAAS;gBACT,OAAO;gBACP,UAAU;YACZ;YACA,eAAe;QACjB;4CAAG,EAAE;IAEL,OAAO;QACL,QAAQ;QACR,UAAU,MAAM,OAAO,CAAC,MAAM,QAAQ,IAAI,MAAM,QAAQ,GAAG,EAAE;QAC7D,SAAS,MAAM,OAAO;QACtB,OAAO,MAAM,KAAK;QAClB,UAAU,MAAM,QAAQ;QAExB,UAAU;QACV;QACA;IACF;AACF;GA1GgB", "debugId": null}}, {"offset": {"line": 4357, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/chatbot/ChatMessage.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport { ChatMessage as ChatMessageType } from '@/hooks/useChatbot';\r\n\r\ninterface ChatMessageProps {\r\n  message: ChatMessageType;\r\n}\r\n\r\nexport default function ChatMessage({ message }: ChatMessageProps) {\r\n  const isUser = message.role === 'user';\r\n  const isEscalated = message.metadata?.escalated;\r\n\r\n  const formatTime = (timestamp: Date | string) => {\r\n    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;\r\n    return date.toLocaleTimeString('en-US', {\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n      hour12: true,\r\n    });\r\n  };\r\n\r\n  return (\r\n    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-2`}>\r\n      <div className={`flex items-end space-x-2 max-w-[85%] ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`}>\r\n        {/* Avatar - Only show for assistant messages */}\r\n        {!isUser && (\r\n          <div className=\"w-7 h-7 rounded-full bg-gradient-to-r from-orange-400 to-orange-600 flex items-center justify-center flex-shrink-0 mb-1\">\r\n            <span className=\"text-white text-xs font-bold\">T2G</span>\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"flex flex-col\">\r\n          {/* Message Bubble - Facebook Messenger Style */}\r\n          <div\r\n            className={`message-bubble px-3 py-2 max-w-xs lg:max-w-md ${\r\n              isUser\r\n                ? 'bg-orange-500 text-white rounded-2xl rounded-br-md'\r\n                : isEscalated\r\n                ? 'bg-red-50 border border-red-200 text-red-800 rounded-2xl rounded-bl-md'\r\n                : 'bg-gray-100 text-gray-900 rounded-2xl rounded-bl-md'\r\n            }`}\r\n          >\r\n            <p className=\"text-sm leading-relaxed whitespace-pre-wrap break-words\">\r\n              {message.content}\r\n            </p>\r\n\r\n            {/* Escalation Notice */}\r\n            {isEscalated && (\r\n              <div className=\"mt-2 p-2 bg-red-100 rounded-lg border border-red-200\">\r\n                <p className=\"text-xs text-red-600 font-medium\">\r\n                  🚨 This conversation has been escalated to our human support team\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Timestamp - Messenger style */}\r\n          <div className={`text-xs mt-1 px-1 ${isUser ? 'text-right text-gray-500' : 'text-left text-gray-500'}`}>\r\n            {formatTime(message.timestamp)}\r\n            {message.metadata?.confidence && process.env.NODE_ENV === 'development' && (\r\n              <span className=\"ml-2\">\r\n                • {Math.round(message.metadata.confidence * 100)}%\r\n              </span>\r\n            )}\r\n          </div>\r\n\r\n          {/* Intent Badge (for debugging - can be removed in production) */}\r\n          {!isUser && message.metadata?.intent && process.env.NODE_ENV === 'development' && (\r\n            <div className=\"mt-1\">\r\n              <span className=\"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded-full\">\r\n                Intent: {message.metadata.intent}\r\n              </span>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AA4D6C;;AA5D7C;;AASe,SAAS,YAAY,EAAE,OAAO,EAAoB;IAC/D,MAAM,SAAS,QAAQ,IAAI,KAAK;IAChC,MAAM,cAAc,QAAQ,QAAQ,EAAE;IAEtC,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,OAAO,cAAc,WAAW,IAAI,KAAK,aAAa;QACnE,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,qBACE,4MAAC;QAAI,WAAW,CAAC,KAAK,EAAE,SAAS,gBAAgB,gBAAgB,KAAK,CAAC;kBACrE,cAAA,4MAAC;YAAI,WAAW,CAAC,qCAAqC,EAAE,SAAS,qCAAqC,IAAI;;gBAEvG,CAAC,wBACA,4MAAC;oBAAI,WAAU;8BACb,cAAA,4MAAC;wBAAK,WAAU;kCAA+B;;;;;;;;;;;8BAInD,4MAAC;oBAAI,WAAU;;sCAEb,4MAAC;4BACC,WAAW,CAAC,8CAA8C,EACxD,SACI,uDACA,cACA,2EACA,uDACJ;;8CAEF,4MAAC;oCAAE,WAAU;8CACV,QAAQ,OAAO;;;;;;gCAIjB,6BACC,4MAAC;oCAAI,WAAU;8CACb,cAAA,4MAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;;;;;;sCAQtD,4MAAC;4BAAI,WAAW,CAAC,kBAAkB,EAAE,SAAS,6BAA6B,2BAA2B;;gCACnG,WAAW,QAAQ,SAAS;gCAC5B,QAAQ,QAAQ,EAAE,cAAc,oDAAyB,+BACxD,4MAAC;oCAAK,WAAU;;wCAAO;wCAClB,KAAK,KAAK,CAAC,QAAQ,QAAQ,CAAC,UAAU,GAAG;wCAAK;;;;;;;;;;;;;wBAMtD,CAAC,UAAU,QAAQ,QAAQ,EAAE,UAAU,oDAAyB,+BAC/D,4MAAC;4BAAI,WAAU;sCACb,cAAA,4MAAC;gCAAK,WAAU;;oCAAwE;oCAC7E,QAAQ,QAAQ,CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;KAtEwB", "debugId": null}}, {"offset": {"line": 4500, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/chatbot/ChatInput.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useRef, useEffect } from 'react';\r\nimport { PaperAirplaneIcon, MicrophoneIcon, FaceSmileIcon, PhotoIcon } from '@heroicons/react/24/outline';\r\n\r\ninterface ChatInputProps {\r\n  onSendMessage: (message: string) => void;\r\n  disabled?: boolean;\r\n  placeholder?: string;\r\n}\r\n\r\nexport default function ChatInput({\r\n  onSendMessage,\r\n  disabled = false,\r\n  placeholder = \"Type your message...\"\r\n}: ChatInputProps) {\r\n  const [message, setMessage] = useState('');\r\n  const [isRecording, setIsRecording] = useState(false);\r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n\r\n  // Auto-resize textarea\r\n  useEffect(() => {\r\n    if (textareaRef.current) {\r\n      textareaRef.current.style.height = 'auto';\r\n      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;\r\n    }\r\n  }, [message]);\r\n\r\n  // Focus on textarea when component mounts\r\n  useEffect(() => {\r\n    if (textareaRef.current && !disabled) {\r\n      textareaRef.current.focus();\r\n    }\r\n  }, [disabled]);\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (message.trim() && !disabled) {\r\n      onSendMessage(message.trim());\r\n      setMessage('');\r\n      \r\n      // Reset textarea height\r\n      if (textareaRef.current) {\r\n        textareaRef.current.style.height = 'auto';\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleKeyPress = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleSubmit(e);\r\n    }\r\n  };\r\n\r\n  const handleVoiceInput = () => {\r\n    // Voice input functionality can be implemented here\r\n    // For now, just toggle recording state\r\n    setIsRecording(!isRecording);\r\n    \r\n    // Placeholder for voice recognition\r\n    if (!isRecording) {\r\n      // Start recording\r\n      console.log('Voice recording started');\r\n    } else {\r\n      // Stop recording\r\n      console.log('Voice recording stopped');\r\n    }\r\n  };\r\n\r\n  const canSend = message.trim().length > 0 && !disabled;\r\n\r\n  return (\r\n    <div className=\"bg-white border-t border-gray-200 max-md:flex-shrink-0\">\r\n      {/* Input Area - Facebook Messenger Style */}\r\n      <div className=\"px-4 py-3 max-md:px-3 max-md:py-2\">\r\n        <form onSubmit={handleSubmit} className=\"flex items-end space-x-2\">\r\n          {/* Additional Action Buttons */}\r\n          <div className=\"flex items-center space-x-1\">\r\n            <button\r\n              type=\"button\"\r\n              disabled={disabled}\r\n              className={`p-2 rounded-full transition-colors duration-200 ${\r\n                disabled ? 'opacity-50 cursor-not-allowed' : 'text-orange-500 hover:bg-orange-50'\r\n              }`}\r\n              title=\"Add photo\"\r\n            >\r\n              <PhotoIcon className=\"w-5 h-5\" />\r\n            </button>\r\n          </div>\r\n\r\n          {/* Message Input Container - Messenger Style */}\r\n          <div className=\"flex-1 relative\">\r\n            <div className=\"chat-input-container flex items-end bg-gray-100 rounded-2xl px-3 py-2 min-h-[40px] transition-all duration-200 focus-within:bg-gray-50\">\r\n              <textarea\r\n                ref={textareaRef}\r\n                value={message}\r\n                onChange={(e) => setMessage(e.target.value)}\r\n                onKeyPress={handleKeyPress}\r\n                placeholder={disabled ? 'Chat is disabled' : placeholder}\r\n                disabled={disabled}\r\n                rows={1}\r\n                className=\"flex-1 bg-transparent resize-none outline-none text-gray-900 placeholder:text-gray-500 text-sm leading-5\"\r\n                style={{\r\n                  minHeight: '20px',\r\n                  maxHeight: '100px',\r\n                }}\r\n              />\r\n\r\n              {/* Emoji Button */}\r\n              <button\r\n                type=\"button\"\r\n                disabled={disabled}\r\n                className={`ml-2 p-1 rounded-full transition-colors duration-200 ${\r\n                  disabled ? 'opacity-50 cursor-not-allowed' : 'text-gray-500 hover:text-orange-500'\r\n                }`}\r\n                title=\"Add emoji\"\r\n              >\r\n                <FaceSmileIcon className=\"w-5 h-5\" />\r\n              </button>\r\n            </div>\r\n\r\n            {/* Character Count */}\r\n            {message.length > 800 && (\r\n              <div className=\"absolute -top-6 right-2 text-xs text-gray-500 bg-white px-1 rounded\">\r\n                {message.length}/1000\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Send Button or Voice Button */}\r\n          {message.trim() ? (\r\n            <button\r\n              type=\"submit\"\r\n              disabled={!canSend}\r\n              className={`flex-shrink-0 p-2 rounded-full transition-all duration-200 ${\r\n                canSend\r\n                  ? 'bg-orange-500 text-white hover:bg-orange-600 transform hover:scale-105'\r\n                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'\r\n              }`}\r\n              title=\"Send message\"\r\n            >\r\n              <PaperAirplaneIcon className=\"w-5 h-5\" />\r\n            </button>\r\n          ) : (\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleVoiceInput}\r\n              disabled={disabled}\r\n              className={`flex-shrink-0 p-2 rounded-full transition-colors duration-200 ${\r\n                isRecording\r\n                  ? 'bg-red-500 text-white animate-pulse'\r\n                  : disabled\r\n                  ? 'bg-gray-200 text-gray-400 cursor-not-allowed'\r\n                  : 'bg-orange-500 text-white hover:bg-orange-600'\r\n              }`}\r\n              title={isRecording ? 'Stop recording' : 'Voice message'}\r\n            >\r\n              <MicrophoneIcon className=\"w-5 h-5\" />\r\n            </button>\r\n          )}\r\n        </form>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAWe,SAAS,UAAU,EAChC,aAAa,EACb,WAAW,KAAK,EAChB,cAAc,sBAAsB,EACrB;;IACf,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,cAAc,CAAA,GAAA,4KAAA,CAAA,SAAM,AAAD,EAAuB;IAEhD,uBAAuB;IACvB,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,YAAY,OAAO,EAAE;gBACvB,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;gBACnC,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,YAAY,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YAC5E;QACF;8BAAG;QAAC;KAAQ;IAEZ,0CAA0C;IAC1C,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,YAAY,OAAO,IAAI,CAAC,UAAU;gBACpC,YAAY,OAAO,CAAC,KAAK;YAC3B;QACF;8BAAG;QAAC;KAAS;IAEb,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,IAAI,QAAQ,IAAI,MAAM,CAAC,UAAU;YAC/B,cAAc,QAAQ,IAAI;YAC1B,WAAW;YAEX,wBAAwB;YACxB,IAAI,YAAY,OAAO,EAAE;gBACvB,YAAY,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG;YACrC;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB,aAAa;QACf;IACF;IAEA,MAAM,mBAAmB;QACvB,oDAAoD;QACpD,uCAAuC;QACvC,eAAe,CAAC;QAEhB,oCAAoC;QACpC,IAAI,CAAC,aAAa;YAChB,kBAAkB;YAClB,QAAQ,GAAG,CAAC;QACd,OAAO;YACL,iBAAiB;YACjB,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,MAAM,UAAU,QAAQ,IAAI,GAAG,MAAM,GAAG,KAAK,CAAC;IAE9C,qBACE,4MAAC;QAAI,WAAU;kBAEb,cAAA,4MAAC;YAAI,WAAU;sBACb,cAAA,4MAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,4MAAC;wBAAI,WAAU;kCACb,cAAA,4MAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAW,CAAC,gDAAgD,EAC1D,WAAW,kCAAkC,sCAC7C;4BACF,OAAM;sCAEN,cAAA,4MAAC,mOAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAKzB,4MAAC;wBAAI,WAAU;;0CACb,4MAAC;gCAAI,WAAU;;kDACb,4MAAC;wCACC,KAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;wCAC1C,YAAY;wCACZ,aAAa,WAAW,qBAAqB;wCAC7C,UAAU;wCACV,MAAM;wCACN,WAAU;wCACV,OAAO;4CACL,WAAW;4CACX,WAAW;wCACb;;;;;;kDAIF,4MAAC;wCACC,MAAK;wCACL,UAAU;wCACV,WAAW,CAAC,qDAAqD,EAC/D,WAAW,kCAAkC,uCAC7C;wCACF,OAAM;kDAEN,cAAA,4MAAC,2OAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAK5B,QAAQ,MAAM,GAAG,qBAChB,4MAAC;gCAAI,WAAU;;oCACZ,QAAQ,MAAM;oCAAC;;;;;;;;;;;;;oBAMrB,QAAQ,IAAI,mBACX,4MAAC;wBACC,MAAK;wBACL,UAAU,CAAC;wBACX,WAAW,CAAC,2DAA2D,EACrE,UACI,2EACA,gDACJ;wBACF,OAAM;kCAEN,cAAA,4MAAC,mPAAA,CAAA,oBAAiB;4BAAC,WAAU;;;;;;;;;;6CAG/B,4MAAC;wBACC,MAAK;wBACL,SAAS;wBACT,UAAU;wBACV,WAAW,CAAC,8DAA8D,EACxE,cACI,wCACA,WACA,iDACA,gDACJ;wBACF,OAAO,cAAc,mBAAmB;kCAExC,cAAA,4MAAC,6OAAA,CAAA,iBAAc;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;GA3JwB;KAAA", "debugId": null}}, {"offset": {"line": 4731, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/chatbot/ChatWindow.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useRef } from 'react';\r\nimport { XMarkIcon, MinusIcon } from '@heroicons/react/24/outline';\r\nimport { useChatbot } from '@/hooks/useChatbot';\r\nimport ChatMessage from './ChatMessage';\r\nimport ChatInput from './ChatInput';\r\n\r\ninterface ChatWindowProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onMinimize: () => void;\r\n  isMinimized: boolean;\r\n  position?: 'bottom-right' | 'bottom-left';\r\n  customerInfo?: {\r\n    name?: string;\r\n    email?: string;\r\n    orderId?: string;\r\n    location?: string;\r\n  };\r\n}\r\n\r\nexport default function ChatWindow({\r\n  isOpen,\r\n  onClose,\r\n  onMinimize,\r\n  isMinimized,\r\n  position = 'bottom-right'\r\n}: ChatWindowProps) {\r\n  const {\r\n    messages,\r\n    loading,\r\n    error,\r\n    isTyping,\r\n    sendMessage,\r\n    clearChat,\r\n  } = useChatbot();\r\n\r\n  const messagesEndRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Auto-scroll to bottom when new messages arrive\r\n  useEffect(() => {\r\n    if (messagesEndRef.current) {\r\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\r\n    }\r\n  }, [messages, isTyping]);\r\n\r\n  // Handle sending messages - Simple!\r\n  const handleSendMessage = async (message: string) => {\r\n    try {\r\n      await sendMessage(message);\r\n    } catch (error) {\r\n      console.error('Failed to send message:', error);\r\n    }\r\n  };\r\n\r\n  // Handle restart chat\r\n  const handleRestartChat = () => {\r\n    clearChat();\r\n  };\r\n\r\n  if (!isOpen) return null;\r\n\r\n  // Position classes for chat window\r\n  const windowPositionClasses = {\r\n    'bottom-right': 'bottom-4 right-4',\r\n    'bottom-left': 'bottom-4 left-4',\r\n  };\r\n\r\n  return (\r\n    <div className={`fixed ${windowPositionClasses[position]} z-50 max-md:inset-0`}>\r\n      <div\r\n        className={`chat-widget bg-white rounded-lg shadow-2xl border border-gray-200 transition-all duration-300 max-md:rounded-none max-md:h-screen max-md:w-full max-md:flex max-md:flex-col ${\r\n          isMinimized ? 'w-80 h-16 max-md:w-full max-md:h-16' : 'w-96 h-[600px] max-md:w-full max-md:h-screen'\r\n        }`}\r\n      >\r\n        {/* Header - Facebook Messenger Style */}\r\n        <div className=\"flex items-center justify-between px-4 py-3 bg-white border-b border-gray-200 rounded-t-lg\">\r\n          <div className=\"flex items-center space-x-3\">\r\n            {/* Avatar with online indicator */}\r\n            <div className=\"relative\">\r\n              <div className=\"w-10 h-10 rounded-full bg-gradient-to-r from-orange-400 to-orange-600 flex items-center justify-center\">\r\n                <span className=\"text-white text-sm font-bold\">T2G</span>\r\n              </div>\r\n              <div className=\"absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full\"></div>\r\n            </div>\r\n            <div>\r\n              <h3 className=\"font-semibold text-gray-900 text-sm\">Tap2Go Assistant</h3>\r\n              <p className=\"text-xs text-green-600 font-medium\">Active now</p>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"flex items-center space-x-1\">\r\n            {/* Action buttons - Messenger style */}\r\n            <button\r\n              onClick={onMinimize}\r\n              className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\r\n              title={isMinimized ? \"Expand chat\" : \"Minimize chat\"}\r\n            >\r\n              <MinusIcon className=\"w-5 h-5 text-gray-600\" />\r\n            </button>\r\n            <button\r\n              onClick={onClose}\r\n              className=\"p-2 hover:bg-gray-100 rounded-full transition-colors\"\r\n              title=\"Close chat\"\r\n            >\r\n              <XMarkIcon className=\"w-5 h-5 text-gray-600\" />\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Chat Content */}\r\n        {!isMinimized && (\r\n          <>\r\n\r\n\r\n            {/* Messages Area - Messenger Style */}\r\n            <div className=\"flex-1 overflow-y-auto p-4 h-[440px] max-md:flex-1 max-md:h-auto bg-white\">\r\n              {loading ? (\r\n                <div className=\"flex items-center justify-center h-full\">\r\n                  <div className=\"text-center\">\r\n                    <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-2\"></div>\r\n                    <p className=\"text-gray-600 text-sm\">Starting chat...</p>\r\n                  </div>\r\n                </div>\r\n              ) : error ? (\r\n                <div className=\"flex items-center justify-center h-full\">\r\n                  <div className=\"text-center\">\r\n                    <div className=\"text-red-500 mb-2\">⚠️</div>\r\n                    <p className=\"text-red-600 text-sm mb-3\">{error}</p>\r\n                    <button\r\n                      onClick={handleRestartChat}\r\n                      className=\"px-4 py-2 bg-orange-500 text-white rounded-lg text-sm hover:bg-orange-600 transition-colors duration-200\"\r\n                    >\r\n                      Try Again\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <>\r\n                  {/* Welcome Message - Messenger Style */}\r\n                  {Array.isArray(messages) && messages.length === 1 && (\r\n                    <div className=\"mb-4 flex justify-center\">\r\n                      <div className=\"bg-gray-100 px-3 py-1 rounded-full\">\r\n                        <p className=\"text-gray-600 text-xs text-center\">\r\n                          👋 Welcome to Tap2Go! I&apos;m here to help you with orders, restaurant info, delivery questions, and more.\r\n                        </p>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Messages */}\r\n                  {Array.isArray(messages) && messages.map((message) => (\r\n                    <ChatMessage\r\n                      key={message.id}\r\n                      message={message}\r\n                    />\r\n                  ))}\r\n\r\n                  {/* Typing Indicator - Messenger Style (appears in message area) */}\r\n                  {isTyping && (\r\n                    <div className=\"flex justify-start mb-2\">\r\n                      <div className=\"flex items-end space-x-2 max-w-[85%]\">\r\n                        {/* Avatar */}\r\n                        <div className=\"w-7 h-7 rounded-full bg-gradient-to-r from-orange-400 to-orange-600 flex items-center justify-center flex-shrink-0 mb-1\">\r\n                          <span className=\"text-white text-xs font-bold\">T2G</span>\r\n                        </div>\r\n\r\n                        {/* Typing bubble */}\r\n                        <div className=\"bg-gray-100 rounded-2xl rounded-bl-md px-3 py-2\">\r\n                          <div className=\"flex space-x-1\">\r\n                            <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\r\n                            <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\r\n                            <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Scroll anchor */}\r\n                  <div ref={messagesEndRef} />\r\n                </>\r\n              )}\r\n            </div>\r\n\r\n            {/* Input Area */}\r\n            <ChatInput\r\n              onSendMessage={handleSendMessage}\r\n              disabled={loading}\r\n              placeholder=\"Ask me anything about Tap2Go...\"\r\n            />\r\n          </>\r\n        )}\r\n\r\n        {/* Minimized State */}\r\n        {isMinimized && (\r\n          <div className=\"p-4 flex items-center justify-between\">\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\r\n              <span className=\"text-sm text-gray-600\">Chat minimized</span>\r\n            </div>\r\n            {Array.isArray(messages) && messages.length > 1 && (\r\n              <div className=\"text-xs text-gray-500\">\r\n                {messages.length - 1} message{messages.length > 2 ? 's' : ''}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAsBe,SAAS,WAAW,EACjC,MAAM,EACN,OAAO,EACP,UAAU,EACV,WAAW,EACX,WAAW,cAAc,EACT;;IAChB,MAAM,EACJ,QAAQ,EACR,OAAO,EACP,KAAK,EACL,QAAQ,EACR,WAAW,EACX,SAAS,EACV,GAAG,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD;IAEb,MAAM,iBAAiB,CAAA,GAAA,4KAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,iDAAiD;IACjD,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,eAAe,OAAO,EAAE;gBAC1B,eAAe,OAAO,CAAC,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAC7D;QACF;+BAAG;QAAC;QAAU;KAAS;IAEvB,oCAAoC;IACpC,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,YAAY;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,sBAAsB;IACtB,MAAM,oBAAoB;QACxB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,mCAAmC;IACnC,MAAM,wBAAwB;QAC5B,gBAAgB;QAChB,eAAe;IACjB;IAEA,qBACE,4MAAC;QAAI,WAAW,CAAC,MAAM,EAAE,qBAAqB,CAAC,SAAS,CAAC,oBAAoB,CAAC;kBAC5E,cAAA,4MAAC;YACC,WAAW,CAAC,4KAA4K,EACtL,cAAc,wCAAwC,gDACtD;;8BAGF,4MAAC;oBAAI,WAAU;;sCACb,4MAAC;4BAAI,WAAU;;8CAEb,4MAAC;oCAAI,WAAU;;sDACb,4MAAC;4CAAI,WAAU;sDACb,cAAA,4MAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,4MAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,4MAAC;;sDACC,4MAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,4MAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;;;;;;;sCAItD,4MAAC;4BAAI,WAAU;;8CAEb,4MAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAO,cAAc,gBAAgB;8CAErC,cAAA,4MAAC,mOAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,4MAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CAEN,cAAA,4MAAC,mOAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAM1B,CAAC,6BACA;;sCAIE,4MAAC;4BAAI,WAAU;sCACZ,wBACC,4MAAC;gCAAI,WAAU;0CACb,cAAA,4MAAC;oCAAI,WAAU;;sDACb,4MAAC;4CAAI,WAAU;;;;;;sDACf,4MAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;uCAGvC,sBACF,4MAAC;gCAAI,WAAU;0CACb,cAAA,4MAAC;oCAAI,WAAU;;sDACb,4MAAC;4CAAI,WAAU;sDAAoB;;;;;;sDACnC,4MAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,4MAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;;;;;qDAML;;oCAEG,MAAM,OAAO,CAAC,aAAa,SAAS,MAAM,KAAK,mBAC9C,4MAAC;wCAAI,WAAU;kDACb,cAAA,4MAAC;4CAAI,WAAU;sDACb,cAAA,4MAAC;gDAAE,WAAU;0DAAoC;;;;;;;;;;;;;;;;oCAQtD,MAAM,OAAO,CAAC,aAAa,SAAS,GAAG,CAAC,CAAC,wBACxC,4MAAC,8JAAA,CAAA,UAAW;4CAEV,SAAS;2CADJ,QAAQ,EAAE;;;;;oCAMlB,0BACC,4MAAC;wCAAI,WAAU;kDACb,cAAA,4MAAC;4CAAI,WAAU;;8DAEb,4MAAC;oDAAI,WAAU;8DACb,cAAA,4MAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAIjD,4MAAC;oDAAI,WAAU;8DACb,cAAA,4MAAC;wDAAI,WAAU;;0EACb,4MAAC;gEAAI,WAAU;;;;;;0EACf,4MAAC;gEAAI,WAAU;gEAAkD,OAAO;oEAAE,gBAAgB;gEAAO;;;;;;0EACjG,4MAAC;gEAAI,WAAU;gEAAkD,OAAO;oEAAE,gBAAgB;gEAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ3G,4MAAC;wCAAI,KAAK;;;;;;;;;;;;;sCAMhB,4MAAC,4JAAA,CAAA,UAAS;4BACR,eAAe;4BACf,UAAU;4BACV,aAAY;;;;;;;;gBAMjB,6BACC,4MAAC;oBAAI,WAAU;;sCACb,4MAAC;4BAAI,WAAU;;8CACb,4MAAC;oCAAI,WAAU;;;;;;8CACf,4MAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;wBAEzC,MAAM,OAAO,CAAC,aAAa,SAAS,MAAM,GAAG,mBAC5C,4MAAC;4BAAI,WAAU;;gCACZ,SAAS,MAAM,GAAG;gCAAE;gCAAS,SAAS,MAAM,GAAG,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AAQ1E;GA9LwB;;QAclB,4IAAA,CAAA,aAAU;;;KAdQ", "debugId": null}}, {"offset": {"line": 5181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/chatbot/ChatWidget.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { ChatBubbleLeftRightIcon } from '@heroicons/react/24/outline';\r\nimport ChatWindow from './ChatWindow';\r\n\r\ninterface ChatWidgetProps {\r\n  customerInfo?: {\r\n    name?: string;\r\n    email?: string;\r\n    orderId?: string;\r\n    location?: string;\r\n  };\r\n  position?: 'bottom-right' | 'bottom-left';\r\n  showWelcomeMessage?: boolean;\r\n  autoOpen?: boolean;\r\n  theme?: 'orange' | 'blue' | 'green';\r\n}\r\n\r\nexport default function ChatWidget({\r\n  customerInfo,\r\n  position = 'bottom-right',\r\n  autoOpen = false,\r\n  theme = 'orange'\r\n}: ChatWidgetProps) {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [isMinimized, setIsMinimized] = useState(false);\r\n  const [hasInteracted, setHasInteracted] = useState(false);\r\n\r\n  // Theme colors\r\n  const themeColors = {\r\n    orange: {\r\n      primary: 'from-orange-500 to-orange-600',\r\n      hover: 'hover:from-orange-600 hover:to-orange-700',\r\n      text: 'text-orange-600',\r\n      bg: 'bg-orange-50',\r\n      border: 'border-orange-200',\r\n    },\r\n    blue: {\r\n      primary: 'from-blue-500 to-blue-600',\r\n      hover: 'hover:from-blue-600 hover:to-blue-700',\r\n      text: 'text-blue-600',\r\n      bg: 'bg-blue-50',\r\n      border: 'border-blue-200',\r\n    },\r\n    green: {\r\n      primary: 'from-green-500 to-green-600',\r\n      hover: 'hover:from-green-600 hover:to-green-700',\r\n      text: 'text-green-600',\r\n      bg: 'bg-green-50',\r\n      border: 'border-green-200',\r\n    },\r\n  };\r\n\r\n  const colors = themeColors[theme];\r\n\r\n  // Position classes - Added 20px more space (bottom-12 -> bottom-20, bottom-16 -> bottom-20)\r\n  const positionClasses = {\r\n    'bottom-right': 'bottom-20 right-4 max-md:bottom-20',\r\n    'bottom-left': 'bottom-20 left-4 max-md:bottom-20',\r\n  };\r\n\r\n\r\n\r\n  // Auto-open after delay\r\n  useEffect(() => {\r\n    if (autoOpen && !hasInteracted) {\r\n      const timer = setTimeout(() => {\r\n        setIsOpen(true);\r\n      }, 3000); // Open after 3 seconds\r\n\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [autoOpen, hasInteracted]);\r\n\r\n  // Disable welcome message - focus on pure chat\r\n  // useEffect(() => {\r\n  //   if (showWelcomeMessage && !hasInteracted && !isOpen) {\r\n  //     const timer = setTimeout(() => {\r\n  //       setShowWelcome(true);\r\n  //     }, 2000);\r\n  //     return () => clearTimeout(timer);\r\n  //   }\r\n  // }, [showWelcomeMessage, hasInteracted, isOpen]);\r\n\r\n  // useEffect(() => {\r\n  //   if (showWelcome) {\r\n  //     const timer = setTimeout(() => {\r\n  //       setShowWelcome(false);\r\n  //     }, 8000);\r\n  //     return () => clearTimeout(timer);\r\n  //   }\r\n  // }, [showWelcome]);\r\n\r\n  const handleToggleChat = () => {\r\n    setHasInteracted(true);\r\n    \r\n    if (isOpen) {\r\n      if (isMinimized) {\r\n        setIsMinimized(false);\r\n      } else {\r\n        setIsOpen(false);\r\n        setIsMinimized(false);\r\n      }\r\n    } else {\r\n      setIsOpen(true);\r\n      setIsMinimized(false);\r\n    }\r\n  };\r\n\r\n  const handleCloseChat = () => {\r\n    setIsOpen(false);\r\n    setIsMinimized(false);\r\n  };\r\n\r\n  const handleMinimizeChat = () => {\r\n    setIsMinimized(!isMinimized);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Chat Window */}\r\n      <ChatWindow\r\n        isOpen={isOpen}\r\n        onClose={handleCloseChat}\r\n        onMinimize={handleMinimizeChat}\r\n        isMinimized={isMinimized}\r\n        position={position}\r\n        customerInfo={customerInfo}\r\n      />\r\n\r\n      {/* Floating Chat Button */}\r\n      {!isOpen && (\r\n        <div className={`fixed ${positionClasses[position]} z-[9999]`}>\r\n\r\n\r\n          {/* Chat Button */}\r\n          <button\r\n            onClick={handleToggleChat}\r\n            className={`w-14 h-14 bg-gradient-to-r ${colors.primary} ${colors.hover} text-white rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-300 flex items-center justify-center group relative`}\r\n            title=\"Open Tap2Go Assistant\"\r\n            style={{ zIndex: 9999 }}\r\n          >\r\n            <ChatBubbleLeftRightIcon className=\"w-6 h-6 group-hover:scale-110 transition-transform duration-200\" />\r\n\r\n            {/* Notification Dot */}\r\n            <div className=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center border-2 border-white\">\r\n              <div className=\"w-2 h-2 bg-white rounded-full animate-pulse\"></div>\r\n            </div>\r\n          </button>\r\n\r\n          {/* Pulsing Ring Animation */}\r\n          <div className=\"absolute inset-0 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 opacity-30 animate-ping\"></div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Keyboard Shortcut Listener */}\r\n      {typeof window !== 'undefined' && (\r\n        <div\r\n          onKeyDown={(e) => {\r\n            if (e.ctrlKey && e.key === '/') {\r\n              e.preventDefault();\r\n              handleToggleChat();\r\n            }\r\n          }}\r\n          tabIndex={-1}\r\n          className=\"sr-only\"\r\n        />\r\n      )}\r\n\r\n      {/* Global Styles for Chat Widget */}\r\n      <style dangerouslySetInnerHTML={{__html: `\r\n        @keyframes chatBounce {\r\n          0%, 20%, 53%, 80%, 100% {\r\n            transform: translate3d(0,0,0);\r\n          }\r\n          40%, 43% {\r\n            transform: translate3d(0, -8px, 0);\r\n          }\r\n          70% {\r\n            transform: translate3d(0, -4px, 0);\r\n          }\r\n          90% {\r\n            transform: translate3d(0, -2px, 0);\r\n          }\r\n        }\r\n\r\n        .chat-bounce {\r\n          animation: chatBounce 2s ease-in-out infinite;\r\n        }\r\n\r\n        /* Custom scrollbar for chat messages */\r\n        .chat-messages::-webkit-scrollbar {\r\n          width: 4px;\r\n        }\r\n\r\n        .chat-messages::-webkit-scrollbar-track {\r\n          background: #f1f1f1;\r\n          border-radius: 2px;\r\n        }\r\n\r\n        .chat-messages::-webkit-scrollbar-thumb {\r\n          background: #c1c1c1;\r\n          border-radius: 2px;\r\n        }\r\n\r\n        .chat-messages::-webkit-scrollbar-thumb:hover {\r\n          background: #a1a1a1;\r\n        }\r\n\r\n        /* Smooth transitions for chat elements */\r\n        .chat-transition {\r\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\r\n        }\r\n\r\n        /* Focus styles for accessibility */\r\n        .chat-widget button:focus {\r\n          outline: 2px solid #f97316;\r\n          outline-offset: 2px;\r\n        }\r\n\r\n        /* Mobile responsiveness - Full screen like Messenger */\r\n        @media (max-width: 767px) {\r\n          .chat-widget {\r\n            width: 100vw !important;\r\n            height: 100vh !important; /* Fallback for older browsers */\r\n            height: 100dvh !important; /* Use dynamic viewport height for mobile */\r\n            max-width: none !important;\r\n            max-height: 100vh !important; /* Fallback */\r\n            max-height: 100dvh !important; /* Dynamic viewport height */\r\n            border-radius: 0 !important;\r\n            position: fixed !important;\r\n            top: 0 !important;\r\n            left: 0 !important;\r\n            right: 0 !important;\r\n            bottom: 0 !important;\r\n            overflow: hidden !important;\r\n          }\r\n        }\r\n\r\n        /* Messenger-like smooth scrolling */\r\n        .chat-messages {\r\n          scroll-behavior: smooth;\r\n        }\r\n\r\n        /* Enhanced message bubble animations */\r\n        .message-bubble {\r\n          animation: messageSlideIn 0.3s ease-out;\r\n        }\r\n\r\n        @keyframes messageSlideIn {\r\n          from {\r\n            opacity: 0;\r\n            transform: translateY(10px);\r\n          }\r\n          to {\r\n            opacity: 1;\r\n            transform: translateY(0);\r\n          }\r\n        }\r\n\r\n        /* Input focus effects */\r\n        .chat-input-container:focus-within {\r\n          box-shadow: 0 0 0 2px rgba(243, 168, 35, 0.2);\r\n        }\r\n      `}} />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAmBe,SAAS,WAAW,EACjC,YAAY,EACZ,WAAW,cAAc,EACzB,WAAW,KAAK,EAChB,QAAQ,QAAQ,EACA;;IAChB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4KAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,eAAe;IACf,MAAM,cAAc;QAClB,QAAQ;YACN,SAAS;YACT,OAAO;YACP,MAAM;YACN,IAAI;YACJ,QAAQ;QACV;QACA,MAAM;YACJ,SAAS;YACT,OAAO;YACP,MAAM;YACN,IAAI;YACJ,QAAQ;QACV;QACA,OAAO;YACL,SAAS;YACT,OAAO;YACP,MAAM;YACN,IAAI;YACJ,QAAQ;QACV;IACF;IAEA,MAAM,SAAS,WAAW,CAAC,MAAM;IAEjC,4FAA4F;IAC5F,MAAM,kBAAkB;QACtB,gBAAgB;QAChB,eAAe;IACjB;IAIA,wBAAwB;IACxB,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,YAAY,CAAC,eAAe;gBAC9B,MAAM,QAAQ;kDAAW;wBACvB,UAAU;oBACZ;iDAAG,OAAO,uBAAuB;gBAEjC;4CAAO,IAAM,aAAa;;YAC5B;QACF;+BAAG;QAAC;QAAU;KAAc;IAE5B,+CAA+C;IAC/C,oBAAoB;IACpB,2DAA2D;IAC3D,uCAAuC;IACvC,8BAA8B;IAC9B,gBAAgB;IAChB,wCAAwC;IACxC,MAAM;IACN,mDAAmD;IAEnD,oBAAoB;IACpB,uBAAuB;IACvB,uCAAuC;IACvC,+BAA+B;IAC/B,gBAAgB;IAChB,wCAAwC;IACxC,MAAM;IACN,qBAAqB;IAErB,MAAM,mBAAmB;QACvB,iBAAiB;QAEjB,IAAI,QAAQ;YACV,IAAI,aAAa;gBACf,eAAe;YACjB,OAAO;gBACL,UAAU;gBACV,eAAe;YACjB;QACF,OAAO;YACL,UAAU;YACV,eAAe;QACjB;IACF;IAEA,MAAM,kBAAkB;QACtB,UAAU;QACV,eAAe;IACjB;IAEA,MAAM,qBAAqB;QACzB,eAAe,CAAC;IAClB;IAEA,qBACE;;0BAEE,4MAAC,6JAAA,CAAA,UAAU;gBACT,QAAQ;gBACR,SAAS;gBACT,YAAY;gBACZ,aAAa;gBACb,UAAU;gBACV,cAAc;;;;;;YAIf,CAAC,wBACA,4MAAC;gBAAI,WAAW,CAAC,MAAM,EAAE,eAAe,CAAC,SAAS,CAAC,SAAS,CAAC;;kCAI3D,4MAAC;wBACC,SAAS;wBACT,WAAW,CAAC,2BAA2B,EAAE,OAAO,OAAO,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,0JAA0J,CAAC;wBACnO,OAAM;wBACN,OAAO;4BAAE,QAAQ;wBAAK;;0CAEtB,4MAAC,+PAAA,CAAA,0BAAuB;gCAAC,WAAU;;;;;;0CAGnC,4MAAC;gCAAI,WAAU;0CACb,cAAA,4MAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;kCAKnB,4MAAC;wBAAI,WAAU;;;;;;;;;;;;YAKlB,aAAkB,6BACjB,4MAAC;gBACC,WAAW,CAAC;oBACV,IAAI,EAAE,OAAO,IAAI,EAAE,GAAG,KAAK,KAAK;wBAC9B,EAAE,cAAc;wBAChB;oBACF;gBACF;gBACA,UAAU,CAAC;gBACX,WAAU;;;;;;0BAKd,4MAAC;gBAAM,yBAAyB;oBAAC,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MA8F1C,CAAC;gBAAA;;;;;;;;AAGP;GAzPwB;KAAA", "debugId": null}}]}