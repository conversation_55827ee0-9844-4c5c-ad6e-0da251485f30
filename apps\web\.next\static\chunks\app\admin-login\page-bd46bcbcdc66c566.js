(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2102],{8246:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(12115);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))})},10184:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(12115);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},14170:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(12115);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))})},35695:(e,t,r)=>{"use strict";var a=r(18999);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"usePathname")&&r.d(t,{usePathname:function(){return a.usePathname}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}})},42528:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var a=r(95155),s=r(12115),n=r(40283),o=r(35695),l=r(9575),i=r(56104),d=r(35317),c=r(8246),u=r(14170),m=r(94038),g=r(48987),h=r(10184);function f(){let{user:e,loading:t}=(0,n.A)(),r=(0,o.useRouter)(),[f,x]=(0,s.useState)(!1),[p,w]=(0,s.useState)(""),[b,y]=(0,s.useState)(!1),[v,j]=(0,s.useState)({email:"<EMAIL>",password:"123456"});s.useEffect(()=>{!t&&e&&("admin"===e.role?r.replace("/admin"):r.replace("/"))},[e,t,r]);let N=async e=>{e.preventDefault(),x(!0),w("");try{let e,t,r;console.log("\uD83D\uDD10 Attempting admin login..."),console.log("Attempting login with: ".concat(v.email));try{e=await (0,l.x9)(i.j2,v.email,v.password),console.log("✅ Login successful with existing user: ".concat(v.email))}catch(t){let e=t&&"object"==typeof t&&"code"in t?t.code:"unknown";if(console.log("❌ Firebase Auth failed with code:",e),"auth/user-not-found"===e)throw Error("Admin account not found. Please contact system administrator.");if("auth/wrong-password"===e||"auth/invalid-credential"===e)throw Error("Incorrect password. Please check your password and try again.");if("auth/too-many-requests"===e)throw Error("Too many failed login attempts. Please try again later or reset your password.");else if("auth/network-request-failed"===e)throw Error("Network error. Please check your internet connection and try again.");else if("auth/user-disabled"===e)throw Error("This account has been disabled. Please contact support.");else throw Error("Authentication failed: ".concat(t instanceof Error?t.message:"Unknown error"))}let a=e.user;console.log("\uD83D\uDC64 Authenticated user UID:",a.uid),console.log("\uD83D\uDC64 Authenticated user email:",a.email);let s=(0,d.H9)(i.db,"users",a.uid);try{t=await (0,d.x7)(s),console.log("\uD83D\uDCC4 User document fetch result:",t.exists())}catch(e){throw console.error("❌ Error fetching user document:",e),Error("Failed to access user database. Please try again.")}if(t.exists()){console.log("\uD83D\uDCC4 User document exists, checking role...");let e=t.data();if(console.log("\uD83D\uDC64 Current user role:",null==e?void 0:e.role),(null==e?void 0:e.role)!=="admin"){console.log("\uD83D\uDD04 Updating user role to admin...");try{await (0,d.BN)(s,{role:"admin",email:v.email,name:"John Lloyd Callao",isActive:!0,isVerified:!0,updatedAt:new Date,lastLoginAt:new Date},{merge:!0}),console.log("✅ Updated user role to admin")}catch(e){throw console.error("❌ Error updating user role:",e),Error("Failed to update user permissions. Please try again.")}}else try{await (0,d.BN)(s,{lastLoginAt:new Date,updatedAt:new Date},{merge:!0}),console.log("✅ Updated last login time")}catch(e){console.warn("⚠️ Could not update last login time:",e)}}else{console.log("\uD83D\uDCDD Creating user document in Firestore...");try{let e={uid:a.uid,email:v.email,name:"John Lloyd Callao",role:"admin",isActive:!0,isVerified:!0,createdAt:new Date,updatedAt:new Date,lastLoginAt:new Date};await (0,d.BN)(s,e),console.log("✅ Created user document successfully")}catch(e){throw console.error("❌ Error creating user document:",e),Error("Failed to create user profile. Please try again.")}}let n=(0,d.H9)(i.db,"admins",a.uid);try{r=await (0,d.x7)(n),console.log("\uD83D\uDC51 Admin document fetch result:",r.exists())}catch(e){throw console.error("❌ Error fetching admin document:",e),Error("Failed to access admin database. Please try again.")}if(r.exists()){console.log("\uD83D\uDC51 Admin document already exists");try{await (0,d.BN)(n,{updatedAt:new Date},{merge:!0}),console.log("✅ Updated admin last access time")}catch(e){console.warn("⚠️ Could not update admin last access time:",e)}}else{console.log("\uD83D\uDC51 Creating admin document...");try{let e={userRef:"users/".concat(a.uid),employeeId:"ADMIN-001",fullName:"John Lloyd Callao",department:"technical",accessLevel:"super_admin",permissions:["manage_vendors","handle_disputes","view_analytics","driver_verification","system_config","manage_admins","manage_customers"],assignedRegions:["US","CA"],createdAt:new Date,updatedAt:new Date};await (0,d.BN)(n,e),console.log("✅ Created admin document successfully")}catch(e){throw console.error("❌ Error creating admin document:",e),Error("Failed to create admin profile. Please try again.")}}console.log("\uD83C\uDF89 Admin login successful! Redirecting to admin panel..."),await new Promise(e=>setTimeout(e,500)),window.location.href="/admin"}catch(e){console.error("❌ Login error:",e),w(e instanceof Error?e.message:"Failed to login as admin")}finally{x(!1)}};return t?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 to-red-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-orange-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Checking authentication..."})]})}):e?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 to-red-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-orange-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Redirecting to admin panel..."})]})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",style:{background:"linear-gradient(to bottom right, #fef3e2, #fed7aa)"},children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto h-16 w-16 rounded-full flex items-center justify-center",style:{backgroundColor:"#f3a823"},children:(0,a.jsx)(c.A,{className:"h-8 w-8 text-white"})}),(0,a.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Tap2Go Admin Login"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:"Super Admin Access Portal"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[(0,a.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[p&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,a.jsx)("div",{className:"flex",children:(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-red-800",children:"Login Failed"}),(0,a.jsx)("div",{className:"mt-2 text-sm text-red-700",children:p})]})})}),(0,a.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 text-blue-400"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-blue-800",children:"Admin Credentials Pre-filled"}),(0,a.jsx)("div",{className:"mt-2 text-sm text-blue-700",children:"Your super admin credentials are ready to use."})]})]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Admin Email"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",value:v.email,onChange:e=>j({...v,email:e.target.value}),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",placeholder:"Enter admin email",required:!0})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,a.jsx)("input",{id:"password",name:"password",type:b?"text":"password",value:v.password,onChange:e=>j({...v,password:e.target.value}),className:"w-full pl-10 pr-12 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent",placeholder:"Enter password",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>y(!b),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:b?(0,a.jsx)(g.A,{className:"h-5 w-5"}):(0,a.jsx)(h.A,{className:"h-5 w-5"})})]})]}),(0,a.jsx)("button",{type:"submit",disabled:f,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",style:{backgroundColor:"#f3a823","--tw-ring-color":"#f3a823"},onMouseEnter:e=>e.currentTarget.style.backgroundColor="#ef7b06",onMouseLeave:e=>e.currentTarget.style.backgroundColor="#f3a823",children:f?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Logging in..."]}):"Login as Super Admin"})]}),(0,a.jsxs)("div",{className:"mt-6 text-center",children:[(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"This is your dedicated super admin login portal."}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"After login, you'll have full administrative access to Tap2Go."})]})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"\uD83D\uDD12 Secure admin access • Change password after first login"})})]})})}},44732:(e,t,r)=>{Promise.resolve().then(r.bind(r,42528))},48987:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(12115);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))})},94038:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(12115);let s=a.forwardRef(function(e,t){let{title:r,titleId:s,...n}=e;return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":s},n),r?a.createElement("title",{id:s},r):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 5.25a3 3 0 0 1 3 3m3 0a6 6 0 0 1-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1 1 21.75 8.25Z"}))})}},e=>{var t=t=>e(e.s=t);e.O(0,[9149,4546,5458,991,283,8441,1684,7358],()=>t(44732)),_N_E=e.O()}]);