{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/RestaurantCard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { Restaurant } from '@/types';\r\nimport { StarIcon, ClockIcon, TruckIcon } from '@heroicons/react/24/solid';\r\n\r\ninterface RestaurantCardProps {\r\n  restaurant: Restaurant;\r\n}\r\n\r\nexport default function RestaurantCard({ restaurant }: RestaurantCardProps) {\r\n  const [imageError, setImageError] = useState(false);\r\n\r\n  return (\r\n    <Link href={`/restaurant/${restaurant.id}`} className=\"block\">\r\n      <div className=\"card hover:shadow-lg transition-shadow duration-200\">\r\n        {/* Restaurant Image */}\r\n        <div className=\"relative h-48 w-full\">\r\n          {imageError || !restaurant.image ? (\r\n            <div className=\"w-full h-full bg-gray-200 flex items-center justify-center\">\r\n              <div className=\"text-center\">\r\n                <div className=\"w-16 h-16 mx-auto mb-2 bg-gray-300 rounded-full flex items-center justify-center\">\r\n                  <span className=\"text-2xl font-bold text-gray-500\">\r\n                    {restaurant.name ? restaurant.name.charAt(0) : '?'}\r\n                  </span>\r\n                </div>\r\n                <p className=\"text-sm text-gray-500\">{restaurant.name || 'Restaurant'}</p>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <Image\r\n              src={restaurant.image}\r\n              alt={restaurant.name || 'Restaurant'}\r\n              fill\r\n              className=\"object-cover\"\r\n              sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\r\n              onError={() => setImageError(true)}\r\n            />\r\n          )}\r\n          {restaurant.featured && (\r\n            <div className=\"absolute top-2 left-2 text-white px-2 py-1 rounded-md text-xs font-semibold\" style={{ backgroundColor: '#f3a823' }}>\r\n              Featured\r\n            </div>\r\n          )}\r\n          {!restaurant.isOpen && (\r\n            <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\r\n              <span className=\"text-white font-semibold text-lg\">Closed</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Restaurant Info */}\r\n        <div className=\"p-4\">\r\n          <div className=\"flex items-start justify-between mb-2\">\r\n            <h3 className=\"text-lg font-semibold text-gray-900 line-clamp-1\">\r\n              {restaurant.name}\r\n            </h3>\r\n            <div className=\"flex items-center space-x-1 ml-2\">\r\n              <StarIcon className=\"h-4 w-4 text-yellow-400\" />\r\n              <span className=\"text-sm font-medium text-gray-700\">\r\n                {restaurant.rating ? restaurant.rating.toFixed(1) : 'N/A'}\r\n              </span>\r\n              <span className=\"text-sm text-gray-500\">\r\n                ({restaurant.reviewCount || 0})\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          <p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">\r\n            {restaurant.description}\r\n          </p>\r\n\r\n          {/*The Cuisine Tags */}\r\n          <div className=\"flex flex-wrap gap-1 mb-3\">\r\n            {restaurant.cuisine.slice(0, 3).map((cuisine, index) => (\r\n              <span\r\n                key={index}\r\n                className=\"inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full\"\r\n              >\r\n                {cuisine}\r\n              </span>\r\n            ))}\r\n            {restaurant.cuisine.length > 3 && (\r\n              <span className=\"inline-block bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full\">\r\n                +{restaurant.cuisine.length - 3} more\r\n              </span>\r\n            )}\r\n          </div>\r\n\r\n          {/* Delivery Info */}\r\n          <div className=\"flex items-center justify-between text-sm text-gray-600\">\r\n            <div className=\"flex items-center space-x-4\">\r\n              <div className=\"flex items-center space-x-1\">\r\n                <ClockIcon className=\"h-4 w-4\" />\r\n                <span>{restaurant.deliveryTime}</span>\r\n              </div>\r\n              <div className=\"flex items-center space-x-1\">\r\n                <TruckIcon className=\"h-4 w-4\" />\r\n                <span>${restaurant.deliveryFee ? restaurant.deliveryFee.toFixed(2) : '0.00'}</span>\r\n              </div>\r\n            </div>\r\n            <div className=\"text-right\">\r\n              <span className=\"text-xs text-gray-500\">Min order</span>\r\n              <div className=\"font-medium\">${restaurant.minimumOrder ? restaurant.minimumOrder.toFixed(2) : '0.00'}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AAAA;AAAA;;;;;;AAMe,SAAS,eAAe,EAAE,UAAU,EAAuB;IACxE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,6PAAC,2KAAA,CAAA,UAAI;QAAC,MAAM,CAAC,YAAY,EAAE,WAAW,EAAE,EAAE;QAAE,WAAU;kBACpD,cAAA,6PAAC;YAAI,WAAU;;8BAEb,6PAAC;oBAAI,WAAU;;wBACZ,cAAc,CAAC,WAAW,KAAK,iBAC9B,6PAAC;4BAAI,WAAU;sCACb,cAAA,6PAAC;gCAAI,WAAU;;kDACb,6PAAC;wCAAI,WAAU;kDACb,cAAA,6PAAC;4CAAK,WAAU;sDACb,WAAW,IAAI,GAAG,WAAW,IAAI,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;kDAGnD,6PAAC;wCAAE,WAAU;kDAAyB,WAAW,IAAI,IAAI;;;;;;;;;;;;;;;;iDAI7D,6PAAC,4IAAA,CAAA,UAAK;4BACJ,KAAK,WAAW,KAAK;4BACrB,KAAK,WAAW,IAAI,IAAI;4BACxB,IAAI;4BACJ,WAAU;4BACV,OAAM;4BACN,SAAS,IAAM,cAAc;;;;;;wBAGhC,WAAW,QAAQ,kBAClB,6PAAC;4BAAI,WAAU;4BAA8E,OAAO;gCAAE,iBAAiB;4BAAU;sCAAG;;;;;;wBAIrI,CAAC,WAAW,MAAM,kBACjB,6PAAC;4BAAI,WAAU;sCACb,cAAA,6PAAC;gCAAK,WAAU;0CAAmC;;;;;;;;;;;;;;;;;8BAMzD,6PAAC;oBAAI,WAAU;;sCACb,6PAAC;4BAAI,WAAU;;8CACb,6PAAC;oCAAG,WAAU;8CACX,WAAW,IAAI;;;;;;8CAElB,6PAAC;oCAAI,WAAU;;sDACb,6PAAC,4NAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6PAAC;4CAAK,WAAU;sDACb,WAAW,MAAM,GAAG,WAAW,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;sDAEtD,6PAAC;4CAAK,WAAU;;gDAAwB;gDACpC,WAAW,WAAW,IAAI;gDAAE;;;;;;;;;;;;;;;;;;;sCAKpC,6PAAC;4BAAE,WAAU;sCACV,WAAW,WAAW;;;;;;sCAIzB,6PAAC;4BAAI,WAAU;;gCACZ,WAAW,OAAO,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC5C,6PAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;gCAMR,WAAW,OAAO,CAAC,MAAM,GAAG,mBAC3B,6PAAC;oCAAK,WAAU;;wCAAwE;wCACpF,WAAW,OAAO,CAAC,MAAM,GAAG;wCAAE;;;;;;;;;;;;;sCAMtC,6PAAC;4BAAI,WAAU;;8CACb,6PAAC;oCAAI,WAAU;;sDACb,6PAAC;4CAAI,WAAU;;8DACb,6PAAC,8NAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,6PAAC;8DAAM,WAAW,YAAY;;;;;;;;;;;;sDAEhC,6PAAC;4CAAI,WAAU;;8DACb,6PAAC,8NAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;8DACrB,6PAAC;;wDAAK;wDAAE,WAAW,WAAW,GAAG,WAAW,WAAW,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;8CAGzE,6PAAC;oCAAI,WAAU;;sDACb,6PAAC;4CAAK,WAAU;sDAAwB;;;;;;sDACxC,6PAAC;4CAAI,WAAU;;gDAAc;gDAAE,WAAW,YAAY,GAAG,WAAW,YAAY,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5G", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/lib/transformers/restaurant.ts"], "sourcesContent": ["import { Restaurant } from '@/types';\r\nimport { DocumentData } from 'firebase/firestore';\r\n\r\n/**\r\n * Single source of truth for transforming Firestore restaurant data\r\n * to the Restaurant interface. This ensures consistency across all pages.\r\n */\r\nexport const transformRestaurantData = (doc: { id: string; data: () => DocumentData }): Restaurant => {\r\n  const data = doc.data();\r\n  \r\n  return {\r\n    id: doc.id,\r\n    name: data.outletName || data.name || '',\r\n    description: data.description || '',\r\n    image: data.coverImageUrl || data.image || '/api/placeholder/300/200',\r\n    coverImage: data.coverImageUrl || data.image || '',\r\n    cuisine: data.cuisineTags || data.cuisine || [],\r\n    address: data.address || {\r\n      street: '',\r\n      city: '',\r\n      state: '',\r\n      zipCode: '',\r\n      country: ''\r\n    },\r\n    phone: data.outletPhone || data.phone || '',\r\n    email: data.email || '',\r\n    ownerId: data.vendorRef || data.ownerId || '',\r\n    rating: data.avgRating || data.rating || 0,\r\n    reviewCount: data.totalReviews || data.reviewCount || 0, // CRITICAL: Check totalReviews first!\r\n    deliveryTime: data.estimatedDeliveryRange || data.deliveryTime || 'N/A',\r\n    deliveryFee: data.deliveryFees?.base || data.deliveryFee || 0,\r\n    minimumOrder: data.minOrderValue || data.minimumOrder || 0,\r\n    isOpen: data.isAcceptingOrders !== undefined ? data.isAcceptingOrders : (data.isOpen !== undefined ? data.isOpen : true),\r\n    openingHours: data.operatingHours || data.openingHours || {\r\n      monday: { open: '09:00', close: '22:00', isClosed: false },\r\n      tuesday: { open: '09:00', close: '22:00', isClosed: false },\r\n      wednesday: { open: '09:00', close: '22:00', isClosed: false },\r\n      thursday: { open: '09:00', close: '22:00', isClosed: false },\r\n      friday: { open: '09:00', close: '22:00', isClosed: false },\r\n      saturday: { open: '09:00', close: '22:00', isClosed: false },\r\n      sunday: { open: '09:00', close: '22:00', isClosed: false }\r\n    },\r\n    featured: data.featured || false,\r\n    status: data.platformStatus || data.status || 'active',\r\n    commissionRate: data.commissionRate || 15,\r\n    totalOrders: data.totalOrders || 0,\r\n    totalRevenue: data.totalRevenue || 0,\r\n    averagePreparationTime: data.preparationTime?.average || data.averagePreparationTime || 20,\r\n    createdAt: data.createdAt?.toDate?.() || new Date(),\r\n    updatedAt: data.updatedAt?.toDate?.() || new Date()\r\n  };\r\n};\r\n\r\n/**\r\n * Transform multiple restaurant documents\r\n */\r\nexport const transformRestaurantsData = (docs: { id: string; data: () => DocumentData }[]): Restaurant[] => {\r\n  return docs.map(transformRestaurantData);\r\n};\r\n"], "names": [], "mappings": ";;;;AAOO,MAAM,0BAA0B,CAAC;IACtC,MAAM,OAAO,IAAI,IAAI;IAErB,OAAO;QACL,IAAI,IAAI,EAAE;QACV,MAAM,KAAK,UAAU,IAAI,KAAK,IAAI,IAAI;QACtC,aAAa,KAAK,WAAW,IAAI;QACjC,OAAO,KAAK,aAAa,IAAI,KAAK,KAAK,IAAI;QAC3C,YAAY,KAAK,aAAa,IAAI,KAAK,KAAK,IAAI;QAChD,SAAS,KAAK,WAAW,IAAI,KAAK,OAAO,IAAI,EAAE;QAC/C,SAAS,KAAK,OAAO,IAAI;YACvB,QAAQ;YACR,MAAM;YACN,OAAO;YACP,SAAS;YACT,SAAS;QACX;QACA,OAAO,KAAK,WAAW,IAAI,KAAK,KAAK,IAAI;QACzC,OAAO,KAAK,KAAK,IAAI;QACrB,SAAS,KAAK,SAAS,IAAI,KAAK,OAAO,IAAI;QAC3C,QAAQ,KAAK,SAAS,IAAI,KAAK,MAAM,IAAI;QACzC,aAAa,KAAK,YAAY,IAAI,KAAK,WAAW,IAAI;QACtD,cAAc,KAAK,sBAAsB,IAAI,KAAK,YAAY,IAAI;QAClE,aAAa,KAAK,YAAY,EAAE,QAAQ,KAAK,WAAW,IAAI;QAC5D,cAAc,KAAK,aAAa,IAAI,KAAK,YAAY,IAAI;QACzD,QAAQ,KAAK,iBAAiB,KAAK,YAAY,KAAK,iBAAiB,GAAI,KAAK,MAAM,KAAK,YAAY,KAAK,MAAM,GAAG;QACnH,cAAc,KAAK,cAAc,IAAI,KAAK,YAAY,IAAI;YACxD,QAAQ;gBAAE,MAAM;gBAAS,OAAO;gBAAS,UAAU;YAAM;YACzD,SAAS;gBAAE,MAAM;gBAAS,OAAO;gBAAS,UAAU;YAAM;YAC1D,WAAW;gBAAE,MAAM;gBAAS,OAAO;gBAAS,UAAU;YAAM;YAC5D,UAAU;gBAAE,MAAM;gBAAS,OAAO;gBAAS,UAAU;YAAM;YAC3D,QAAQ;gBAAE,MAAM;gBAAS,OAAO;gBAAS,UAAU;YAAM;YACzD,UAAU;gBAAE,MAAM;gBAAS,OAAO;gBAAS,UAAU;YAAM;YAC3D,QAAQ;gBAAE,MAAM;gBAAS,OAAO;gBAAS,UAAU;YAAM;QAC3D;QACA,UAAU,KAAK,QAAQ,IAAI;QAC3B,QAAQ,KAAK,cAAc,IAAI,KAAK,MAAM,IAAI;QAC9C,gBAAgB,KAAK,cAAc,IAAI;QACvC,aAAa,KAAK,WAAW,IAAI;QACjC,cAAc,KAAK,YAAY,IAAI;QACnC,wBAAwB,KAAK,eAAe,EAAE,WAAW,KAAK,sBAAsB,IAAI;QACxF,WAAW,KAAK,SAAS,EAAE,cAAc,IAAI;QAC7C,WAAW,KAAK,SAAS,EAAE,cAAc,IAAI;IAC/C;AACF;AAKO,MAAM,2BAA2B,CAAC;IACvC,OAAO,KAAK,GAAG,CAAC;AAClB", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/lib/googleMapsLoader.ts"], "sourcesContent": ["// Professional Google Maps API Loader\r\n// Singleton pattern to prevent multiple API loads\r\n// Industry standard implementation\r\n\r\ninterface GoogleMapsLoaderOptions {\r\n  apiKey: string;\r\n  libraries?: string[];\r\n  language?: string;\r\n  region?: string;\r\n}\r\n\r\nclass GoogleMapsLoader {\r\n  private static instance: GoogleMapsLoader;\r\n  private isLoaded = false;\r\n  private isLoading = false;\r\n  private loadPromise: Promise<void> | null = null;\r\n  private callbacks: Array<() => void> = [];\r\n  private errorCallbacks: Array<(error: string) => void> = [];\r\n\r\n  private constructor() {}\r\n\r\n  public static getInstance(): GoogleMapsLoader {\r\n    if (!GoogleMapsLoader.instance) {\r\n      GoogleMapsLoader.instance = new GoogleMapsLoader();\r\n    }\r\n    return GoogleMapsLoader.instance;\r\n  }\r\n\r\n  public async load(options: GoogleMapsLoaderOptions): Promise<void> {\r\n    // If already loaded, resolve immediately\r\n    if (this.isLoaded && window.google && window.google.maps) {\r\n      return Promise.resolve();\r\n    }\r\n\r\n    // If currently loading, return the existing promise\r\n    if (this.isLoading && this.loadPromise) {\r\n      return this.loadPromise;\r\n    }\r\n\r\n    // Start loading\r\n    this.isLoading = true;\r\n    this.loadPromise = this.loadGoogleMaps(options);\r\n    \r\n    return this.loadPromise;\r\n  }\r\n\r\n  private loadGoogleMaps(options: GoogleMapsLoaderOptions): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      // Check if already loaded\r\n      if (window.google && window.google.maps) {\r\n        this.isLoaded = true;\r\n        this.isLoading = false;\r\n        this.notifyCallbacks();\r\n        resolve();\r\n        return;\r\n      }\r\n\r\n      // Check if script already exists\r\n      const existingScript = document.querySelector('script[src*=\"maps.googleapis.com\"]');\r\n      if (existingScript) {\r\n        // Script exists, wait for it to load\r\n        const checkLoaded = () => {\r\n          if (window.google && window.google.maps) {\r\n            this.isLoaded = true;\r\n            this.isLoading = false;\r\n            this.notifyCallbacks();\r\n            resolve();\r\n          } else {\r\n            setTimeout(checkLoaded, 100);\r\n          }\r\n        };\r\n        checkLoaded();\r\n        return;\r\n      }\r\n\r\n      // Create unique callback name\r\n      const callbackName = `googleMapsCallback_${Date.now()}`;\r\n      \r\n      // Set up global callback\r\n      (window as unknown as Record<string, unknown>)[callbackName] = () => {\r\n        this.isLoaded = true;\r\n        this.isLoading = false;\r\n        this.notifyCallbacks();\r\n\r\n        // Clean up\r\n        delete (window as unknown as Record<string, unknown>)[callbackName];\r\n        resolve();\r\n      };\r\n\r\n      // Build URL\r\n      const libraries = options.libraries?.join(',') || 'places';\r\n      const language = options.language || 'en';\r\n      const region = options.region || 'PH';\r\n      \r\n      const url = `https://maps.googleapis.com/maps/api/js?key=${options.apiKey}&libraries=${libraries}&language=${language}&region=${region}&callback=${callbackName}`;\r\n\r\n      // Create and load script\r\n      const script = document.createElement('script');\r\n      script.src = url;\r\n      script.async = true;\r\n      script.defer = true;\r\n      \r\n      script.onerror = () => {\r\n        this.isLoading = false;\r\n        this.loadPromise = null;\r\n        const error = 'Failed to load Google Maps API';\r\n        this.notifyErrorCallbacks(error);\r\n        reject(new Error(error));\r\n      };\r\n\r\n      document.head.appendChild(script);\r\n    });\r\n  }\r\n\r\n  public onLoad(callback: () => void): void {\r\n    if (this.isLoaded) {\r\n      callback();\r\n    } else {\r\n      this.callbacks.push(callback);\r\n    }\r\n  }\r\n\r\n  public onError(callback: (error: string) => void): void {\r\n    this.errorCallbacks.push(callback);\r\n  }\r\n\r\n  private notifyCallbacks(): void {\r\n    this.callbacks.forEach(callback => callback());\r\n    this.callbacks = [];\r\n  }\r\n\r\n  private notifyErrorCallbacks(error: string): void {\r\n    this.errorCallbacks.forEach(callback => callback(error));\r\n    this.errorCallbacks = [];\r\n  }\r\n\r\n  public isGoogleMapsLoaded(): boolean {\r\n    return this.isLoaded && !!(window.google && window.google.maps);\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const googleMapsLoader = GoogleMapsLoader.getInstance();\r\n\r\n// Convenience function\r\nexport const loadGoogleMaps = async (apiKey: string): Promise<void> => {\r\n  return googleMapsLoader.load({\r\n    apiKey,\r\n    libraries: ['places'],\r\n    region: 'PH'\r\n  });\r\n};\r\n\r\n// Type declarations\r\ndeclare global {\r\n  interface Window {\r\n    google: typeof google;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,sCAAsC;AACtC,kDAAkD;AAClD,mCAAmC;;;;;AASnC,MAAM;IACJ,OAAe,SAA2B;IAClC,WAAW,MAAM;IACjB,YAAY,MAAM;IAClB,cAAoC,KAAK;IACzC,YAA+B,EAAE,CAAC;IAClC,iBAAiD,EAAE,CAAC;IAE5D,aAAsB,CAAC;IAEvB,OAAc,cAAgC;QAC5C,IAAI,CAAC,iBAAiB,QAAQ,EAAE;YAC9B,iBAAiB,QAAQ,GAAG,IAAI;QAClC;QACA,OAAO,iBAAiB,QAAQ;IAClC;IAEA,MAAa,KAAK,OAAgC,EAAiB;QACjE,yCAAyC;QACzC,IAAI,IAAI,CAAC,QAAQ,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,EAAE;YACxD,OAAO,QAAQ,OAAO;QACxB;QAEA,oDAAoD;QACpD,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,EAAE;YACtC,OAAO,IAAI,CAAC,WAAW;QACzB;QAEA,gBAAgB;QAChB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC;QAEvC,OAAO,IAAI,CAAC,WAAW;IACzB;IAEQ,eAAe,OAAgC,EAAiB;QACtE,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,0BAA0B;YAC1B,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,EAAE;gBACvC,IAAI,CAAC,QAAQ,GAAG;gBAChB,IAAI,CAAC,SAAS,GAAG;gBACjB,IAAI,CAAC,eAAe;gBACpB;gBACA;YACF;YAEA,iCAAiC;YACjC,MAAM,iBAAiB,SAAS,aAAa,CAAC;YAC9C,IAAI,gBAAgB;gBAClB,qCAAqC;gBACrC,MAAM,cAAc;oBAClB,IAAI,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI,EAAE;wBACvC,IAAI,CAAC,QAAQ,GAAG;wBAChB,IAAI,CAAC,SAAS,GAAG;wBACjB,IAAI,CAAC,eAAe;wBACpB;oBACF,OAAO;wBACL,WAAW,aAAa;oBAC1B;gBACF;gBACA;gBACA;YACF;YAEA,8BAA8B;YAC9B,MAAM,eAAe,CAAC,mBAAmB,EAAE,KAAK,GAAG,IAAI;YAEvD,yBAAyB;YACxB,MAA6C,CAAC,aAAa,GAAG;gBAC7D,IAAI,CAAC,QAAQ,GAAG;gBAChB,IAAI,CAAC,SAAS,GAAG;gBACjB,IAAI,CAAC,eAAe;gBAEpB,WAAW;gBACX,OAAO,AAAC,MAA6C,CAAC,aAAa;gBACnE;YACF;YAEA,YAAY;YACZ,MAAM,YAAY,QAAQ,SAAS,EAAE,KAAK,QAAQ;YAClD,MAAM,WAAW,QAAQ,QAAQ,IAAI;YACrC,MAAM,SAAS,QAAQ,MAAM,IAAI;YAEjC,MAAM,MAAM,CAAC,4CAA4C,EAAE,QAAQ,MAAM,CAAC,WAAW,EAAE,UAAU,UAAU,EAAE,SAAS,QAAQ,EAAE,OAAO,UAAU,EAAE,cAAc;YAEjK,yBAAyB;YACzB,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,OAAO,GAAG,GAAG;YACb,OAAO,KAAK,GAAG;YACf,OAAO,KAAK,GAAG;YAEf,OAAO,OAAO,GAAG;gBACf,IAAI,CAAC,SAAS,GAAG;gBACjB,IAAI,CAAC,WAAW,GAAG;gBACnB,MAAM,QAAQ;gBACd,IAAI,CAAC,oBAAoB,CAAC;gBAC1B,OAAO,IAAI,MAAM;YACnB;YAEA,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF;IAEO,OAAO,QAAoB,EAAQ;QACxC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB;QACF,OAAO;YACL,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QACtB;IACF;IAEO,QAAQ,QAAiC,EAAQ;QACtD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC;IAC3B;IAEQ,kBAAwB;QAC9B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA,WAAY;QACnC,IAAI,CAAC,SAAS,GAAG,EAAE;IACrB;IAEQ,qBAAqB,KAAa,EAAQ;QAChD,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA,WAAY,SAAS;QACjD,IAAI,CAAC,cAAc,GAAG,EAAE;IAC1B;IAEO,qBAA8B;QACnC,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,IAAI;IAChE;AACF;AAGO,MAAM,mBAAmB,iBAAiB,WAAW;AAGrD,MAAM,iBAAiB,OAAO;IACnC,OAAO,iBAAiB,IAAI,CAAC;QAC3B;QACA,WAAW;YAAC;SAAS;QACrB,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 543, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/ProfessionalMap.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\r\nimport { loadGoogleMaps } from '@/lib/googleMapsLoader';\r\n\r\n// Professional Google Maps Component\r\n// Based on industry best practices from Google Maps documentation\r\n// Stable, performant, and professional implementation\r\n// Uses singleton pattern to prevent multiple API loads\r\n\r\ninterface MapProps {\r\n  center?: { lat: number; lng: number };\r\n  zoom?: number;\r\n  height?: string;\r\n  className?: string;\r\n  onLocationSelect?: (location: { lat: number; lng: number; address: string }) => void;\r\n}\r\n\r\n// Global types handled by googleMapsLoader\r\n\r\nexport default function ProfessionalMap({\r\n  center = { lat: 14.5995, lng: 120.9842 }, // Manila default\r\n  zoom = 15,\r\n  height = '400px',\r\n  className = '',\r\n  onLocationSelect\r\n}: MapProps) {\r\n  const mapRef = useRef<HTMLDivElement>(null);\r\n  const mapInstanceRef = useRef<google.maps.Map | null>(null);\r\n  const markerRef = useRef<google.maps.Marker | null>(null);\r\n  const infoWindowRef = useRef<google.maps.InfoWindow | null>(null);\r\n  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);\r\n  const searchInputRef = useRef<HTMLInputElement>(null);\r\n  \r\n  const [isLoaded, setIsLoaded] = useState(false);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [selectedLocation, setSelectedLocation] = useState<string>('');\r\n\r\n  // Load Google Maps API using singleton loader\r\n  const loadMapsAPI = useCallback(async () => {\r\n    const apiKey = process.env.NEXT_PUBLIC_MAPS_FRONTEND_KEY;\r\n    if (!apiKey) {\r\n      setError('Google Maps API key not configured');\r\n      setIsLoading(false);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await loadGoogleMaps(apiKey);\r\n      setIsLoaded(true);\r\n      setIsLoading(false);\r\n    } catch (err) {\r\n      setError('Failed to load Google Maps');\r\n      setIsLoading(false);\r\n      console.error('Google Maps loading error:', err);\r\n    }\r\n  }, []);\r\n\r\n  // Initialize map\r\n  const initializeMap = useCallback(() => {\r\n    if (!mapRef.current || !window.google || mapInstanceRef.current) return;\r\n\r\n    try {\r\n      // Create map with professional settings\r\n      mapInstanceRef.current = new window.google.maps.Map(mapRef.current, {\r\n        center,\r\n        zoom,\r\n        mapTypeId: 'roadmap',\r\n        disableDefaultUI: false,\r\n        zoomControl: true,\r\n        mapTypeControl: false,\r\n        scaleControl: true,\r\n        streetViewControl: false,\r\n        rotateControl: false,\r\n        fullscreenControl: true,\r\n        gestureHandling: 'auto',\r\n        styles: [\r\n          {\r\n            featureType: 'poi',\r\n            elementType: 'labels',\r\n            stylers: [{ visibility: 'off' }]\r\n          }\r\n        ]\r\n      });\r\n\r\n      // Add click listener for location selection\r\n      mapInstanceRef.current.addListener('click', (event: google.maps.MapMouseEvent) => {\r\n        // Close any open info windows when clicking on empty map area\r\n        if (infoWindowRef.current) {\r\n          infoWindowRef.current.close();\r\n        }\r\n        const lat = event.latLng?.lat() ?? 0;\r\n        const lng = event.latLng?.lng() ?? 0;\r\n        \r\n        // Remove existing marker and info window\r\n        if (markerRef.current) {\r\n          markerRef.current.setMap(null);\r\n        }\r\n        if (infoWindowRef.current) {\r\n          infoWindowRef.current.close();\r\n        }\r\n\r\n        // Reverse geocode to get address first\r\n        const geocoder = new window.google.maps.Geocoder();\r\n        geocoder.geocode({ location: { lat, lng } }, (results: google.maps.GeocoderResult[] | null, status: google.maps.GeocoderStatus) => {\r\n          if (status === 'OK' && results && results[0]) {\r\n            const address = results[0].formatted_address;\r\n            const placeName = results[0].address_components?.[0]?.long_name || 'Selected Location';\r\n\r\n            // Add new marker with Google Maps style - DRAGGABLE\r\n            markerRef.current = new window.google.maps.Marker({\r\n              position: { lat, lng },\r\n              map: mapInstanceRef.current,\r\n              title: address,\r\n              draggable: true, // Make marker draggable\r\n              icon: {\r\n                path: 'M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z',\r\n                fillColor: '#EA4335',\r\n                fillOpacity: 1,\r\n                strokeColor: '#ffffff',\r\n                strokeWeight: 2,\r\n                scale: 1.5,\r\n                anchor: new window.google.maps.Point(12, 24)\r\n              }\r\n            });\r\n\r\n            // Create info window with Google Maps style content\r\n            infoWindowRef.current = new window.google.maps.InfoWindow({\r\n              content: `\r\n                <div style=\"padding: 12px; min-width: 200px; font-family: Roboto, Arial, sans-serif;\">\r\n                  <div style=\"font-weight: 500; font-size: 16px; color: #202124; margin-bottom: 4px;\">\r\n                    ${placeName}\r\n                  </div>\r\n                  <div style=\"font-size: 14px; color: #5f6368; line-height: 1.4; margin-bottom: 8px;\">\r\n                    ${address}\r\n                  </div>\r\n                  <div style=\"font-size: 12px; color: #70757a; display: flex; justify-content: space-between;\">\r\n                    <span>Lat: ${lat.toFixed(6)}</span>\r\n                    <span>Lng: ${lng.toFixed(6)}</span>\r\n                  </div>\r\n                </div>\r\n              `,\r\n              pixelOffset: new window.google.maps.Size(0, -10)\r\n            });\r\n\r\n            // Open info window immediately\r\n            if (infoWindowRef.current && mapInstanceRef.current && markerRef.current) {\r\n              infoWindowRef.current.open(mapInstanceRef.current, markerRef.current);\r\n            }\r\n\r\n            // Add click listener to marker to reopen info window\r\n            markerRef.current.addListener('click', () => {\r\n              if (infoWindowRef.current && mapInstanceRef.current && markerRef.current) {\r\n                infoWindowRef.current.open(mapInstanceRef.current, markerRef.current);\r\n              }\r\n            });\r\n\r\n            // Add drag listener to update location when marker is dragged\r\n            markerRef.current.addListener('dragend', (event: google.maps.MapMouseEvent) => {\r\n              const newLat = event.latLng?.lat() ?? 0;\r\n              const newLng = event.latLng?.lng() ?? 0;\r\n\r\n              // Update info window content with new coordinates\r\n              const geocoder = new window.google.maps.Geocoder();\r\n              geocoder.geocode({ location: { lat: newLat, lng: newLng } }, (results: google.maps.GeocoderResult[] | null, status: google.maps.GeocoderStatus) => {\r\n                if (status === 'OK' && results && results[0]) {\r\n                  const newAddress = results[0].formatted_address;\r\n                  const newPlaceName = results[0].address_components?.[0]?.long_name || 'Dragged Location';\r\n\r\n                  // Update info window content\r\n                  if (infoWindowRef.current) {\r\n                    infoWindowRef.current.setContent(`\r\n                    <div style=\"padding: 12px; min-width: 200px; font-family: Roboto, Arial, sans-serif;\">\r\n                      <div style=\"font-weight: 500; font-size: 16px; color: #202124; margin-bottom: 4px;\">\r\n                        ${newPlaceName}\r\n                      </div>\r\n                      <div style=\"font-size: 13px; color: #1a73e8; margin-bottom: 8px; font-weight: 500;\">\r\n                        📍 Dragged Location\r\n                      </div>\r\n                      <div style=\"font-size: 14px; color: #5f6368; line-height: 1.4; margin-bottom: 8px;\">\r\n                        ${newAddress}\r\n                      </div>\r\n                      <div style=\"font-size: 12px; color: #70757a; display: flex; justify-content: space-between;\">\r\n                        <span>Lat: ${newLat.toFixed(6)}</span>\r\n                        <span>Lng: ${newLng.toFixed(6)}</span>\r\n                      </div>\r\n                    </div>\r\n                  `);\r\n                  }\r\n\r\n                  // Update marker title\r\n                  if (markerRef.current) {\r\n                    markerRef.current.setTitle(newAddress);\r\n                  }\r\n\r\n                  // Update state\r\n                  setSelectedLocation(newAddress);\r\n                  onLocationSelect?.({ lat: newLat, lng: newLng, address: newAddress });\r\n                } else {\r\n                  // Fallback if geocoding fails\r\n                  const fallbackAddress = `Dragged to ${newLat.toFixed(6)}, ${newLng.toFixed(6)}`;\r\n\r\n                  if (infoWindowRef.current) {\r\n                    infoWindowRef.current.setContent(`\r\n                    <div style=\"padding: 12px; min-width: 200px; font-family: Roboto, Arial, sans-serif;\">\r\n                      <div style=\"font-weight: 500; font-size: 16px; color: #202124; margin-bottom: 4px;\">\r\n                        Dragged Location\r\n                      </div>\r\n                      <div style=\"font-size: 13px; color: #1a73e8; margin-bottom: 8px; font-weight: 500;\">\r\n                        📍 Custom Position\r\n                      </div>\r\n                      <div style=\"font-size: 12px; color: #70757a; display: flex; justify-content: space-between;\">\r\n                        <span>Lat: ${newLat.toFixed(6)}</span>\r\n                        <span>Lng: ${newLng.toFixed(6)}</span>\r\n                      </div>\r\n                    </div>\r\n                  `);\r\n                  }\r\n\r\n                  if (markerRef.current) {\r\n                    markerRef.current.setTitle(fallbackAddress);\r\n                  }\r\n                  setSelectedLocation(fallbackAddress);\r\n                  onLocationSelect?.({ lat: newLat, lng: newLng, address: fallbackAddress });\r\n                }\r\n              });\r\n            });\r\n\r\n            setSelectedLocation(address);\r\n            onLocationSelect?.({ lat, lng, address });\r\n          } else {\r\n            // Fallback if geocoding fails\r\n            const fallbackAddress = `Location at ${lat.toFixed(6)}, ${lng.toFixed(6)}`;\r\n\r\n            markerRef.current = new window.google.maps.Marker({\r\n              position: { lat, lng },\r\n              map: mapInstanceRef.current,\r\n              title: fallbackAddress,\r\n              draggable: true, // Make fallback marker draggable too\r\n              icon: {\r\n                path: 'M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z',\r\n                fillColor: '#EA4335',\r\n                fillOpacity: 1,\r\n                strokeColor: '#ffffff',\r\n                strokeWeight: 2,\r\n                scale: 1.5,\r\n                anchor: new window.google.maps.Point(12, 24)\r\n              }\r\n            });\r\n\r\n            infoWindowRef.current = new window.google.maps.InfoWindow({\r\n              content: `\r\n                <div style=\"padding: 12px; min-width: 200px; font-family: Roboto, Arial, sans-serif;\">\r\n                  <div style=\"font-weight: 500; font-size: 16px; color: #202124; margin-bottom: 4px;\">\r\n                    Selected Location\r\n                  </div>\r\n                  <div style=\"font-size: 12px; color: #70757a; display: flex; justify-content: space-between;\">\r\n                    <span>Lat: ${lat.toFixed(6)}</span>\r\n                    <span>Lng: ${lng.toFixed(6)}</span>\r\n                  </div>\r\n                </div>\r\n              `,\r\n              pixelOffset: new window.google.maps.Size(0, -10)\r\n            });\r\n\r\n            if (infoWindowRef.current && mapInstanceRef.current && markerRef.current) {\r\n              infoWindowRef.current.open(mapInstanceRef.current, markerRef.current);\r\n            }\r\n\r\n            markerRef.current.addListener('click', () => {\r\n              if (infoWindowRef.current && mapInstanceRef.current && markerRef.current) {\r\n                infoWindowRef.current.open(mapInstanceRef.current, markerRef.current);\r\n              }\r\n            });\r\n\r\n            // Add drag listener for fallback marker\r\n            markerRef.current.addListener('dragend', (event: google.maps.MapMouseEvent) => {\r\n              const newLat = event.latLng?.lat() ?? 0;\r\n              const newLng = event.latLng?.lng() ?? 0;\r\n\r\n              // Try to get address for new position\r\n              const geocoder = new window.google.maps.Geocoder();\r\n              geocoder.geocode({ location: { lat: newLat, lng: newLng } }, (results: google.maps.GeocoderResult[] | null, status: google.maps.GeocoderStatus) => {\r\n                if (status === 'OK' && results && results[0]) {\r\n                  const newAddress = results[0].formatted_address;\r\n                  const newPlaceName = results[0].address_components?.[0]?.long_name || 'Dragged Location';\r\n\r\n                  if (infoWindowRef.current) {\r\n                    infoWindowRef.current.setContent(`\r\n                    <div style=\"padding: 12px; min-width: 200px; font-family: Roboto, Arial, sans-serif;\">\r\n                      <div style=\"font-weight: 500; font-size: 16px; color: #202124; margin-bottom: 4px;\">\r\n                        ${newPlaceName}\r\n                      </div>\r\n                      <div style=\"font-size: 13px; color: #1a73e8; margin-bottom: 8px; font-weight: 500;\">\r\n                        📍 Dragged Location\r\n                      </div>\r\n                      <div style=\"font-size: 14px; color: #5f6368; line-height: 1.4; margin-bottom: 8px;\">\r\n                        ${newAddress}\r\n                      </div>\r\n                      <div style=\"font-size: 12px; color: #70757a; display: flex; justify-content: space-between;\">\r\n                        <span>Lat: ${newLat.toFixed(6)}</span>\r\n                        <span>Lng: ${newLng.toFixed(6)}</span>\r\n                      </div>\r\n                    </div>\r\n                  `);\r\n                  }\r\n\r\n                  if (markerRef.current) {\r\n                    markerRef.current.setTitle(newAddress);\r\n                  }\r\n                  setSelectedLocation(newAddress);\r\n                  onLocationSelect?.({ lat: newLat, lng: newLng, address: newAddress });\r\n                } else {\r\n                  const newFallbackAddress = `Dragged to ${newLat.toFixed(6)}, ${newLng.toFixed(6)}`;\r\n\r\n                  if (infoWindowRef.current) {\r\n                    infoWindowRef.current.setContent(`\r\n                    <div style=\"padding: 12px; min-width: 200px; font-family: Roboto, Arial, sans-serif;\">\r\n                      <div style=\"font-weight: 500; font-size: 16px; color: #202124; margin-bottom: 4px;\">\r\n                        Dragged Location\r\n                      </div>\r\n                      <div style=\"font-size: 13px; color: #1a73e8; margin-bottom: 8px; font-weight: 500;\">\r\n                        📍 Custom Position\r\n                      </div>\r\n                      <div style=\"font-size: 12px; color: #70757a; display: flex; justify-content: space-between;\">\r\n                        <span>Lat: ${newLat.toFixed(6)}</span>\r\n                        <span>Lng: ${newLng.toFixed(6)}</span>\r\n                      </div>\r\n                    </div>\r\n                  `);\r\n                  }\r\n\r\n                  if (markerRef.current) {\r\n                    markerRef.current.setTitle(newFallbackAddress);\r\n                  }\r\n                  setSelectedLocation(newFallbackAddress);\r\n                  onLocationSelect?.({ lat: newLat, lng: newLng, address: newFallbackAddress });\r\n                }\r\n              });\r\n            });\r\n\r\n            setSelectedLocation(fallbackAddress);\r\n            onLocationSelect?.({ lat, lng, address: fallbackAddress });\r\n          }\r\n        });\r\n      });\r\n\r\n    } catch (err) {\r\n      setError('Failed to initialize map');\r\n      console.error('Map initialization error:', err);\r\n    }\r\n  }, [center, zoom, onLocationSelect]);\r\n\r\n  // Initialize autocomplete\r\n  const initializeAutocomplete = useCallback(() => {\r\n    if (!searchInputRef.current || !window.google || autocompleteRef.current) return;\r\n\r\n    try {\r\n      autocompleteRef.current = new window.google.maps.places.Autocomplete(\r\n        searchInputRef.current,\r\n        {\r\n          componentRestrictions: { country: 'PH' },\r\n          fields: ['place_id', 'geometry', 'name', 'formatted_address']\r\n        }\r\n      );\r\n\r\n      autocompleteRef.current.addListener('place_changed', () => {\r\n        if (!autocompleteRef.current) return;\r\n        const place = autocompleteRef.current.getPlace();\r\n        \r\n        if (!place.geometry || !place.geometry.location) {\r\n          return;\r\n        }\r\n\r\n        const lat = place.geometry.location.lat();\r\n        const lng = place.geometry.location.lng();\r\n        const address = place.formatted_address || place.name;\r\n        const placeName = place.name || 'Selected Location';\r\n        const placeTypes = place.types || [];\r\n\r\n        // Update map center with smooth animation\r\n        mapInstanceRef.current?.panTo({ lat, lng });\r\n        mapInstanceRef.current?.setZoom(17);\r\n\r\n        // Remove existing marker and info window\r\n        if (markerRef.current) {\r\n          markerRef.current.setMap(null);\r\n        }\r\n        if (infoWindowRef.current) {\r\n          infoWindowRef.current.close();\r\n        }\r\n\r\n        // Add new marker with Google Maps style pin - DRAGGABLE\r\n        markerRef.current = new window.google.maps.Marker({\r\n          position: { lat, lng },\r\n          map: mapInstanceRef.current,\r\n          title: address,\r\n          draggable: true, // Make search result marker draggable\r\n          icon: {\r\n            path: 'M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z',\r\n            fillColor: '#EA4335',\r\n            fillOpacity: 1,\r\n            strokeColor: '#ffffff',\r\n            strokeWeight: 2,\r\n            scale: 1.5,\r\n            anchor: new window.google.maps.Point(12, 24)\r\n          }\r\n        });\r\n\r\n        // Determine place type for display\r\n        let placeTypeDisplay = '';\r\n        if (placeTypes.includes('restaurant')) placeTypeDisplay = '🍽️ Restaurant';\r\n        else if (placeTypes.includes('shopping_mall')) placeTypeDisplay = '🏬 Shopping Mall';\r\n        else if (placeTypes.includes('hospital')) placeTypeDisplay = '🏥 Hospital';\r\n        else if (placeTypes.includes('school')) placeTypeDisplay = '🏫 School';\r\n        else if (placeTypes.includes('bank')) placeTypeDisplay = '🏦 Bank';\r\n        else if (placeTypes.includes('gas_station')) placeTypeDisplay = '⛽ Gas Station';\r\n        else if (placeTypes.includes('establishment')) placeTypeDisplay = '📍 Establishment';\r\n        else placeTypeDisplay = '📍 Location';\r\n\r\n        // Create professional info window with Google Maps styling\r\n        infoWindowRef.current = new window.google.maps.InfoWindow({\r\n          content: `\r\n            <div style=\"padding: 16px; min-width: 250px; max-width: 300px; font-family: Roboto, Arial, sans-serif;\">\r\n              <div style=\"font-weight: 500; font-size: 18px; color: #202124; margin-bottom: 6px; line-height: 1.3;\">\r\n                ${placeName}\r\n              </div>\r\n              ${placeTypeDisplay ? `\r\n                <div style=\"font-size: 13px; color: #1a73e8; margin-bottom: 8px; font-weight: 500;\">\r\n                  ${placeTypeDisplay}\r\n                </div>\r\n              ` : ''}\r\n              <div style=\"font-size: 14px; color: #5f6368; line-height: 1.4; margin-bottom: 12px;\">\r\n                ${address}\r\n              </div>\r\n              <div style=\"border-top: 1px solid #e8eaed; padding-top: 8px;\">\r\n                <div style=\"font-size: 12px; color: #70757a; display: flex; justify-content: space-between;\">\r\n                  <span>Lat: ${lat.toFixed(6)}</span>\r\n                  <span>Lng: ${lng.toFixed(6)}</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          `,\r\n          pixelOffset: new window.google.maps.Size(0, -10)\r\n        });\r\n\r\n        // Open info window immediately\r\n        if (infoWindowRef.current && mapInstanceRef.current && markerRef.current) {\r\n          infoWindowRef.current.open(mapInstanceRef.current, markerRef.current);\r\n        }\r\n\r\n        // Add click listener to marker to reopen info window\r\n        markerRef.current.addListener('click', () => {\r\n          if (infoWindowRef.current && mapInstanceRef.current && markerRef.current) {\r\n            infoWindowRef.current.open(mapInstanceRef.current, markerRef.current);\r\n          }\r\n        });\r\n\r\n        // Add drag listener for search result marker\r\n        markerRef.current.addListener('dragend', (event: google.maps.MapMouseEvent) => {\r\n          const newLat = event.latLng?.lat() ?? 0;\r\n          const newLng = event.latLng?.lng() ?? 0;\r\n\r\n          // Get new address for dragged position\r\n          const geocoder = new window.google.maps.Geocoder();\r\n          geocoder.geocode({ location: { lat: newLat, lng: newLng } }, (results: google.maps.GeocoderResult[] | null, status: google.maps.GeocoderStatus) => {\r\n            if (status === 'OK' && results && results[0]) {\r\n              const newAddress = results[0].formatted_address;\r\n              const newPlaceName = results[0].address_components?.[0]?.long_name || 'Dragged Location';\r\n              const newPlaceTypes = results[0].types || [];\r\n\r\n              // Determine new place type\r\n              let newPlaceTypeDisplay = '';\r\n              if (newPlaceTypes.includes('restaurant')) newPlaceTypeDisplay = '🍽️ Restaurant';\r\n              else if (newPlaceTypes.includes('shopping_mall')) newPlaceTypeDisplay = '🏬 Shopping Mall';\r\n              else if (newPlaceTypes.includes('hospital')) newPlaceTypeDisplay = '🏥 Hospital';\r\n              else if (newPlaceTypes.includes('school')) newPlaceTypeDisplay = '🏫 School';\r\n              else if (newPlaceTypes.includes('bank')) newPlaceTypeDisplay = '🏦 Bank';\r\n              else if (newPlaceTypes.includes('gas_station')) newPlaceTypeDisplay = '⛽ Gas Station';\r\n              else if (newPlaceTypes.includes('establishment')) newPlaceTypeDisplay = '📍 Establishment';\r\n              else newPlaceTypeDisplay = '📍 Dragged Location';\r\n\r\n              // Update info window content\r\n              if (infoWindowRef.current) {\r\n                infoWindowRef.current.setContent(`\r\n                <div style=\"padding: 16px; min-width: 250px; max-width: 300px; font-family: Roboto, Arial, sans-serif;\">\r\n                  <div style=\"font-weight: 500; font-size: 18px; color: #202124; margin-bottom: 6px; line-height: 1.3;\">\r\n                    ${newPlaceName}\r\n                  </div>\r\n                  <div style=\"font-size: 13px; color: #1a73e8; margin-bottom: 8px; font-weight: 500;\">\r\n                    ${newPlaceTypeDisplay}\r\n                  </div>\r\n                  <div style=\"font-size: 14px; color: #5f6368; line-height: 1.4; margin-bottom: 12px;\">\r\n                    ${newAddress}\r\n                  </div>\r\n                  <div style=\"border-top: 1px solid #e8eaed; padding-top: 8px;\">\r\n                    <div style=\"font-size: 12px; color: #70757a; display: flex; justify-content: space-between;\">\r\n                      <span>Lat: ${newLat.toFixed(6)}</span>\r\n                      <span>Lng: ${newLng.toFixed(6)}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              `);\r\n              }\r\n\r\n              if (markerRef.current) {\r\n                markerRef.current.setTitle(newAddress);\r\n              }\r\n              setSelectedLocation(newAddress);\r\n              onLocationSelect?.({ lat: newLat, lng: newLng, address: newAddress });\r\n            } else {\r\n              // Fallback if geocoding fails\r\n              const fallbackAddress = `Dragged to ${newLat.toFixed(6)}, ${newLng.toFixed(6)}`;\r\n\r\n              if (infoWindowRef.current) {\r\n                infoWindowRef.current.setContent(`\r\n                <div style=\"padding: 16px; min-width: 250px; max-width: 300px; font-family: Roboto, Arial, sans-serif;\">\r\n                  <div style=\"font-weight: 500; font-size: 18px; color: #202124; margin-bottom: 6px; line-height: 1.3;\">\r\n                    Dragged Location\r\n                  </div>\r\n                  <div style=\"font-size: 13px; color: #1a73e8; margin-bottom: 8px; font-weight: 500;\">\r\n                    📍 Custom Position\r\n                  </div>\r\n                  <div style=\"border-top: 1px solid #e8eaed; padding-top: 8px;\">\r\n                    <div style=\"font-size: 12px; color: #70757a; display: flex; justify-content: space-between;\">\r\n                      <span>Lat: ${newLat.toFixed(6)}</span>\r\n                      <span>Lng: ${newLng.toFixed(6)}</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              `);\r\n              }\r\n\r\n              if (markerRef.current) {\r\n                markerRef.current.setTitle(fallbackAddress);\r\n              }\r\n              setSelectedLocation(fallbackAddress);\r\n              onLocationSelect?.({ lat: newLat, lng: newLng, address: fallbackAddress });\r\n            }\r\n          });\r\n        });\r\n\r\n        setSelectedLocation(address || 'Selected Location');\r\n        onLocationSelect?.({ lat, lng, address: address || 'Selected Location' });\r\n      });\r\n\r\n    } catch (err) {\r\n      console.error('Autocomplete initialization error:', err);\r\n    }\r\n  }, [onLocationSelect]);\r\n\r\n  // Load Google Maps on mount\r\n  useEffect(() => {\r\n    loadMapsAPI();\r\n  }, [loadMapsAPI]);\r\n\r\n  // Initialize map when loaded\r\n  useEffect(() => {\r\n    if (isLoaded) {\r\n      initializeMap();\r\n      initializeAutocomplete();\r\n    }\r\n  }, [isLoaded, initializeMap, initializeAutocomplete]);\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className={`bg-gray-100 rounded-lg flex items-center justify-center ${className}`} style={{ height }}>\r\n        <div className=\"text-center\">\r\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-2\"></div>\r\n          <p className=\"text-sm text-gray-600\">Loading Map...</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className={`bg-gray-100 rounded-lg flex items-center justify-center ${className}`} style={{ height }}>\r\n        <div className=\"text-center text-gray-600\">\r\n          <p className=\"text-sm font-medium\">Map Error</p>\r\n          <p className=\"text-xs\">{error}</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      {/* Search Input */}\r\n      <div className=\"absolute top-4 left-4 right-4 z-10\">\r\n        <input\r\n          ref={searchInputRef}\r\n          type=\"text\"\r\n          placeholder=\"Search any location in Philippines...\"\r\n          className=\"w-full px-4 py-3 bg-white border border-gray-300 rounded-lg shadow-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\r\n        />\r\n      </div>\r\n\r\n      {/* Map Container */}\r\n      <div ref={mapRef} className=\"w-full rounded-lg\" style={{ height }} />\r\n\r\n      {/* Selected Location Display */}\r\n      {selectedLocation && (\r\n        <div className=\"absolute bottom-4 left-4 right-4 bg-white p-3 rounded-lg shadow-lg border\">\r\n          <p className=\"text-sm font-medium text-gray-900\">Selected Location:</p>\r\n          <p className=\"text-xs text-gray-600 mt-1\">{selectedLocation}</p>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAoBe,SAAS,gBAAgB,EACtC,SAAS;IAAE,KAAK;IAAS,KAAK;AAAS,CAAC,EACxC,OAAO,EAAE,EACT,SAAS,OAAO,EAChB,YAAY,EAAE,EACd,gBAAgB,EACP;IACT,MAAM,SAAS,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAkB;IACtC,MAAM,iBAAiB,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAA0B;IACtD,MAAM,YAAY,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAA6B;IACpD,MAAM,gBAAgB,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAiC;IAC5D,MAAM,kBAAkB,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAA0C;IACvE,MAAM,iBAAiB,CAAA,GAAA,oNAAA,CAAA,SAAM,AAAD,EAAoB;IAEhD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,8CAA8C;IAC9C,MAAM,cAAc,CAAA,GAAA,oNAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,MAAM;QACN,uCAAa;;QAIb;QAEA,IAAI;YACF,MAAM,CAAA,GAAA,6IAAA,CAAA,iBAAc,AAAD,EAAE;YACrB,YAAY;YACZ,aAAa;QACf,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,aAAa;YACb,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF,GAAG,EAAE;IAEL,iBAAiB;IACjB,MAAM,gBAAgB,CAAA,GAAA,oNAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,OAAO,MAAM,IAAI,eAAe,OAAO,EAAE;QAEjE,IAAI;YACF,wCAAwC;YACxC,eAAe,OAAO,GAAG,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,OAAO,EAAE;gBAClE;gBACA;gBACA,WAAW;gBACX,kBAAkB;gBAClB,aAAa;gBACb,gBAAgB;gBAChB,cAAc;gBACd,mBAAmB;gBACnB,eAAe;gBACf,mBAAmB;gBACnB,iBAAiB;gBACjB,QAAQ;oBACN;wBACE,aAAa;wBACb,aAAa;wBACb,SAAS;4BAAC;gCAAE,YAAY;4BAAM;yBAAE;oBAClC;iBACD;YACH;YAEA,4CAA4C;YAC5C,eAAe,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC;gBAC3C,8DAA8D;gBAC9D,IAAI,cAAc,OAAO,EAAE;oBACzB,cAAc,OAAO,CAAC,KAAK;gBAC7B;gBACA,MAAM,MAAM,MAAM,MAAM,EAAE,SAAS;gBACnC,MAAM,MAAM,MAAM,MAAM,EAAE,SAAS;gBAEnC,yCAAyC;gBACzC,IAAI,UAAU,OAAO,EAAE;oBACrB,UAAU,OAAO,CAAC,MAAM,CAAC;gBAC3B;gBACA,IAAI,cAAc,OAAO,EAAE;oBACzB,cAAc,OAAO,CAAC,KAAK;gBAC7B;gBAEA,uCAAuC;gBACvC,MAAM,WAAW,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ;gBAChD,SAAS,OAAO,CAAC;oBAAE,UAAU;wBAAE;wBAAK;oBAAI;gBAAE,GAAG,CAAC,SAA8C;oBAC1F,IAAI,WAAW,QAAQ,WAAW,OAAO,CAAC,EAAE,EAAE;wBAC5C,MAAM,UAAU,OAAO,CAAC,EAAE,CAAC,iBAAiB;wBAC5C,MAAM,YAAY,OAAO,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,EAAE,EAAE,aAAa;wBAEnE,oDAAoD;wBACpD,UAAU,OAAO,GAAG,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;4BAChD,UAAU;gCAAE;gCAAK;4BAAI;4BACrB,KAAK,eAAe,OAAO;4BAC3B,OAAO;4BACP,WAAW;4BACX,MAAM;gCACJ,MAAM;gCACN,WAAW;gCACX,aAAa;gCACb,aAAa;gCACb,cAAc;gCACd,OAAO;gCACP,QAAQ,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;4BAC3C;wBACF;wBAEA,oDAAoD;wBACpD,cAAc,OAAO,GAAG,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;4BACxD,SAAS,CAAC;;;oBAGJ,EAAE,UAAU;;;oBAGZ,EAAE,QAAQ;;;+BAGC,EAAE,IAAI,OAAO,CAAC,GAAG;+BACjB,EAAE,IAAI,OAAO,CAAC,GAAG;;;cAGlC,CAAC;4BACD,aAAa,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;wBAC/C;wBAEA,+BAA+B;wBAC/B,IAAI,cAAc,OAAO,IAAI,eAAe,OAAO,IAAI,UAAU,OAAO,EAAE;4BACxE,cAAc,OAAO,CAAC,IAAI,CAAC,eAAe,OAAO,EAAE,UAAU,OAAO;wBACtE;wBAEA,qDAAqD;wBACrD,UAAU,OAAO,CAAC,WAAW,CAAC,SAAS;4BACrC,IAAI,cAAc,OAAO,IAAI,eAAe,OAAO,IAAI,UAAU,OAAO,EAAE;gCACxE,cAAc,OAAO,CAAC,IAAI,CAAC,eAAe,OAAO,EAAE,UAAU,OAAO;4BACtE;wBACF;wBAEA,8DAA8D;wBAC9D,UAAU,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC;4BACxC,MAAM,SAAS,MAAM,MAAM,EAAE,SAAS;4BACtC,MAAM,SAAS,MAAM,MAAM,EAAE,SAAS;4BAEtC,kDAAkD;4BAClD,MAAM,WAAW,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ;4BAChD,SAAS,OAAO,CAAC;gCAAE,UAAU;oCAAE,KAAK;oCAAQ,KAAK;gCAAO;4BAAE,GAAG,CAAC,SAA8C;gCAC1G,IAAI,WAAW,QAAQ,WAAW,OAAO,CAAC,EAAE,EAAE;oCAC5C,MAAM,aAAa,OAAO,CAAC,EAAE,CAAC,iBAAiB;oCAC/C,MAAM,eAAe,OAAO,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,EAAE,EAAE,aAAa;oCAEtE,6BAA6B;oCAC7B,IAAI,cAAc,OAAO,EAAE;wCACzB,cAAc,OAAO,CAAC,UAAU,CAAC,CAAC;;;wBAG9B,EAAE,aAAa;;;;;;wBAMf,EAAE,WAAW;;;mCAGF,EAAE,OAAO,OAAO,CAAC,GAAG;mCACpB,EAAE,OAAO,OAAO,CAAC,GAAG;;;kBAGrC,CAAC;oCACD;oCAEA,sBAAsB;oCACtB,IAAI,UAAU,OAAO,EAAE;wCACrB,UAAU,OAAO,CAAC,QAAQ,CAAC;oCAC7B;oCAEA,eAAe;oCACf,oBAAoB;oCACpB,mBAAmB;wCAAE,KAAK;wCAAQ,KAAK;wCAAQ,SAAS;oCAAW;gCACrE,OAAO;oCACL,8BAA8B;oCAC9B,MAAM,kBAAkB,CAAC,WAAW,EAAE,OAAO,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,OAAO,CAAC,IAAI;oCAE/E,IAAI,cAAc,OAAO,EAAE;wCACzB,cAAc,OAAO,CAAC,UAAU,CAAC,CAAC;;;;;;;;;mCASnB,EAAE,OAAO,OAAO,CAAC,GAAG;mCACpB,EAAE,OAAO,OAAO,CAAC,GAAG;;;kBAGrC,CAAC;oCACD;oCAEA,IAAI,UAAU,OAAO,EAAE;wCACrB,UAAU,OAAO,CAAC,QAAQ,CAAC;oCAC7B;oCACA,oBAAoB;oCACpB,mBAAmB;wCAAE,KAAK;wCAAQ,KAAK;wCAAQ,SAAS;oCAAgB;gCAC1E;4BACF;wBACF;wBAEA,oBAAoB;wBACpB,mBAAmB;4BAAE;4BAAK;4BAAK;wBAAQ;oBACzC,OAAO;wBACL,8BAA8B;wBAC9B,MAAM,kBAAkB,CAAC,YAAY,EAAE,IAAI,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,OAAO,CAAC,IAAI;wBAE1E,UAAU,OAAO,GAAG,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;4BAChD,UAAU;gCAAE;gCAAK;4BAAI;4BACrB,KAAK,eAAe,OAAO;4BAC3B,OAAO;4BACP,WAAW;4BACX,MAAM;gCACJ,MAAM;gCACN,WAAW;gCACX,aAAa;gCACb,aAAa;gCACb,cAAc;gCACd,OAAO;gCACP,QAAQ,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;4BAC3C;wBACF;wBAEA,cAAc,OAAO,GAAG,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;4BACxD,SAAS,CAAC;;;;;;+BAMO,EAAE,IAAI,OAAO,CAAC,GAAG;+BACjB,EAAE,IAAI,OAAO,CAAC,GAAG;;;cAGlC,CAAC;4BACD,aAAa,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;wBAC/C;wBAEA,IAAI,cAAc,OAAO,IAAI,eAAe,OAAO,IAAI,UAAU,OAAO,EAAE;4BACxE,cAAc,OAAO,CAAC,IAAI,CAAC,eAAe,OAAO,EAAE,UAAU,OAAO;wBACtE;wBAEA,UAAU,OAAO,CAAC,WAAW,CAAC,SAAS;4BACrC,IAAI,cAAc,OAAO,IAAI,eAAe,OAAO,IAAI,UAAU,OAAO,EAAE;gCACxE,cAAc,OAAO,CAAC,IAAI,CAAC,eAAe,OAAO,EAAE,UAAU,OAAO;4BACtE;wBACF;wBAEA,wCAAwC;wBACxC,UAAU,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC;4BACxC,MAAM,SAAS,MAAM,MAAM,EAAE,SAAS;4BACtC,MAAM,SAAS,MAAM,MAAM,EAAE,SAAS;4BAEtC,sCAAsC;4BACtC,MAAM,WAAW,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ;4BAChD,SAAS,OAAO,CAAC;gCAAE,UAAU;oCAAE,KAAK;oCAAQ,KAAK;gCAAO;4BAAE,GAAG,CAAC,SAA8C;gCAC1G,IAAI,WAAW,QAAQ,WAAW,OAAO,CAAC,EAAE,EAAE;oCAC5C,MAAM,aAAa,OAAO,CAAC,EAAE,CAAC,iBAAiB;oCAC/C,MAAM,eAAe,OAAO,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,EAAE,EAAE,aAAa;oCAEtE,IAAI,cAAc,OAAO,EAAE;wCACzB,cAAc,OAAO,CAAC,UAAU,CAAC,CAAC;;;wBAG9B,EAAE,aAAa;;;;;;wBAMf,EAAE,WAAW;;;mCAGF,EAAE,OAAO,OAAO,CAAC,GAAG;mCACpB,EAAE,OAAO,OAAO,CAAC,GAAG;;;kBAGrC,CAAC;oCACD;oCAEA,IAAI,UAAU,OAAO,EAAE;wCACrB,UAAU,OAAO,CAAC,QAAQ,CAAC;oCAC7B;oCACA,oBAAoB;oCACpB,mBAAmB;wCAAE,KAAK;wCAAQ,KAAK;wCAAQ,SAAS;oCAAW;gCACrE,OAAO;oCACL,MAAM,qBAAqB,CAAC,WAAW,EAAE,OAAO,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,OAAO,CAAC,IAAI;oCAElF,IAAI,cAAc,OAAO,EAAE;wCACzB,cAAc,OAAO,CAAC,UAAU,CAAC,CAAC;;;;;;;;;mCASnB,EAAE,OAAO,OAAO,CAAC,GAAG;mCACpB,EAAE,OAAO,OAAO,CAAC,GAAG;;;kBAGrC,CAAC;oCACD;oCAEA,IAAI,UAAU,OAAO,EAAE;wCACrB,UAAU,OAAO,CAAC,QAAQ,CAAC;oCAC7B;oCACA,oBAAoB;oCACpB,mBAAmB;wCAAE,KAAK;wCAAQ,KAAK;wCAAQ,SAAS;oCAAmB;gCAC7E;4BACF;wBACF;wBAEA,oBAAoB;wBACpB,mBAAmB;4BAAE;4BAAK;4BAAK,SAAS;wBAAgB;oBAC1D;gBACF;YACF;QAEF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF,GAAG;QAAC;QAAQ;QAAM;KAAiB;IAEnC,0BAA0B;IAC1B,MAAM,yBAAyB,CAAA,GAAA,oNAAA,CAAA,cAAW,AAAD,EAAE;QACzC,IAAI,CAAC,eAAe,OAAO,IAAI,CAAC,OAAO,MAAM,IAAI,gBAAgB,OAAO,EAAE;QAE1E,IAAI;YACF,gBAAgB,OAAO,GAAG,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAClE,eAAe,OAAO,EACtB;gBACE,uBAAuB;oBAAE,SAAS;gBAAK;gBACvC,QAAQ;oBAAC;oBAAY;oBAAY;oBAAQ;iBAAoB;YAC/D;YAGF,gBAAgB,OAAO,CAAC,WAAW,CAAC,iBAAiB;gBACnD,IAAI,CAAC,gBAAgB,OAAO,EAAE;gBAC9B,MAAM,QAAQ,gBAAgB,OAAO,CAAC,QAAQ;gBAE9C,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,MAAM,QAAQ,CAAC,QAAQ,EAAE;oBAC/C;gBACF;gBAEA,MAAM,MAAM,MAAM,QAAQ,CAAC,QAAQ,CAAC,GAAG;gBACvC,MAAM,MAAM,MAAM,QAAQ,CAAC,QAAQ,CAAC,GAAG;gBACvC,MAAM,UAAU,MAAM,iBAAiB,IAAI,MAAM,IAAI;gBACrD,MAAM,YAAY,MAAM,IAAI,IAAI;gBAChC,MAAM,aAAa,MAAM,KAAK,IAAI,EAAE;gBAEpC,0CAA0C;gBAC1C,eAAe,OAAO,EAAE,MAAM;oBAAE;oBAAK;gBAAI;gBACzC,eAAe,OAAO,EAAE,QAAQ;gBAEhC,yCAAyC;gBACzC,IAAI,UAAU,OAAO,EAAE;oBACrB,UAAU,OAAO,CAAC,MAAM,CAAC;gBAC3B;gBACA,IAAI,cAAc,OAAO,EAAE;oBACzB,cAAc,OAAO,CAAC,KAAK;gBAC7B;gBAEA,wDAAwD;gBACxD,UAAU,OAAO,GAAG,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;oBAChD,UAAU;wBAAE;wBAAK;oBAAI;oBACrB,KAAK,eAAe,OAAO;oBAC3B,OAAO;oBACP,WAAW;oBACX,MAAM;wBACJ,MAAM;wBACN,WAAW;wBACX,aAAa;wBACb,aAAa;wBACb,cAAc;wBACd,OAAO;wBACP,QAAQ,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;oBAC3C;gBACF;gBAEA,mCAAmC;gBACnC,IAAI,mBAAmB;gBACvB,IAAI,WAAW,QAAQ,CAAC,eAAe,mBAAmB;qBACrD,IAAI,WAAW,QAAQ,CAAC,kBAAkB,mBAAmB;qBAC7D,IAAI,WAAW,QAAQ,CAAC,aAAa,mBAAmB;qBACxD,IAAI,WAAW,QAAQ,CAAC,WAAW,mBAAmB;qBACtD,IAAI,WAAW,QAAQ,CAAC,SAAS,mBAAmB;qBACpD,IAAI,WAAW,QAAQ,CAAC,gBAAgB,mBAAmB;qBAC3D,IAAI,WAAW,QAAQ,CAAC,kBAAkB,mBAAmB;qBAC7D,mBAAmB;gBAExB,2DAA2D;gBAC3D,cAAc,OAAO,GAAG,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxD,SAAS,CAAC;;;gBAGJ,EAAE,UAAU;;cAEd,EAAE,mBAAmB,CAAC;;kBAElB,EAAE,iBAAiB;;cAEvB,CAAC,GAAG,GAAG;;gBAEL,EAAE,QAAQ;;;;6BAIG,EAAE,IAAI,OAAO,CAAC,GAAG;6BACjB,EAAE,IAAI,OAAO,CAAC,GAAG;;;;UAIpC,CAAC;oBACD,aAAa,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;gBAC/C;gBAEA,+BAA+B;gBAC/B,IAAI,cAAc,OAAO,IAAI,eAAe,OAAO,IAAI,UAAU,OAAO,EAAE;oBACxE,cAAc,OAAO,CAAC,IAAI,CAAC,eAAe,OAAO,EAAE,UAAU,OAAO;gBACtE;gBAEA,qDAAqD;gBACrD,UAAU,OAAO,CAAC,WAAW,CAAC,SAAS;oBACrC,IAAI,cAAc,OAAO,IAAI,eAAe,OAAO,IAAI,UAAU,OAAO,EAAE;wBACxE,cAAc,OAAO,CAAC,IAAI,CAAC,eAAe,OAAO,EAAE,UAAU,OAAO;oBACtE;gBACF;gBAEA,6CAA6C;gBAC7C,UAAU,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC;oBACxC,MAAM,SAAS,MAAM,MAAM,EAAE,SAAS;oBACtC,MAAM,SAAS,MAAM,MAAM,EAAE,SAAS;oBAEtC,uCAAuC;oBACvC,MAAM,WAAW,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ;oBAChD,SAAS,OAAO,CAAC;wBAAE,UAAU;4BAAE,KAAK;4BAAQ,KAAK;wBAAO;oBAAE,GAAG,CAAC,SAA8C;wBAC1G,IAAI,WAAW,QAAQ,WAAW,OAAO,CAAC,EAAE,EAAE;4BAC5C,MAAM,aAAa,OAAO,CAAC,EAAE,CAAC,iBAAiB;4BAC/C,MAAM,eAAe,OAAO,CAAC,EAAE,CAAC,kBAAkB,EAAE,CAAC,EAAE,EAAE,aAAa;4BACtE,MAAM,gBAAgB,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE;4BAE5C,2BAA2B;4BAC3B,IAAI,sBAAsB;4BAC1B,IAAI,cAAc,QAAQ,CAAC,eAAe,sBAAsB;iCAC3D,IAAI,cAAc,QAAQ,CAAC,kBAAkB,sBAAsB;iCACnE,IAAI,cAAc,QAAQ,CAAC,aAAa,sBAAsB;iCAC9D,IAAI,cAAc,QAAQ,CAAC,WAAW,sBAAsB;iCAC5D,IAAI,cAAc,QAAQ,CAAC,SAAS,sBAAsB;iCAC1D,IAAI,cAAc,QAAQ,CAAC,gBAAgB,sBAAsB;iCACjE,IAAI,cAAc,QAAQ,CAAC,kBAAkB,sBAAsB;iCACnE,sBAAsB;4BAE3B,6BAA6B;4BAC7B,IAAI,cAAc,OAAO,EAAE;gCACzB,cAAc,OAAO,CAAC,UAAU,CAAC,CAAC;;;oBAG9B,EAAE,aAAa;;;oBAGf,EAAE,oBAAoB;;;oBAGtB,EAAE,WAAW;;;;iCAIA,EAAE,OAAO,OAAO,CAAC,GAAG;iCACpB,EAAE,OAAO,OAAO,CAAC,GAAG;;;;cAIvC,CAAC;4BACD;4BAEA,IAAI,UAAU,OAAO,EAAE;gCACrB,UAAU,OAAO,CAAC,QAAQ,CAAC;4BAC7B;4BACA,oBAAoB;4BACpB,mBAAmB;gCAAE,KAAK;gCAAQ,KAAK;gCAAQ,SAAS;4BAAW;wBACrE,OAAO;4BACL,8BAA8B;4BAC9B,MAAM,kBAAkB,CAAC,WAAW,EAAE,OAAO,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,OAAO,CAAC,IAAI;4BAE/E,IAAI,cAAc,OAAO,EAAE;gCACzB,cAAc,OAAO,CAAC,UAAU,CAAC,CAAC;;;;;;;;;;iCAUjB,EAAE,OAAO,OAAO,CAAC,GAAG;iCACpB,EAAE,OAAO,OAAO,CAAC,GAAG;;;;cAIvC,CAAC;4BACD;4BAEA,IAAI,UAAU,OAAO,EAAE;gCACrB,UAAU,OAAO,CAAC,QAAQ,CAAC;4BAC7B;4BACA,oBAAoB;4BACpB,mBAAmB;gCAAE,KAAK;gCAAQ,KAAK;gCAAQ,SAAS;4BAAgB;wBAC1E;oBACF;gBACF;gBAEA,oBAAoB,WAAW;gBAC/B,mBAAmB;oBAAE;oBAAK;oBAAK,SAAS,WAAW;gBAAoB;YACzE;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,sCAAsC;QACtD;IACF,GAAG;QAAC;KAAiB;IAErB,4BAA4B;IAC5B,CAAA,GAAA,oNAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAY;IAEhB,6BAA6B;IAC7B,CAAA,GAAA,oNAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ;YACA;QACF;IACF,GAAG;QAAC;QAAU;QAAe;KAAuB;IAEpD,IAAI,WAAW;QACb,qBACE,6PAAC;YAAI,WAAW,CAAC,wDAAwD,EAAE,WAAW;YAAE,OAAO;gBAAE;YAAO;sBACtG,cAAA,6PAAC;gBAAI,WAAU;;kCACb,6PAAC;wBAAI,WAAU;;;;;;kCACf,6PAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,IAAI,OAAO;QACT,qBACE,6PAAC;YAAI,WAAW,CAAC,wDAAwD,EAAE,WAAW;YAAE,OAAO;gBAAE;YAAO;sBACtG,cAAA,6PAAC;gBAAI,WAAU;;kCACb,6PAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,6PAAC;wBAAE,WAAU;kCAAW;;;;;;;;;;;;;;;;;IAIhC;IAEA,qBACE,6PAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BAErC,6PAAC;gBAAI,WAAU;0BACb,cAAA,6PAAC;oBACC,KAAK;oBACL,MAAK;oBACL,aAAY;oBACZ,WAAU;;;;;;;;;;;0BAKd,6PAAC;gBAAI,KAAK;gBAAQ,WAAU;gBAAoB,OAAO;oBAAE;gBAAO;;;;;;YAG/D,kCACC,6PAAC;gBAAI,WAAU;;kCACb,6PAAC;wBAAE,WAAU;kCAAoC;;;;;;kCACjD,6PAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;;;;;;;AAKrD", "debugId": null}}, {"offset": {"line": 1267, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/home/<USER>"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\nimport RestaurantCard from '@/components/RestaurantCard';\r\nimport { Restaurant, Category } from '@/types';\r\nimport { collection, getDocs, query, orderBy, limit } from 'firebase/firestore';\r\nimport { db } from '@/lib/firebase';\r\nimport { transformRestaurantsData } from '@/lib/transformers/restaurant';\r\nimport ProfessionalMap from '@/components/ProfessionalMap';\r\n\r\nexport default function HomeContent() {\r\n  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);\r\n  const [categories, setCategories] = useState<Category[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [selectedCategory, setSelectedCategory] = useState<string>('');\r\n\r\n  // Professional map state\r\n  const [showMap, setShowMap] = useState(false);\r\n  const [selectedLocation, setSelectedLocation] = useState<{lat: number; lng: number; address: string} | null>(null);\r\n\r\n  useEffect(() => {\r\n    const loadData = async () => {\r\n      try {\r\n        // Load categories from Firestore\r\n        const categoriesRef = collection(db, 'categories');\r\n        const categoriesQuery = query(categoriesRef, orderBy('sortOrder'));\r\n        const categoriesSnapshot = await getDocs(categoriesQuery);\r\n\r\n        const categoriesData = categoriesSnapshot.docs.map(doc => ({\r\n          id: doc.id,\r\n          ...doc.data()\r\n        })) as Category[];\r\n\r\n        setCategories(categoriesData);\r\n\r\n        // Load restaurants from Firestore\r\n        const restaurantsRef = collection(db, 'restaurants');\r\n        const restaurantsQuery = query(restaurantsRef, limit(12));\r\n        const restaurantsSnapshot = await getDocs(restaurantsQuery);\r\n\r\n        // Use centralized transformer for consistency\r\n        const restaurantsData = transformRestaurantsData(restaurantsSnapshot.docs);\r\n\r\n        setRestaurants(restaurantsData);\r\n\r\n      } catch (error) {\r\n        console.error('Error loading data:', error);\r\n        // Set empty arrays if Firestore fails\r\n        setRestaurants([]);\r\n        setCategories([]);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    loadData();\r\n  }, []);\r\n\r\n  const filteredRestaurants = restaurants.filter(restaurant => {\r\n    const matchesSearch = !searchQuery ||\r\n      restaurant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n      restaurant.description.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n      restaurant.cuisine.some(c => c.toLowerCase().includes(searchQuery.toLowerCase()));\r\n\r\n    const matchesCategory = !selectedCategory ||\r\n      restaurant.cuisine.includes(selectedCategory);\r\n\r\n    return matchesSearch && matchesCategory;\r\n  });\r\n\r\n  // Professional map handlers\r\n  const handleLocationSelect = (location: {lat: number; lng: number; address: string}) => {\r\n    setSelectedLocation(location);\r\n    console.log('Location selected:', location);\r\n  };\r\n\r\n  const toggleMapView = () => {\r\n    setShowMap(!showMap);\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      {/* Professional Map Section */}\r\n      <section className=\"py-8 bg-white border border-gray-200 rounded-lg\">\r\n        <div className=\"px-6\">\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <div>\r\n              <h2 className=\"text-2xl font-bold text-gray-900\">Explore Locations</h2>\r\n              <p className=\"text-gray-600 mt-1\">\r\n                {selectedLocation\r\n                  ? `Selected: ${selectedLocation.address}`\r\n                  : 'Search and explore any location in the Philippines'\r\n                }\r\n              </p>\r\n            </div>\r\n            <button\r\n              onClick={toggleMapView}\r\n              className={`px-4 py-2 rounded-lg font-medium transition-colors ${\r\n                showMap\r\n                  ? 'bg-orange-100 text-orange-700 hover:bg-orange-200'\r\n                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\r\n              }`}\r\n            >\r\n              {showMap ? 'Hide Map' : 'Show Map'}\r\n            </button>\r\n          </div>\r\n\r\n          {showMap && (\r\n            <div className=\"bg-gray-50 rounded-lg p-4\">\r\n              <ProfessionalMap\r\n                height=\"500px\"\r\n                className=\"w-full\"\r\n                onLocationSelect={handleLocationSelect}\r\n                center={selectedLocation ? { lat: selectedLocation.lat, lng: selectedLocation.lng } : undefined}\r\n              />\r\n\r\n              <div className=\"mt-4 text-sm text-gray-600 text-center\">\r\n                <p>Search any location • Click on the map to select • Drag markers to adjust position • Professional Google Maps integration</p>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </section>\r\n\r\n      {/* Categories */}\r\n      {categories.length > 0 && (\r\n        <section className=\"py-8 bg-white border border-gray-200 rounded-lg\">\r\n          <div className=\"px-6\">\r\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-8\">Browse by Category</h2>\r\n            <div className=\"flex flex-wrap gap-4 mb-8\">\r\n              <button\r\n                onClick={() => setSelectedCategory('')}\r\n                className={`px-4 py-2 rounded-full font-medium transition-colors ${\r\n                  selectedCategory === ''\r\n                    ? 'text-white'\r\n                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\r\n                }`}\r\n                style={selectedCategory === '' ? { backgroundColor: '#f3a823' } : {}}\r\n              >\r\n                All\r\n              </button>\r\n              {categories.map((category) => (\r\n                <button\r\n                  key={category.id}\r\n                  onClick={() => setSelectedCategory(category.name)}\r\n                  className={`px-4 py-2 rounded-full font-medium transition-colors ${\r\n                    selectedCategory === category.name\r\n                      ? 'text-white'\r\n                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\r\n                  }`}\r\n                  style={selectedCategory === category.name ? { backgroundColor: '#f3a823' } : {}}\r\n                >\r\n                  {category.name}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </section>\r\n      )}\r\n\r\n      {/* Featured Restaurants */}\r\n      <section className=\"py-8 bg-white border border-gray-200 rounded-lg\">\r\n        <div className=\"px-6\">\r\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-8\">\r\n            {searchQuery || selectedCategory ? 'Search Results' : 'Restaurants'}\r\n          </h2>\r\n\r\n          {loading ? (\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\r\n              {[...Array(8)].map((_, index) => (\r\n                <div key={index} className=\"card animate-pulse\">\r\n                  <div className=\"h-48 bg-gray-300\"></div>\r\n                  <div className=\"p-4 space-y-3\">\r\n                    <div className=\"h-4 bg-gray-300 rounded w-3/4\"></div>\r\n                    <div className=\"h-3 bg-gray-300 rounded w-full\"></div>\r\n                    <div className=\"h-3 bg-gray-300 rounded w-2/3\"></div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          ) : filteredRestaurants.length > 0 ? (\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\r\n              {filteredRestaurants.map((restaurant) => (\r\n                <RestaurantCard key={restaurant.id} restaurant={restaurant} />\r\n              ))}\r\n            </div>\r\n          ) : (\r\n            <div className=\"text-center py-16\">\r\n              <div className=\"w-24 h-24 mx-auto mb-6 bg-gray-200 rounded-full flex items-center justify-center\">\r\n                <svg className=\"w-12 h-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10\" />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-xl font-semibold text-gray-900 mb-2\">No restaurants available yet</h3>\r\n              <p className=\"text-gray-500 mb-6\">\r\n                {searchQuery || selectedCategory\r\n                  ? 'No restaurants found matching your criteria. Try adjusting your search or filters.'\r\n                  : 'We\\'re working on adding amazing restaurants to your area. Check back soon!'\r\n                }\r\n              </p>\r\n              {(searchQuery || selectedCategory) && (\r\n                <button\r\n                  onClick={() => {\r\n                    setSearchQuery('');\r\n                    setSelectedCategory('');\r\n                  }}\r\n                  className=\"btn-primary\"\r\n                >\r\n                  Clear Filters\r\n                </button>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"bg-gray-900 text-white py-12 rounded-lg\">\r\n        <div className=\"px-6\">\r\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\r\n            <div>\r\n              <div className=\"flex items-center space-x-2 mb-4\">\r\n                <div className=\"w-8 h-8 rounded-lg flex items-center justify-center\" style={{ backgroundColor: '#f3a823' }}>\r\n                  <span className=\"text-white font-bold text-lg\">T</span>\r\n                </div>\r\n                <span className=\"text-xl font-bold\">Tap2Go</span>\r\n              </div>\r\n              <p className=\"text-gray-400\">\r\n                Your favorite food delivery platform. Fast, reliable, and delicious.\r\n              </p>\r\n            </div>\r\n            <div>\r\n              <h3 className=\"font-semibold mb-4\">Company</h3>\r\n              <ul className=\"space-y-2 text-gray-400\">\r\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">About Us</a></li>\r\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Careers</a></li>\r\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Press</a></li>\r\n              </ul>\r\n            </div>\r\n            <div>\r\n              <h3 className=\"font-semibold mb-4\">Support</h3>\r\n              <ul className=\"space-y-2 text-gray-400\">\r\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Help Center</a></li>\r\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Contact Us</a></li>\r\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Terms of Service</a></li>\r\n              </ul>\r\n            </div>\r\n            <div>\r\n              <h3 className=\"font-semibold mb-4\">For Restaurants</h3>\r\n              <ul className=\"space-y-2 text-gray-400\">\r\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Partner with Us</a></li>\r\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Restaurant Dashboard</a></li>\r\n                <li><a href=\"#\" className=\"hover:text-white transition-colors\">Business Support</a></li>\r\n                <li><a href=\"/analytics-demo\" className=\"hover:text-orange-400 transition-colors font-medium\">📊 Analytics Demo</a></li>\r\n              </ul>\r\n            </div>\r\n          </div>\r\n          <div className=\"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\">\r\n            <p>&copy; 2024 Tap2Go. All rights reserved.</p>\r\n          </div>\r\n        </div>\r\n      </footer>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,yBAAyB;IACzB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oNAAA,CAAA,WAAQ,AAAD,EAAsD;IAE7G,CAAA,GAAA,oNAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI;gBACF,iCAAiC;gBACjC,MAAM,gBAAgB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,qIAAA,CAAA,KAAE,EAAE;gBACrC,MAAM,kBAAkB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,eAAe,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;gBACrD,MAAM,qBAAqB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;gBAEzC,MAAM,iBAAiB,mBAAmB,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;wBACzD,IAAI,IAAI,EAAE;wBACV,GAAG,IAAI,IAAI,EAAE;oBACf,CAAC;gBAED,cAAc;gBAEd,kCAAkC;gBAClC,MAAM,iBAAiB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,qIAAA,CAAA,KAAE,EAAE;gBACtC,MAAM,mBAAmB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,gBAAgB,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE;gBACrD,MAAM,sBAAsB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;gBAE1C,8CAA8C;gBAC9C,MAAM,kBAAkB,CAAA,GAAA,uJAAA,CAAA,2BAAwB,AAAD,EAAE,oBAAoB,IAAI;gBAEzE,eAAe;YAEjB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,sCAAsC;gBACtC,eAAe,EAAE;gBACjB,cAAc,EAAE;YAClB,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAA;QAC7C,MAAM,gBAAgB,CAAC,eACrB,WAAW,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC9D,WAAW,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACrE,WAAW,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAE/E,MAAM,kBAAkB,CAAC,oBACvB,WAAW,OAAO,CAAC,QAAQ,CAAC;QAE9B,OAAO,iBAAiB;IAC1B;IAEA,4BAA4B;IAC5B,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QACpB,QAAQ,GAAG,CAAC,sBAAsB;IACpC;IAEA,MAAM,gBAAgB;QACpB,WAAW,CAAC;IACd;IAEA,qBACE,6PAAC;QAAI,WAAU;;0BAEb,6PAAC;gBAAQ,WAAU;0BACjB,cAAA,6PAAC;oBAAI,WAAU;;sCACb,6PAAC;4BAAI,WAAU;;8CACb,6PAAC;;sDACC,6PAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,6PAAC;4CAAE,WAAU;sDACV,mBACG,CAAC,UAAU,EAAE,iBAAiB,OAAO,EAAE,GACvC;;;;;;;;;;;;8CAIR,6PAAC;oCACC,SAAS;oCACT,WAAW,CAAC,mDAAmD,EAC7D,UACI,sDACA,+CACJ;8CAED,UAAU,aAAa;;;;;;;;;;;;wBAI3B,yBACC,6PAAC;4BAAI,WAAU;;8CACb,6PAAC,oJAAA,CAAA,UAAe;oCACd,QAAO;oCACP,WAAU;oCACV,kBAAkB;oCAClB,QAAQ,mBAAmB;wCAAE,KAAK,iBAAiB,GAAG;wCAAE,KAAK,iBAAiB,GAAG;oCAAC,IAAI;;;;;;8CAGxF,6PAAC;oCAAI,WAAU;8CACb,cAAA,6PAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQZ,WAAW,MAAM,GAAG,mBACnB,6PAAC;gBAAQ,WAAU;0BACjB,cAAA,6PAAC;oBAAI,WAAU;;sCACb,6PAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6PAAC;4BAAI,WAAU;;8CACb,6PAAC;oCACC,SAAS,IAAM,oBAAoB;oCACnC,WAAW,CAAC,qDAAqD,EAC/D,qBAAqB,KACjB,eACA,+CACJ;oCACF,OAAO,qBAAqB,KAAK;wCAAE,iBAAiB;oCAAU,IAAI,CAAC;8CACpE;;;;;;gCAGA,WAAW,GAAG,CAAC,CAAC,yBACf,6PAAC;wCAEC,SAAS,IAAM,oBAAoB,SAAS,IAAI;wCAChD,WAAW,CAAC,qDAAqD,EAC/D,qBAAqB,SAAS,IAAI,GAC9B,eACA,+CACJ;wCACF,OAAO,qBAAqB,SAAS,IAAI,GAAG;4CAAE,iBAAiB;wCAAU,IAAI,CAAC;kDAE7E,SAAS,IAAI;uCATT,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAkB5B,6PAAC;gBAAQ,WAAU;0BACjB,cAAA,6PAAC;oBAAI,WAAU;;sCACb,6PAAC;4BAAG,WAAU;sCACX,eAAe,mBAAmB,mBAAmB;;;;;;wBAGvD,wBACC,6PAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,sBACrB,6PAAC;oCAAgB,WAAU;;sDACzB,6PAAC;4CAAI,WAAU;;;;;;sDACf,6PAAC;4CAAI,WAAU;;8DACb,6PAAC;oDAAI,WAAU;;;;;;8DACf,6PAAC;oDAAI,WAAU;;;;;;8DACf,6PAAC;oDAAI,WAAU;;;;;;;;;;;;;mCALT;;;;;;;;;mCAUZ,oBAAoB,MAAM,GAAG,kBAC/B,6PAAC;4BAAI,WAAU;sCACZ,oBAAoB,GAAG,CAAC,CAAC,2BACxB,6PAAC,mJAAA,CAAA,UAAc;oCAAqB,YAAY;mCAA3B,WAAW,EAAE;;;;;;;;;iDAItC,6PAAC;4BAAI,WAAU;;8CACb,6PAAC;oCAAI,WAAU;8CACb,cAAA,6PAAC;wCAAI,WAAU;wCAA0B,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjF,cAAA,6PAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6PAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,6PAAC;oCAAE,WAAU;8CACV,eAAe,mBACZ,uFACA;;;;;;gCAGL,CAAC,eAAe,gBAAgB,mBAC/B,6PAAC;oCACC,SAAS;wCACP,eAAe;wCACf,oBAAoB;oCACtB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAUX,6PAAC;gBAAO,WAAU;0BAChB,cAAA,6PAAC;oBAAI,WAAU;;sCACb,6PAAC;4BAAI,WAAU;;8CACb,6PAAC;;sDACC,6PAAC;4CAAI,WAAU;;8DACb,6PAAC;oDAAI,WAAU;oDAAsD,OAAO;wDAAE,iBAAiB;oDAAU;8DACvG,cAAA,6PAAC;wDAAK,WAAU;kEAA+B;;;;;;;;;;;8DAEjD,6PAAC;oDAAK,WAAU;8DAAoB;;;;;;;;;;;;sDAEtC,6PAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAI/B,6PAAC;;sDACC,6PAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6PAAC;4CAAG,WAAU;;8DACZ,6PAAC;8DAAG,cAAA,6PAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6PAAC;8DAAG,cAAA,6PAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6PAAC;8DAAG,cAAA,6PAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;8CAGnE,6PAAC;;sDACC,6PAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6PAAC;4CAAG,WAAU;;8DACZ,6PAAC;8DAAG,cAAA,6PAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6PAAC;8DAAG,cAAA,6PAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6PAAC;8DAAG,cAAA,6PAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;;;;;;8CAGnE,6PAAC;;sDACC,6PAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6PAAC;4CAAG,WAAU;;8DACZ,6PAAC;8DAAG,cAAA,6PAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6PAAC;8DAAG,cAAA,6PAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6PAAC;8DAAG,cAAA,6PAAC;wDAAE,MAAK;wDAAI,WAAU;kEAAqC;;;;;;;;;;;8DAC/D,6PAAC;8DAAG,cAAA,6PAAC;wDAAE,MAAK;wDAAkB,WAAU;kEAAsD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAIpG,6PAAC;4BAAI,WAAU;sCACb,cAAA,6PAAC;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}]}