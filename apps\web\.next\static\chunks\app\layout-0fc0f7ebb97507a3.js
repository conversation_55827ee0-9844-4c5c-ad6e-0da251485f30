(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{5323:(e,t,r)=>{"use strict";r.d(t,{CartProvider:()=>i,_:()=>o});var a=r(95155),s=r(12115);let n=(0,s.createContext)(void 0);function o(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}function i(e){let{children:t}=e,[r,o]=(0,s.useReducer)(function e(t,r){switch(r.type){case"ADD_ITEM":{let e,{item:a,quantity:s,specialInstructions:n}=r.payload;if(!t||t.restaurantId!==a.restaurantId){let e={id:"".concat(a.id,"-").concat(Date.now()),menuItem:a,quantity:s,specialInstructions:n,totalPrice:a.price*s},t=e.totalPrice,r=.08*t;return{items:[e],restaurantId:a.restaurantId,subtotal:t,deliveryFee:5.99,tax:r,total:t+5.99+r}}let o=t.items.findIndex(e=>e.menuItem.id===a.id&&e.specialInstructions===n);if(o>=0)e=t.items.map((e,t)=>t===o?{...e,quantity:e.quantity+s,totalPrice:(e.quantity+s)*e.menuItem.price}:e);else{let r={id:"".concat(a.id,"-").concat(Date.now()),menuItem:a,quantity:s,specialInstructions:n,totalPrice:a.price*s};e=[...t.items,r]}let i=e.reduce((e,t)=>e+t.totalPrice,0),l=.08*i;return{...t,items:e,subtotal:i,tax:l,total:i+t.deliveryFee+l}}case"REMOVE_ITEM":{if(!t)return null;let e=t.items.filter(e=>e.id!==r.payload.itemId);if(0===e.length)return null;let a=e.reduce((e,t)=>e+t.totalPrice,0),s=.08*a;return{...t,items:e,subtotal:a,tax:s,total:a+t.deliveryFee+s}}case"UPDATE_QUANTITY":{if(!t)return null;let{itemId:a,quantity:s}=r.payload;if(s<=0)return e(t,{type:"REMOVE_ITEM",payload:{itemId:a}});let n=t.items.map(e=>e.id===a?{...e,quantity:s,totalPrice:e.menuItem.price*s}:e),o=n.reduce((e,t)=>e+t.totalPrice,0),i=.08*o;return{...t,items:n,subtotal:o,tax:i,total:o+t.deliveryFee+i}}case"CLEAR_CART":return null;case"LOAD_CART":return r.payload;default:return t}},null);return(0,s.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{let t=JSON.parse(e);o({type:"LOAD_CART",payload:t})}catch(e){console.error("Error loading cart from localStorage:",e)}},[]),(0,s.useEffect)(()=>{r?localStorage.setItem("cart",JSON.stringify(r)):localStorage.removeItem("cart")},[r]),(0,a.jsx)(n.Provider,{value:{cart:r,addToCart:(e,t,r)=>{o({type:"ADD_ITEM",payload:{item:e,quantity:t,specialInstructions:r}})},removeFromCart:e=>{o({type:"REMOVE_ITEM",payload:{itemId:e}})},updateQuantity:(e,t)=>{o({type:"UPDATE_QUANTITY",payload:{itemId:e,quantity:t}})},clearCart:()=>{o({type:"CLEAR_CART"})},getCartTotal:()=>(null==r?void 0:r.total)||0},children:t})}},8993:(e,t,r)=>{"use strict";r.d(t,{default:()=>ez});var a=r(95155);r(12115);var s=r(34540),n=r(97730),o=r(17533),i=r(84966),l=r(61328),d=r(18091),c=r(37469),u=r(38651),m=r(19159);let g=(0,i.zD)("orders/fetchOrders",async(e,t)=>{let{filters:r,page:a=1,limit:s=20,role:n}=e,{rejectWithValue:o}=t;try{let e=await fetch("/api/orders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({filters:r,page:a,limit:s,role:n})});if(!e.ok)throw Error("Failed to fetch orders");return await e.json()}catch(e){return o(e instanceof Error?e.message:"Failed to fetch orders")}}),p=(0,i.zD)("orders/createOrder",async(e,t)=>{let{rejectWithValue:r}=t;try{let t=await fetch("/api/orders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create order");return await t.json()}catch(e){return r(e instanceof Error?e.message:"Failed to create order")}}),h=(0,i.zD)("orders/updateStatus",async(e,t)=>{let{orderId:r,status:a,note:s}=e,{rejectWithValue:n}=t;try{let e=await fetch("/api/orders/".concat(r,"/status"),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:a,note:s})});if(!e.ok)throw Error("Failed to update order status");return await e.json()}catch(e){return n(e instanceof Error?e.message:"Failed to update order status")}}),f=(0,i.zD)("orders/assignDriver",async(e,t)=>{let{orderId:r,driverId:a}=e,{rejectWithValue:s}=t;try{let e=await fetch("/api/orders/".concat(r,"/assign-driver"),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({driverId:a})});if(!e.ok)throw Error("Failed to assign driver");return await e.json()}catch(e){return s(e instanceof Error?e.message:"Failed to assign driver")}}),x=(0,i.zD)("orders/rateOrder",async(e,t)=>{let{orderId:r,rating:a}=e,{rejectWithValue:s}=t;try{let e=await fetch("/api/orders/".concat(r,"/rate"),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({rating:a})});if(!e.ok)throw Error("Failed to rate order");return await e.json()}catch(e){return s(e instanceof Error?e.message:"Failed to rate order")}}),y=(0,i.Z0)({name:"orders",initialState:{orders:[],currentOrder:null,loading:!1,creating:!1,updating:!1,error:null,filters:{},sortBy:"createdAt",sortOrder:"desc",currentPage:1,totalPages:1,totalCount:0,liveUpdates:!0,lastUpdate:0,customerOrders:[],vendorOrders:[],driverOrders:[],adminOrders:[],stats:{total:0,pending:0,confirmed:0,preparing:0,ready:0,pickedUp:0,delivered:0,cancelled:0,todayRevenue:0,averageOrderValue:0}},reducers:{updateOrderRealTime:(e,t)=>{let r=t.payload,a=e.orders.findIndex(e=>e.id===r.id);if(a>=0?e.orders[a]=r:e.orders.unshift(r),r.customerId){let t=e.customerOrders.findIndex(e=>e.id===r.id);t>=0&&(e.customerOrders[t]=r)}if(r.vendorId){let t=e.vendorOrders.findIndex(e=>e.id===r.id);t>=0&&(e.vendorOrders[t]=r)}if(r.driverId){let t=e.driverOrders.findIndex(e=>e.id===r.id);t>=0&&(e.driverOrders[t]=r)}e.lastUpdate=Date.now()},setCurrentOrder:(e,t)=>{e.currentOrder=t.payload},setFilters:(e,t)=>{e.filters=t.payload,e.currentPage=1},updateFilter:(e,t)=>{let{key:r,value:a}=t.payload;void 0!==a?e.filters[r]=a:delete e.filters[r],e.currentPage=1},clearFilters:e=>{e.filters={},e.currentPage=1},setSorting:(e,t)=>{let{sortBy:r,sortOrder:a}=t.payload;e.sortBy=r,e.sortOrder=a},setCurrentPage:(e,t)=>{e.currentPage=t.payload},setLiveUpdates:(e,t)=>{e.liveUpdates=t.payload},updateStats:(e,t)=>{e.stats={...e.stats,...t.payload}},setError:(e,t)=>{e.error=t.payload},clearError:e=>{e.error=null},setLoading:(e,t)=>{e.loading=t.payload},setCreating:(e,t)=>{e.creating=t.payload},setUpdating:(e,t)=>{e.updating=t.payload}},extraReducers:e=>{e.addCase(g.pending,e=>{e.loading=!0,e.error=null}).addCase(g.fulfilled,(e,t)=>{let{orders:r,totalCount:a,totalPages:s,role:n}=t.payload;e.orders=r,e.totalCount=a,e.totalPages=s,e.loading=!1,"customer"===n?e.customerOrders=r:"vendor"===n?e.vendorOrders=r:"driver"===n?e.driverOrders=r:"admin"===n&&(e.adminOrders=r)}).addCase(g.rejected,(e,t)=>{e.loading=!1,e.error=t.payload}),e.addCase(p.pending,e=>{e.creating=!0,e.error=null}).addCase(p.fulfilled,(e,t)=>{let r=t.payload;e.orders.unshift(r),e.currentOrder=r,e.creating=!1}).addCase(p.rejected,(e,t)=>{e.creating=!1,e.error=t.payload}),e.addCase(h.pending,e=>{e.updating=!0,e.error=null}).addCase(h.fulfilled,(e,t)=>{let r=t.payload;y.caseReducers.updateOrderRealTime(e,{type:"orders/updateOrderRealTime",payload:r}),e.updating=!1}).addCase(h.rejected,(e,t)=>{e.updating=!1,e.error=t.payload}),e.addCase(f.fulfilled,(e,t)=>{let r=t.payload;y.caseReducers.updateOrderRealTime(e,{type:"orders/updateOrderRealTime",payload:r})}),e.addCase(x.fulfilled,(e,t)=>{let r=t.payload;y.caseReducers.updateOrderRealTime(e,{type:"orders/updateOrderRealTime",payload:r})})}}),{updateOrderRealTime:b,setCurrentOrder:v,setFilters:w,updateFilter:j,clearFilters:N,setSorting:C,setCurrentPage:T,setLiveUpdates:O,updateStats:S,setError:A,clearError:I,setLoading:D,setCreating:E,setUpdating:k}=y.actions,P=(0,i.zD)("restaurants/fetchRestaurants",async(e,t)=>{let{rejectWithValue:r}=t;try{return[]}catch(e){return r(e instanceof Error?e.message:"Failed to fetch restaurants")}}),L=(0,i.Z0)({name:"restaurants",initialState:{restaurants:[],currentRestaurant:null,menu:[],loading:!1,error:null,filters:{},searchQuery:"",sortBy:"distance"},reducers:{setCurrentRestaurant:(e,t)=>{e.currentRestaurant=t.payload},setFilters:(e,t)=>{e.filters=t.payload},setSearchQuery:(e,t)=>{e.searchQuery=t.payload},setSortBy:(e,t)=>{e.sortBy=t.payload}},extraReducers:e=>{e.addCase(P.pending,e=>{e.loading=!0,e.error=null}).addCase(P.fulfilled,(e,t)=>{e.restaurants=t.payload,e.loading=!1}).addCase(P.rejected,(e,t)=>{e.loading=!1,e.error=t.payload})}}),{setCurrentRestaurant:R,setFilters:F,setSearchQuery:M,setSortBy:z}=L.actions,U=(0,i.zD)("drivers/fetchDrivers",async(e,t)=>{let{rejectWithValue:r}=t;try{return[]}catch(e){return r(e instanceof Error?e.message:"Failed to fetch drivers")}}),_=(0,i.Z0)({name:"drivers",initialState:{drivers:[],availableDrivers:[],currentDriver:null,loading:!1,error:null,realTimeTracking:!1},reducers:{updateDriverLocation:(e,t)=>{let{driverId:r,location:a}=t.payload,s=e.drivers.find(e=>e.id===r);s&&(s.location=a)},setDriverOnlineStatus:(e,t)=>{let{driverId:r,isOnline:a}=t.payload,s=e.drivers.find(e=>e.id===r);s&&(s.isOnline=a)},setRealTimeTracking:(e,t)=>{e.realTimeTracking=t.payload}},extraReducers:e=>{e.addCase(U.pending,e=>{e.loading=!0,e.error=null}).addCase(U.fulfilled,(e,t)=>{e.drivers=t.payload,e.availableDrivers=t.payload.filter(e=>e.isOnline&&"active"===e.status),e.loading=!1}).addCase(U.rejected,(e,t)=>{e.loading=!1,e.error=t.payload})}}),{updateDriverLocation:V,setDriverOnlineStatus:H,setRealTimeTracking:Z}=_.actions,q=(0,i.zD)("customers/fetchCustomers",async(e,t)=>{let{rejectWithValue:r}=t;try{return[]}catch(e){return r(e instanceof Error?e.message:"Failed to fetch customers")}}),G=(0,i.Z0)({name:"customers",initialState:{customers:[],loading:!1,error:null},reducers:{},extraReducers:e=>{e.addCase(q.pending,e=>{e.loading=!0,e.error=null}).addCase(q.fulfilled,(e,t)=>{e.customers=t.payload,e.loading=!1}).addCase(q.rejected,(e,t)=>{e.loading=!1,e.error=t.payload})}}),Q=(0,i.Z0)({name:"realTime",initialState:{connections:{},orderUpdates:{},driverLocations:{},isConnected:!1,lastHeartbeat:0},reducers:{setConnection:(e,t)=>{let{id:r,connected:a}=t.payload;e.connections[r]=a},updateOrderRealTime:(e,t)=>{let{orderId:r,update:a}=t.payload;e.orderUpdates[r]=a},updateDriverLocation:(e,t)=>{let{driverId:r,location:a}=t.payload;e.driverLocations[r]={...a,timestamp:Date.now()}},setConnected:(e,t)=>{e.isConnected=t.payload,e.lastHeartbeat=Date.now()}}}),{setConnection:B,updateOrderRealTime:Y,updateDriverLocation:J,setConnected:K}=Q.actions,W=(0,i.zD)("analytics/fetchAnalytics",async(e,t)=>{let{rejectWithValue:r}=t;try{return console.log("Fetching analytics with params:",e),{metrics:{totalRevenue:0,totalOrders:0,averageOrderValue:0,customerCount:0},reports:[]}}catch(e){return r(e instanceof Error?e.message:"Failed to fetch analytics")}}),$=(0,i.Z0)({name:"analytics",initialState:{metrics:{totalRevenue:0,totalOrders:0,averageOrderValue:0,customerCount:0},reports:[],loading:!1,error:null},reducers:{},extraReducers:e=>{e.addCase(W.pending,e=>{e.loading=!0,e.error=null}).addCase(W.fulfilled,(e,t)=>{e.metrics=t.payload.metrics,e.reports=t.payload.reports,e.loading=!1}).addCase(W.rejected,(e,t)=>{e.loading=!1,e.error=t.payload})}}),X=(0,i.Z0)({name:"notifications",initialState:{notifications:[],unreadCount:0,loading:!1,error:null},reducers:{addNotification:(e,t)=>{e.notifications.unshift(t.payload),t.payload.read||(e.unreadCount+=1)},markAsRead:(e,t)=>{let r=e.notifications.find(e=>e.id===t.payload);r&&!r.read&&(r.read=!0,e.unreadCount-=1)},markAllAsRead:e=>{e.notifications.forEach(e=>e.read=!0),e.unreadCount=0},removeNotification:(e,t)=>{let r=e.notifications.findIndex(e=>e.id===t.payload);r>=0&&(e.notifications[r].read||(e.unreadCount-=1),e.notifications.splice(r,1))}}}),{addNotification:ee,markAsRead:et,markAllAsRead:er,removeNotification:ea}=X.actions,es=(0,i.zD)("admin/fetchDashboardStats",async(e,t)=>{let{rejectWithValue:r}=t;try{return{totalUsers:0,totalVendors:0,totalDrivers:0,totalOrders:0,totalRevenue:0,activeOrders:0,pendingVendors:0,pendingDrivers:0}}catch(e){return r(e instanceof Error?e.message:"Failed to fetch dashboard stats")}}),en=(0,i.Z0)({name:"admin",initialState:{dashboardStats:{totalUsers:0,totalVendors:0,totalDrivers:0,totalOrders:0,totalRevenue:0,activeOrders:0,pendingVendors:0,pendingDrivers:0},users:[],vendors:[],drivers:[],loading:!1,error:null,selectedItems:[],bulkActions:!1},reducers:{setSelectedItems:(e,t)=>{e.selectedItems=t.payload},toggleBulkActions:e=>{e.bulkActions=!e.bulkActions,e.bulkActions||(e.selectedItems=[])}},extraReducers:e=>{e.addCase(es.pending,e=>{e.loading=!0,e.error=null}).addCase(es.fulfilled,(e,t)=>{e.dashboardStats=t.payload,e.loading=!1}).addCase(es.rejected,(e,t)=>{e.loading=!1,e.error=t.payload})}}),{setSelectedItems:eo,toggleBulkActions:ei}=en.actions,el=(0,i.Z0)({name:"vendor",initialState:{restaurant:null,menu:[],orders:[],analytics:null,loading:!1,error:null,menuEditMode:!1,selectedOrders:[]},reducers:{setRestaurant:(e,t)=>{e.restaurant=t.payload},setMenu:(e,t)=>{e.menu=t.payload},setMenuEditMode:(e,t)=>{e.menuEditMode=t.payload},setSelectedOrders:(e,t)=>{e.selectedOrders=t.payload}}}),{setRestaurant:ed,setMenu:ec,setMenuEditMode:eu,setSelectedOrders:em}=el.actions,eg=(0,i.Z0)({name:"driverPanel",initialState:{profile:null,currentDelivery:null,earnings:{today:0,week:0,month:0,total:0},isOnline:!1,location:null,loading:!1,error:null},reducers:{setProfile:(e,t)=>{e.profile=t.payload},setCurrentDelivery:(e,t)=>{e.currentDelivery=t.payload},setOnlineStatus:(e,t)=>{e.isOnline=t.payload},updateLocation:(e,t)=>{e.location=t.payload},updateEarnings:(e,t)=>{e.earnings={...e.earnings,...t.payload}}}}),{setProfile:ep,setCurrentDelivery:eh,setOnlineStatus:ef,updateLocation:ex,updateEarnings:ey}=eg.actions;var eb=r(15890),ev=r(20498),ew=r(70404);let ej=(0,ev.cw)({baseUrl:"/api",prepareHeaders:e=>(e.set("content-type","application/json"),e)}),eN=(0,ew.xP)({reducerPath:"api",baseQuery:ej,tagTypes:["User","Restaurant","MenuItem","Order","Driver","Customer","Vendor","Admin","Analytics","Notification"],endpoints:e=>({getRestaurants:e.query({query:()=>"/restaurants",providesTags:["Restaurant"]}),getOrders:e.query({query:()=>"/orders",providesTags:["Order"]})})}),{useGetRestaurantsQuery:eC,useGetOrdersQuery:eT}=eN,eO=e=>t=>r=>{if(["cart/addToCart","cart/removeFromCart","orders/createOrder","auth/signIn","auth/signUp","restaurants/setCurrentRestaurant"].includes(r.type))try{var a;console.log("Analytics Event:",{action:r.type,payload:r.payload,timestamp:new Date().toISOString(),userId:null==(a=e.getState().auth.user)?void 0:a.id})}catch(e){console.error("Analytics tracking error:",e)}return t(r)},eS=e=>e=>t=>("auth/setUser"===t.type&&t.payload&&function(e,t){console.log("Setting up real-time listeners for user:",t.id),t.role,t.role,t.role}(0,t.payload),"auth/clearAuth"===t.type&&(eA.forEach(e=>e()),eA=[],console.log("Cleaned up real-time listeners")),e(t)),eA=[],eI=e=>t=>r=>{if(r.type.endsWith("/rejected")){var a;let t=r.payload||(null==(a=r.error)?void 0:a.message)||"An error occurred";e.dispatch((0,u.p6)({title:"Error",message:t})),console.error("Redux Error:",{action:r.type,error:t,timestamp:new Date().toISOString()})}return t(r)};var eD=r(35317);let eE=e=>{if(null==e)return e;if(e instanceof eD.Dc)return e.toDate().toISOString();if(e instanceof Date)return e.toISOString();if(Array.isArray(e))return e.map(eE);if("object"==typeof e&&null!==e&&e.constructor===Object){let t={};for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=eE(e[r]));return t}return e},ek=()=>e=>t=>["auth/setUser","auth/syncAuthState","auth/updateUserData","auth/signIn/fulfilled","auth/signUp/fulfilled","auth/updateUserProfile/fulfilled"].includes(t.type)?e({...t,payload:eE(t.payload)}):e(t),eP=(0,o.HY)({auth:c.Ay.reducer,ui:u.Ay.reducer,cart:m.Ay.reducer,orders:y.reducer,restaurants:L.reducer,drivers:_.reducer,customers:G.reducer,realTime:Q.reducer,analytics:$.reducer,notifications:X.reducer,admin:en.reducer,vendor:el.reducer,driverPanel:eg.reducer,cms:eb.Ay,api:eN.reducer}),eL={key:"tap2go-root",version:1,storage:d.A,whitelist:["cart","ui","auth"],blacklist:["api","realTime","analytics"]},eR=(0,l.rL)(eL,eP),eF=(0,i.U1)({reducer:eR,middleware:e=>e({serializableCheck:{ignoredActions:[l.ZM,l.r2,l.Hz,l.DY,l.Cq,l.eY],ignoredActionPaths:["meta.arg","meta.baseQueryMeta"],ignoredPaths:["meta.arg","meta.baseQueryMeta"]},immutableCheck:!1}).concat(ek).concat(eN.middleware).concat(eO).concat(eS).concat(eI),devTools:!1}),eM=(0,l.GM)(eF);function ez(e){let{children:t}=e;return(0,a.jsx)(s.Kq,{store:eF,children:(0,a.jsx)(n.Q,{loading:(0,a.jsx)("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center bg-white",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 border-4 border-orange-500 border-t-transparent rounded-full animate-spin"}),(0,a.jsx)("p",{className:"text-gray-600 font-medium",children:"Loading Tap2Go..."})]})}),persistor:eM,children:t})})}(0,ev.$k)(eF.dispatch)},15890:(e,t,r)=>{"use strict";r.d(t,{$9:()=>l,Ay:()=>N,Di:()=>b,ET:()=>g,Ke:()=>j,Km:()=>o,Lk:()=>x,Nh:()=>m,Wt:()=>c,YF:()=>s,YS:()=>h,a1:()=>f,fd:()=>n,r1:()=>p,sx:()=>w,uZ:()=>i,vw:()=>v,w_:()=>u,xQ:()=>y,zw:()=>d});let a=(0,r(84966).Z0)({name:"cms",initialState:{posts:[],pages:[],categories:[],tags:[],stats:{totalPosts:0,publishedPosts:0,draftPosts:0,totalPages:0,publishedPages:0,totalCategories:0,totalTags:0,totalViews:0,trashedPosts:0,trashedPages:0},loading:{global:!1,posts:!1,pages:!1,categories:!1,tags:!1},activeTab:"posts",viewMode:"all",error:null,lastUpdated:null},reducers:{setActiveTab:(e,t)=>{e.activeTab=t.payload,e.error=null},setViewMode:(e,t)=>{e.viewMode=t.payload,e.error=null},setLoading:(e,t)=>{e.loading.global=t.payload},setPosts:(e,t)=>{e.posts=t.payload},setPages:(e,t)=>{e.pages=t.payload},setCategories:(e,t)=>{e.categories=t.payload},setTags:(e,t)=>{e.tags=t.payload},setStats:(e,t)=>{e.stats=t.payload},setError:(e,t)=>{e.error=t.payload},clearError:e=>{e.error=null}}}),{setActiveTab:s,setViewMode:n,setLoading:o,setPosts:i,setPages:l,setCategories:d,setTags:c,setStats:u,setError:m,clearError:g}=a.actions,p=e=>e.cms.posts,h=e=>e.cms.pages,f=e=>e.cms.categories,x=e=>e.cms.tags,y=e=>e.cms.stats,b=e=>{let t=e.cms.loading;return t.global||t.posts||t.pages||t.categories||t.tags},v=e=>e.cms.activeTab,w=e=>e.cms.viewMode,j=e=>e.cms.error,N=a.reducer},24608:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,65356,23)),Promise.resolve().then(r.t.bind(r,30347,23)),Promise.resolve().then(r.bind(r,28448)),Promise.resolve().then(r.bind(r,52750)),Promise.resolve().then(r.bind(r,40283)),Promise.resolve().then(r.bind(r,5323)),Promise.resolve().then(r.bind(r,8993))},28448:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var a=r(95155),s=r(12115),n=r(30192),o=r(47165),i=r(74500);function l(e){var t,r,s,n;let{message:o}=e,i="user"===o.role,l=null==(t=o.metadata)?void 0:t.escalated;return(0,a.jsx)("div",{className:"flex ".concat(i?"justify-end":"justify-start"," mb-2"),children:(0,a.jsxs)("div",{className:"flex items-end space-x-2 max-w-[85%] ".concat(i?"flex-row-reverse space-x-reverse":""),children:[!i&&(0,a.jsx)("div",{className:"w-7 h-7 rounded-full bg-gradient-to-r from-orange-400 to-orange-600 flex items-center justify-center flex-shrink-0 mb-1",children:(0,a.jsx)("span",{className:"text-white text-xs font-bold",children:"T2G"})}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"message-bubble px-3 py-2 max-w-xs lg:max-w-md ".concat(i?"bg-orange-500 text-white rounded-2xl rounded-br-md":l?"bg-red-50 border border-red-200 text-red-800 rounded-2xl rounded-bl-md":"bg-gray-100 text-gray-900 rounded-2xl rounded-bl-md"),children:[(0,a.jsx)("p",{className:"text-sm leading-relaxed whitespace-pre-wrap break-words",children:o.content}),l&&(0,a.jsx)("div",{className:"mt-2 p-2 bg-red-100 rounded-lg border border-red-200",children:(0,a.jsx)("p",{className:"text-xs text-red-600 font-medium",children:"\uD83D\uDEA8 This conversation has been escalated to our human support team"})})]}),(0,a.jsxs)("div",{className:"text-xs mt-1 px-1 ".concat(i?"text-right text-gray-500":"text-left text-gray-500"),children:[("string"==typeof(n=o.timestamp)?new Date(n):n).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit",hour12:!0}),(null==(r=o.metadata)?void 0:r.confidence)&&!1]}),!i&&(null==(s=o.metadata)?void 0:s.intent)&&!1]})]})})}var d=r(16465),c=r(72994),u=r(94049),m=r(34333);function g(e){let{onSendMessage:t,disabled:r=!1,placeholder:n="Type your message..."}=e,[o,i]=(0,s.useState)(""),[l,g]=(0,s.useState)(!1),p=(0,s.useRef)(null);(0,s.useEffect)(()=>{p.current&&(p.current.style.height="auto",p.current.style.height="".concat(p.current.scrollHeight,"px"))},[o]),(0,s.useEffect)(()=>{p.current&&!r&&p.current.focus()},[r]);let h=e=>{e.preventDefault(),o.trim()&&!r&&(t(o.trim()),i(""),p.current&&(p.current.style.height="auto"))},f=o.trim().length>0&&!r;return(0,a.jsx)("div",{className:"bg-white border-t border-gray-200 max-md:flex-shrink-0",children:(0,a.jsx)("div",{className:"px-4 py-3 max-md:px-3 max-md:py-2",children:(0,a.jsxs)("form",{onSubmit:h,className:"flex items-end space-x-2",children:[(0,a.jsx)("div",{className:"flex items-center space-x-1",children:(0,a.jsx)("button",{type:"button",disabled:r,className:"p-2 rounded-full transition-colors duration-200 ".concat(r?"opacity-50 cursor-not-allowed":"text-orange-500 hover:bg-orange-50"),title:"Add photo",children:(0,a.jsx)(d.A,{className:"w-5 h-5"})})}),(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsxs)("div",{className:"chat-input-container flex items-end bg-gray-100 rounded-2xl px-3 py-2 min-h-[40px] transition-all duration-200 focus-within:bg-gray-50",children:[(0,a.jsx)("textarea",{ref:p,value:o,onChange:e=>i(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),h(e))},placeholder:r?"Chat is disabled":n,disabled:r,rows:1,className:"flex-1 bg-transparent resize-none outline-none text-gray-900 placeholder:text-gray-500 text-sm leading-5",style:{minHeight:"20px",maxHeight:"100px"}}),(0,a.jsx)("button",{type:"button",disabled:r,className:"ml-2 p-1 rounded-full transition-colors duration-200 ".concat(r?"opacity-50 cursor-not-allowed":"text-gray-500 hover:text-orange-500"),title:"Add emoji",children:(0,a.jsx)(c.A,{className:"w-5 h-5"})})]}),o.length>800&&(0,a.jsxs)("div",{className:"absolute -top-6 right-2 text-xs text-gray-500 bg-white px-1 rounded",children:[o.length,"/1000"]})]}),o.trim()?(0,a.jsx)("button",{type:"submit",disabled:!f,className:"flex-shrink-0 p-2 rounded-full transition-all duration-200 ".concat(f?"bg-orange-500 text-white hover:bg-orange-600 transform hover:scale-105":"bg-gray-300 text-gray-500 cursor-not-allowed"),title:"Send message",children:(0,a.jsx)(u.A,{className:"w-5 h-5"})}):(0,a.jsx)("button",{type:"button",onClick:()=>{g(!l),l?console.log("Voice recording stopped"):console.log("Voice recording started")},disabled:r,className:"flex-shrink-0 p-2 rounded-full transition-colors duration-200 ".concat(l?"bg-red-500 text-white animate-pulse":r?"bg-gray-200 text-gray-400 cursor-not-allowed":"bg-orange-500 text-white hover:bg-orange-600"),title:l?"Stop recording":"Voice message",children:(0,a.jsx)(m.A,{className:"w-5 h-5"})})]})})})}function p(e){let{isOpen:t,onClose:r,onMinimize:n,isMinimized:d,position:c="bottom-right"}=e,{messages:u,loading:m,error:p,isTyping:h,sendMessage:f,clearChat:x}=function(){let[e,t]=(0,s.useState)({messages:[],loading:!1,error:null,isTyping:!1}),[r,a]=(0,s.useState)(!1);r||0!==e.messages.length||(t(e=>({...e,messages:[{id:"welcome_".concat(Date.now()),role:"assistant",content:"Hi! I'm an AI assistant. I can help you with any questions or topics you'd like to discuss. How can I help you today?",timestamp:new Date().toISOString()}]})),a(!0));let n=(0,s.useCallback)(async r=>{if(!r.trim())return;let a={id:"user_".concat(Date.now()),role:"user",content:r.trim(),timestamp:new Date().toISOString()};t(e=>({...e,messages:[...e.messages,a],isTyping:!0,error:null}));try{let a=await fetch("/api/chatbot/chat",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:r.trim(),conversationHistory:e.messages.slice(-6)})}),s=await a.json();if(!a.ok)throw Error(s.error||"Failed to send message");return t(e=>({...e,messages:[...e.messages,s.message],isTyping:!1})),s.message}catch(e){throw console.error("Error sending message:",e),t(t=>({...t,isTyping:!1,error:e instanceof Error?e.message:"Failed to send message"})),e}},[e.messages]),o=(0,s.useCallback)(()=>{t({messages:[],loading:!1,error:null,isTyping:!1}),a(!1)},[]);return{messages:Array.isArray(e.messages)?e.messages:[],loading:e.loading,error:e.error,isTyping:e.isTyping,sendMessage:n,clearChat:o}}(),y=(0,s.useRef)(null);(0,s.useEffect)(()=>{y.current&&y.current.scrollIntoView({behavior:"smooth"})},[u,h]);let b=async e=>{try{await f(e)}catch(e){console.error("Failed to send message:",e)}};return t?(0,a.jsx)("div",{className:"fixed ".concat({"bottom-right":"bottom-4 right-4","bottom-left":"bottom-4 left-4"}[c]," z-50 max-md:inset-0"),children:(0,a.jsxs)("div",{className:"chat-widget bg-white rounded-lg shadow-2xl border border-gray-200 transition-all duration-300 max-md:rounded-none max-md:h-screen max-md:w-full max-md:flex max-md:flex-col ".concat(d?"w-80 h-16 max-md:w-full max-md:h-16":"w-96 h-[600px] max-md:w-full max-md:h-screen"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between px-4 py-3 bg-white border-b border-gray-200 rounded-t-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-full bg-gradient-to-r from-orange-400 to-orange-600 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:"T2G"})}),(0,a.jsx)("div",{className:"absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 text-sm",children:"Tap2Go Assistant"}),(0,a.jsx)("p",{className:"text-xs text-green-600 font-medium",children:"Active now"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("button",{onClick:n,className:"p-2 hover:bg-gray-100 rounded-full transition-colors",title:d?"Expand chat":"Minimize chat",children:(0,a.jsx)(o.A,{className:"w-5 h-5 text-gray-600"})}),(0,a.jsx)("button",{onClick:r,className:"p-2 hover:bg-gray-100 rounded-full transition-colors",title:"Close chat",children:(0,a.jsx)(i.A,{className:"w-5 h-5 text-gray-600"})})]})]}),!d&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-4 h-[440px] max-md:flex-1 max-md:h-auto bg-white",children:m?(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:"Starting chat..."})]})}):p?(0,a.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-red-500 mb-2",children:"⚠️"}),(0,a.jsx)("p",{className:"text-red-600 text-sm mb-3",children:p}),(0,a.jsx)("button",{onClick:()=>{x()},className:"px-4 py-2 bg-orange-500 text-white rounded-lg text-sm hover:bg-orange-600 transition-colors duration-200",children:"Try Again"})]})}):(0,a.jsxs)(a.Fragment,{children:[Array.isArray(u)&&1===u.length&&(0,a.jsx)("div",{className:"mb-4 flex justify-center",children:(0,a.jsx)("div",{className:"bg-gray-100 px-3 py-1 rounded-full",children:(0,a.jsx)("p",{className:"text-gray-600 text-xs text-center",children:"\uD83D\uDC4B Welcome to Tap2Go! I'm here to help you with orders, restaurant info, delivery questions, and more."})})}),Array.isArray(u)&&u.map(e=>(0,a.jsx)(l,{message:e},e.id)),h&&(0,a.jsx)("div",{className:"flex justify-start mb-2",children:(0,a.jsxs)("div",{className:"flex items-end space-x-2 max-w-[85%]",children:[(0,a.jsx)("div",{className:"w-7 h-7 rounded-full bg-gradient-to-r from-orange-400 to-orange-600 flex items-center justify-center flex-shrink-0 mb-1",children:(0,a.jsx)("span",{className:"text-white text-xs font-bold",children:"T2G"})}),(0,a.jsx)("div",{className:"bg-gray-100 rounded-2xl rounded-bl-md px-3 py-2",children:(0,a.jsxs)("div",{className:"flex space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})]})}),(0,a.jsx)("div",{ref:y})]})}),(0,a.jsx)(g,{onSendMessage:b,disabled:m,placeholder:"Ask me anything about Tap2Go..."})]}),d&&(0,a.jsxs)("div",{className:"p-4 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Chat minimized"})]}),Array.isArray(u)&&u.length>1&&(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[u.length-1," message",u.length>2?"s":""]})]})]})}):null}function h(e){let{customerInfo:t,position:r="bottom-right",autoOpen:o=!1,theme:i="orange"}=e,[l,d]=(0,s.useState)(!1),[c,u]=(0,s.useState)(!1),[m,g]=(0,s.useState)(!1),h={orange:{primary:"from-orange-500 to-orange-600",hover:"hover:from-orange-600 hover:to-orange-700",text:"text-orange-600",bg:"bg-orange-50",border:"border-orange-200"},blue:{primary:"from-blue-500 to-blue-600",hover:"hover:from-blue-600 hover:to-blue-700",text:"text-blue-600",bg:"bg-blue-50",border:"border-blue-200"},green:{primary:"from-green-500 to-green-600",hover:"hover:from-green-600 hover:to-green-700",text:"text-green-600",bg:"bg-green-50",border:"border-green-200"}}[i];(0,s.useEffect)(()=>{if(o&&!m){let e=setTimeout(()=>{d(!0)},3e3);return()=>clearTimeout(e)}},[o,m]);let f=()=>{g(!0),l?c||d(!1):d(!0),u(!1)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p,{isOpen:l,onClose:()=>{d(!1),u(!1)},onMinimize:()=>{u(!c)},isMinimized:c,position:r,customerInfo:t}),!l&&(0,a.jsxs)("div",{className:"fixed ".concat({"bottom-right":"bottom-20 right-4 max-md:bottom-20","bottom-left":"bottom-20 left-4 max-md:bottom-20"}[r]," z-[9999]"),children:[(0,a.jsxs)("button",{onClick:f,className:"w-14 h-14 bg-gradient-to-r ".concat(h.primary," ").concat(h.hover," text-white rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-300 flex items-center justify-center group relative"),title:"Open Tap2Go Assistant",style:{zIndex:9999},children:[(0,a.jsx)(n.A,{className:"w-6 h-6 group-hover:scale-110 transition-transform duration-200"}),(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center border-2 border-white",children:(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-pulse"})})]}),(0,a.jsx)("div",{className:"absolute inset-0 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 opacity-30 animate-ping"})]}),(0,a.jsx)("div",{onKeyDown:e=>{e.ctrlKey&&"/"===e.key&&(e.preventDefault(),f())},tabIndex:-1,className:"sr-only"}),(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:"\n        @keyframes chatBounce {\n          0%, 20%, 53%, 80%, 100% {\n            transform: translate3d(0,0,0);\n          }\n          40%, 43% {\n            transform: translate3d(0, -8px, 0);\n          }\n          70% {\n            transform: translate3d(0, -4px, 0);\n          }\n          90% {\n            transform: translate3d(0, -2px, 0);\n          }\n        }\n\n        .chat-bounce {\n          animation: chatBounce 2s ease-in-out infinite;\n        }\n\n        /* Custom scrollbar for chat messages */\n        .chat-messages::-webkit-scrollbar {\n          width: 4px;\n        }\n\n        .chat-messages::-webkit-scrollbar-track {\n          background: #f1f1f1;\n          border-radius: 2px;\n        }\n\n        .chat-messages::-webkit-scrollbar-thumb {\n          background: #c1c1c1;\n          border-radius: 2px;\n        }\n\n        .chat-messages::-webkit-scrollbar-thumb:hover {\n          background: #a1a1a1;\n        }\n\n        /* Smooth transitions for chat elements */\n        .chat-transition {\n          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n        }\n\n        /* Focus styles for accessibility */\n        .chat-widget button:focus {\n          outline: 2px solid #f97316;\n          outline-offset: 2px;\n        }\n\n        /* Mobile responsiveness - Full screen like Messenger */\n        @media (max-width: 767px) {\n          .chat-widget {\n            width: 100vw !important;\n            height: 100vh !important; /* Fallback for older browsers */\n            height: 100dvh !important; /* Use dynamic viewport height for mobile */\n            max-width: none !important;\n            max-height: 100vh !important; /* Fallback */\n            max-height: 100dvh !important; /* Dynamic viewport height */\n            border-radius: 0 !important;\n            position: fixed !important;\n            top: 0 !important;\n            left: 0 !important;\n            right: 0 !important;\n            bottom: 0 !important;\n            overflow: hidden !important;\n          }\n        }\n\n        /* Messenger-like smooth scrolling */\n        .chat-messages {\n          scroll-behavior: smooth;\n        }\n\n        /* Enhanced message bubble animations */\n        .message-bubble {\n          animation: messageSlideIn 0.3s ease-out;\n        }\n\n        @keyframes messageSlideIn {\n          from {\n            opacity: 0;\n            transform: translateY(10px);\n          }\n          to {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        /* Input focus effects */\n        .chat-input-container:focus-within {\n          box-shadow: 0 0 0 2px rgba(243, 168, 35, 0.2);\n        }\n      "}})]})}},30347:()=>{},52750:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var a=r(95155),s=r(12115),n=r(35695),o=r(40283);function i(e){let{isLoading:t=!1,duration:r=600,className:n=""}=e,[o,i]=(0,s.useState)(t),[l,d]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{t?(i(!0),d(!1)):(d(!0),setTimeout(()=>{i(!1),d(!1)},400))},[t]),o)?(0,a.jsx)("div",{className:"fixed inset-0 z-[9999] flex items-center justify-center transition-opacity duration-300 ".concat(l?"opacity-0":"opacity-100"," ").concat(n),style:{background:"linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)"},children:(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("div",{className:"w-20 h-20 rounded-2xl flex items-center justify-center shadow-2xl",style:{background:"linear-gradient(135deg, #f3a823 0%, #ef7b06 100%)",boxShadow:"0 20px 40px rgba(243, 168, 35, 0.3)"},children:(0,a.jsx)("span",{className:"text-white font-bold text-3xl",children:"T"})})}),(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h1",{className:"text-white text-2xl font-semibold mb-1",children:"Tap2Go"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Food Delivery"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-orange-500 rounded-full animate-pulse"}),(0,a.jsx)("div",{className:"w-2 h-2 bg-orange-500 rounded-full animate-pulse",style:{animationDelay:"0.2s"}}),(0,a.jsx)("div",{className:"w-2 h-2 bg-orange-500 rounded-full animate-pulse",style:{animationDelay:"0.4s"}})]}),(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Loading your experience..."})}),(0,a.jsxs)("div",{className:"absolute bottom-16 text-center",children:[(0,a.jsx)("p",{className:"text-gray-500 text-sm mb-2",children:"from"}),(0,a.jsx)("p",{className:"text-gray-400 text-lg font-medium",children:"Tap2Go Team"})]})]})}):null}function l(e){let{isLoading:t=!1,className:r=""}=e,[n,o]=(0,s.useState)(0),[i,l]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{if(t){l(!0),o(0);let e=setInterval(()=>{o(t=>t>=85?(clearInterval(e),85):t+20*Math.random())},100);return()=>clearInterval(e)}{o(100);let e=setTimeout(()=>{l(!1),o(0)},300);return()=>clearTimeout(e)}},[t]),i)?(0,a.jsx)("div",{className:"fixed top-0 left-0 right-0 z-[9999] h-1 ".concat(r),children:(0,a.jsx)("div",{className:"h-full bg-gradient-to-r from-orange-500 via-orange-400 to-orange-600 transition-all duration-200 ease-out",style:{width:"".concat(n,"%"),boxShadow:"0 0 8px rgba(243, 168, 35, 0.6)"}})}):null}function d(e){let{isLoading:t=!1,size:r="sm"}=e;return t?(0,a.jsx)("div",{className:"fixed top-4 right-4 z-[9999]",children:(0,a.jsx)("div",{className:"bg-white/95 backdrop-blur-sm p-2 rounded-full shadow-lg border border-gray-100",children:(0,a.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[r]," animate-spin rounded-full border-2 border-orange-500 border-t-transparent")})})}):null}let c=(0,s.createContext)(void 0);function u(e){let{children:t,variant:r="facebook",showInitialLoad:u=!0}=e,m=function(){let[e,t]=(0,s.useState)(!1),[r,a]=(0,s.useState)(!0),o=(0,n.usePathname)();return(0,s.useEffect)(()=>{r&&t(!0)},[r]),(0,s.useEffect)(()=>{},[o,r]),{isLoading:e,isInitialLoad:r,pathname:o,completeInitialLoad:()=>{t(!1),a(!1)}}}(),g=function(){let[e,t]=(0,s.useState)(!1),r=()=>t(!0),a=()=>t(!1),n=async e=>{r();try{return await e()}finally{a()}};return{isLoading:e,startLoading:r,stopLoading:a,withLoading:n}}(),p=function(){let[e,t]=(0,s.useState)(!1),r=()=>t(!0),a=()=>t(!1),n=async e=>{r();try{return await e()}finally{setTimeout(a,200)}};return{isAuthLoading:e,startAuthLoading:r,stopAuthLoading:a,withAuthLoading:n}}(),h=(0,o.A)(),f={isPageLoading:m.isLoading,isInitialLoad:m.isInitialLoad,isManualLoading:g.isLoading,startLoading:g.startLoading,stopLoading:g.stopLoading,withLoading:g.withLoading,isAuthLoading:p.isAuthLoading,startAuthLoading:p.startAuthLoading,stopAuthLoading:p.stopAuthLoading,withAuthLoading:p.withAuthLoading};s.useEffect(()=>{if(h.isInitialized&&!h.loading&&m.isInitialLoad){let e=setTimeout(()=>{m.completeInitialLoad()},300);return()=>clearTimeout(e)}},[h.isInitialized,h.loading,m.isInitialLoad,m.completeInitialLoad,m]);let x=m.isLoading||g.isLoading||p.isAuthLoading,y=u&&m.isInitialLoad&&(!h.isInitialized||h.loading);return(0,a.jsxs)(c.Provider,{value:f,children:["facebook"===r&&(0,a.jsx)(i,{isLoading:y,duration:2e3}),"progress"===r&&(0,a.jsx)(l,{isLoading:x}),"dot"===r&&(0,a.jsx)(d,{isLoading:x,size:"md"}),"minimal"===r&&x&&(0,a.jsx)("div",{className:"fixed top-4 left-4 z-[9999]",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2 bg-white/95 backdrop-blur-sm px-3 py-1.5 rounded-full shadow-sm border border-gray-100",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-orange-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-xs text-gray-600 font-medium",children:"Loading"})]})}),t]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3444,9149,4546,5458,991,2891,9806,283,1355,8441,1684,7358],()=>t(24608)),_N_E=e.O()}]);