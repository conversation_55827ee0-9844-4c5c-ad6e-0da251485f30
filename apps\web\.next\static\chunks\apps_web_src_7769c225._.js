(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/apps/web/src/lib/firebase.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Import the functions you need from the SDKs you need
__turbopack_context__.s({
    "auth": (()=>auth),
    "db": (()=>db),
    "default": (()=>__TURBOPACK__default__export__),
    "getMessagingInstance": (()=>getMessagingInstance),
    "initializeMessaging": (()=>initializeMessaging),
    "messaging": (()=>messaging),
    "storage": (()=>storage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$firebase$2f$app$2f$dist$2f$esm$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/firebase/app/dist/esm/index.esm.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$app$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@firebase/app/dist/esm/index.esm2017.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$firebase$2f$auth$2f$dist$2f$esm$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/firebase/auth/dist/esm/index.esm.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__o__as__getAuth$3e$__ = __turbopack_context__.i("[project]/node_modules/@firebase/auth/dist/esm2017/index-dfc2d82f.js [app-client] (ecmascript) <export o as getAuth>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$firebase$2f$firestore$2f$dist$2f$esm$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/firebase/firestore/dist/esm/index.esm.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/firestore/dist/index.esm2017.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$firebase$2f$storage$2f$dist$2f$esm$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/firebase/storage/dist/esm/index.esm.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$storage$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/storage/dist/index.esm2017.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$firebase$2f$messaging$2f$dist$2f$esm$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/firebase/messaging/dist/esm/index.esm.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$messaging$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/messaging/dist/esm/index.esm2017.js [app-client] (ecmascript)");
;
;
;
;
;
// Validate environment variables
const requiredEnvVars = {
    apiKey: ("TURBOPACK compile-time value", "AIzaSyB6ALvnN6aX0DMVhePhkUow9VrPauBCqgQ"),
    authDomain: ("TURBOPACK compile-time value", "tap2go-kuucn.firebaseapp.com"),
    projectId: ("TURBOPACK compile-time value", "tap2go-kuucn"),
    storageBucket: ("TURBOPACK compile-time value", "tap2go-kuucn.firebasestorage.app"),
    messagingSenderId: ("TURBOPACK compile-time value", "828629511294"),
    appId: ("TURBOPACK compile-time value", "1:828629511294:web:fae32760ca3c3afcb87c2f")
};
// Check for missing environment variables
const missingVars = Object.entries(requiredEnvVars).filter(([, value])=>!value).map(([key])=>key);
if (missingVars.length > 0) {
    throw new Error(`Missing Firebase environment variables: ${missingVars.join(', ')}`);
}
// Your web app's Firebase configuration
const firebaseConfig = {
    apiKey: requiredEnvVars.apiKey,
    authDomain: requiredEnvVars.authDomain,
    projectId: requiredEnvVars.projectId,
    storageBucket: requiredEnvVars.storageBucket,
    messagingSenderId: requiredEnvVars.messagingSenderId,
    appId: requiredEnvVars.appId
};
// Initialize Firebase
const app = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$app$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["initializeApp"])(firebaseConfig);
const auth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__o__as__getAuth$3e$__["getAuth"])(app);
const db = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFirestore"])(app);
const storage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$storage$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getStorage"])(app);
// Initialize Firebase Cloud Messaging (only in browser environment)
let messaging = null;
const initializeMessaging = async ()=>{
    if ("TURBOPACK compile-time truthy", 1) {
        try {
            const supported = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$messaging$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isSupported"])();
            if (supported) {
                messaging = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$messaging$2f$dist$2f$esm$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMessaging"])(app);
                console.log('Firebase Cloud Messaging initialized successfully');
                return messaging;
            } else {
                console.warn('Firebase Cloud Messaging is not supported in this browser');
                return null;
            }
        } catch (error) {
            console.error('Error initializing Firebase Cloud Messaging:', error);
            return null;
        }
    }
    return null;
};
const getMessagingInstance = ()=>messaging;
;
const __TURBOPACK__default__export__ = app;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/lib/database/collections.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Firebase Firestore Collection Definitions
// This file defines all collection paths and document structures
__turbopack_context__.s({
    "COLLECTIONS": (()=>COLLECTIONS),
    "generateDocId": (()=>generateDocId),
    "generateReferralCode": (()=>generateReferralCode),
    "getAdminRef": (()=>getAdminRef),
    "getAnalyticsRef": (()=>getAnalyticsRef),
    "getCollectionPath": (()=>getCollectionPath),
    "getCustomerRef": (()=>getCustomerRef),
    "getDisputeRef": (()=>getDisputeRef),
    "getDriverRef": (()=>getDriverRef),
    "getNotificationRef": (()=>getNotificationRef),
    "getOrderRef": (()=>getOrderRef),
    "getPlatformConfigRef": (()=>getPlatformConfigRef),
    "getRestaurantRef": (()=>getRestaurantRef),
    "getSubcollectionPath": (()=>getSubcollectionPath),
    "getUserRef": (()=>getUserRef),
    "getVendorRef": (()=>getVendorRef),
    "isValidPaymentStatus": (()=>isValidPaymentStatus),
    "isValidRole": (()=>isValidRole)
});
const COLLECTIONS = {
    // Top-level collections (only implemented ones)
    USERS: 'users',
    ADMINS: 'admins',
    VENDORS: 'vendors',
    CUSTOMERS: 'customers',
    DRIVERS: 'drivers',
    RESTAURANTS: 'restaurants',
    ORDERS: 'orders',
    PLATFORM_CONFIG: 'platformConfig',
    NOTIFICATIONS: 'notifications',
    DISPUTES: 'disputes',
    ANALYTICS: 'analytics',
    CATEGORIES: 'categories',
    SYSTEM_CONFIG: 'systemConfig',
    SYSTEM: '_system',
    // Subcollections (only implemented ones)
    ADMIN_ACTIONS: 'adminActions',
    // Vendor subcollections
    VENDOR_DOCUMENTS: 'documents',
    VENDOR_PAYOUTS: 'payouts',
    MODIFIER_GROUPS: 'modifierGroups',
    MASTER_MENU_ITEMS: 'masterMenuItems',
    MASTER_MENU_ASSIGNMENTS: 'masterMenuAssignments',
    VENDOR_AUDIT_LOGS: 'auditLogs',
    VENDOR_ANALYTICS: 'analytics',
    // Restaurant subcollections
    MENU_CATEGORIES: 'menuCategories',
    MENU_ITEMS: 'menuItems',
    RESTAURANT_PROMOTIONS: 'promotions',
    RESTAURANT_REVIEWS: 'reviews',
    // Customer subcollections
    CUSTOMER_ADDRESSES: 'addresses',
    CUSTOMER_PAYMENT_METHODS: 'paymentMethods',
    CUSTOMER_FAVORITES: 'favorites',
    CUSTOMER_CART: 'cart',
    // Driver subcollections
    DRIVER_EARNINGS: 'earnings',
    DRIVER_REVIEWS: 'reviews',
    DRIVER_DELIVERY_HISTORY: 'deliveryHistory'
};
const generateDocId = ()=>{
    return Date.now().toString() + Math.random().toString(36).substring(2, 11);
};
const generateReferralCode = ()=>{
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for(let i = 0; i < 8; i++){
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
};
const getCollectionPath = (collection)=>{
    return COLLECTIONS[collection];
};
const getSubcollectionPath = (parentCollection, parentDocId, subcollection)=>{
    return `${COLLECTIONS[parentCollection]}/${parentDocId}/${COLLECTIONS[subcollection]}`;
};
const getUserRef = (uid)=>`users/${uid}`;
const getAdminRef = (uid)=>`admins/${uid}`;
const getVendorRef = (uid)=>`vendors/${uid}`;
const getCustomerRef = (uid)=>`customers/${uid}`;
const getDriverRef = (uid)=>`drivers/${uid}`;
const getRestaurantRef = (restaurantId)=>`restaurants/${restaurantId}`;
const getOrderRef = (orderId)=>`orders/${orderId}`;
const getPlatformConfigRef = ()=>`platformConfig/config`;
const getNotificationRef = (notificationId)=>`notifications/${notificationId}`;
const getDisputeRef = (disputeId)=>`disputes/${disputeId}`;
const getAnalyticsRef = (analyticsId)=>`analytics/${analyticsId}`;
const isValidRole = (role)=>{
    return [
        'admin',
        'vendor',
        'customer',
        'driver'
    ].includes(role);
};
const isValidPaymentStatus = (status)=>{
    return [
        'pending',
        'paid',
        'failed',
        'refunded'
    ].includes(status);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/lib/database/users.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addFCMToken": (()=>addFCMToken),
    "checkAdminExists": (()=>checkAdminExists),
    "checkUserExists": (()=>checkUserExists),
    "createAdmin": (()=>createAdmin),
    "createAdminAction": (()=>createAdminAction),
    "createUser": (()=>createUser),
    "createUserWithRole": (()=>createUserWithRole),
    "getAdmin": (()=>getAdmin),
    "getAdminActions": (()=>getAdminActions),
    "getAdminsByAccessLevel": (()=>getAdminsByAccessLevel),
    "getAdminsByDepartment": (()=>getAdminsByDepartment),
    "getUser": (()=>getUser),
    "getUsersByRole": (()=>getUsersByRole),
    "hasAdminPermission": (()=>hasAdminPermission),
    "isUserActive": (()=>isUserActive),
    "isUserVerified": (()=>isUserVerified),
    "promoteUserToAdmin": (()=>promoteUserToAdmin),
    "removeFCMToken": (()=>removeFCMToken),
    "searchUsersByEmail": (()=>searchUsersByEmail),
    "updateAdmin": (()=>updateAdmin),
    "updateUser": (()=>updateUser),
    "updateUserLastLogin": (()=>updateUserLastLogin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$firebase$2f$firestore$2f$dist$2f$esm$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/firebase/firestore/dist/esm/index.esm.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/firestore/dist/index.esm2017.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/lib/firebase.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$collections$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/lib/database/collections.ts [app-client] (ecmascript)");
;
;
;
const createUser = async (uid, userData)=>{
    const userRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["db"], __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$collections$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLLECTIONS"].USERS, uid);
    const userDoc = {
        uid,
        ...userData,
        createdAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serverTimestamp"])(),
        updatedAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serverTimestamp"])()
    };
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setDoc"])(userRef, userDoc);
};
const getUser = async (uid)=>{
    const userRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["db"], __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$collections$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLLECTIONS"].USERS, uid);
    const userSnap = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDoc"])(userRef);
    if (userSnap.exists()) {
        return userSnap.data();
    }
    return null;
};
const updateUser = async (uid, updates)=>{
    const userRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["db"], __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$collections$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLLECTIONS"].USERS, uid);
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateDoc"])(userRef, {
        ...updates,
        updatedAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serverTimestamp"])()
    });
};
const updateUserLastLogin = async (uid)=>{
    const userRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["db"], __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$collections$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLLECTIONS"].USERS, uid);
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateDoc"])(userRef, {
        lastLoginAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serverTimestamp"])(),
        updatedAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serverTimestamp"])()
    });
};
const addFCMToken = async (uid, token)=>{
    const user = await getUser(uid);
    if (!user) throw new Error('User not found');
    const currentTokens = user.fcmTokens || [];
    if (!currentTokens.includes(token)) {
        await updateUser(uid, {
            fcmTokens: [
                ...currentTokens,
                token
            ]
        });
    }
};
const removeFCMToken = async (uid, token)=>{
    const user = await getUser(uid);
    if (!user) throw new Error('User not found');
    const currentTokens = user.fcmTokens || [];
    await updateUser(uid, {
        fcmTokens: currentTokens.filter((t)=>t !== token)
    });
};
const getUsersByRole = async (role)=>{
    const usersRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["db"], __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$collections$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLLECTIONS"].USERS);
    const q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["query"])(usersRef, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["where"])('role', '==', role));
    const querySnapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDocs"])(q);
    return querySnapshot.docs.map((doc)=>doc.data());
};
const searchUsersByEmail = async (email)=>{
    const usersRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["db"], __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$collections$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLLECTIONS"].USERS);
    const q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["query"])(usersRef, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["where"])('email', '==', email));
    const querySnapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDocs"])(q);
    return querySnapshot.docs.map((doc)=>doc.data());
};
const createAdmin = async (uid, adminData)=>{
    const adminRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["db"], __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$collections$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLLECTIONS"].ADMINS, uid);
    const adminDoc = {
        ...adminData,
        createdAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serverTimestamp"])(),
        updatedAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serverTimestamp"])()
    };
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setDoc"])(adminRef, adminDoc);
};
const getAdmin = async (uid)=>{
    const adminRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["db"], __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$collections$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLLECTIONS"].ADMINS, uid);
    const adminSnap = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDoc"])(adminRef);
    if (adminSnap.exists()) {
        return adminSnap.data();
    }
    return null;
};
const updateAdmin = async (uid, updates)=>{
    const adminRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["db"], __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$collections$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLLECTIONS"].ADMINS, uid);
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateDoc"])(adminRef, {
        ...updates,
        updatedAt: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serverTimestamp"])()
    });
};
const getAdminsByDepartment = async (department)=>{
    const adminsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["db"], __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$collections$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLLECTIONS"].ADMINS);
    const q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["query"])(adminsRef, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["where"])('department', '==', department));
    const querySnapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDocs"])(q);
    return querySnapshot.docs.map((doc)=>doc.data());
};
const getAdminsByAccessLevel = async (accessLevel)=>{
    const adminsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["db"], __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$collections$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLLECTIONS"].ADMINS);
    const q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["query"])(adminsRef, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["where"])('accessLevel', '==', accessLevel));
    const querySnapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDocs"])(q);
    return querySnapshot.docs.map((doc)=>doc.data());
};
const createAdminAction = async (adminUid, actionData)=>{
    const actionsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["db"], __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$collections$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLLECTIONS"].ADMINS, adminUid, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$collections$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLLECTIONS"].ADMIN_ACTIONS);
    const actionDoc = {
        ...actionData,
        timestamp: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serverTimestamp"])()
    };
    const docRef = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addDoc"])(actionsRef, actionDoc);
    // Update the document with its own ID
    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateDoc"])(docRef, {
        actionId: docRef.id
    });
    return docRef.id;
};
const getAdminActions = async (adminUid, actionType)=>{
    const actionsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["db"], __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$collections$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLLECTIONS"].ADMINS, adminUid, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$collections$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["COLLECTIONS"].ADMIN_ACTIONS);
    let q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["query"])(actionsRef);
    if (actionType) {
        q = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["query"])(actionsRef, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["where"])('actionType', '==', actionType));
    }
    const querySnapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDocs"])(q);
    return querySnapshot.docs.map((doc)=>doc.data());
};
const checkUserExists = async (uid)=>{
    const user = await getUser(uid);
    return user !== null;
};
const checkAdminExists = async (uid)=>{
    const admin = await getAdmin(uid);
    return admin !== null;
};
const isUserActive = async (uid)=>{
    const user = await getUser(uid);
    return user?.isActive === true;
};
const isUserVerified = async (uid)=>{
    const user = await getUser(uid);
    return user?.isVerified === true;
};
const hasAdminPermission = async (adminUid, permission)=>{
    const admin = await getAdmin(adminUid);
    return admin?.permissions.includes(permission) === true;
};
const createUserWithRole = async (uid, email, role, additionalData = {})=>{
    const userData = {
        email,
        role,
        isActive: true,
        isVerified: false,
        ...additionalData
    };
    await createUser(uid, userData);
};
const promoteUserToAdmin = async (uid, adminData)=>{
    // Update user role
    await updateUser(uid, {
        role: 'admin'
    });
    // Create admin document
    await createAdmin(uid, {
        ...adminData,
        userRef: `users/${uid}`
    });
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/contexts/AuthContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$firebase$2f$auth$2f$dist$2f$esm$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/firebase/auth/dist/esm/index.esm.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__ab__as__signInWithEmailAndPassword$3e$__ = __turbopack_context__.i("[project]/node_modules/@firebase/auth/dist/esm2017/index-dfc2d82f.js [app-client] (ecmascript) <export ab as signInWithEmailAndPassword>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__aa__as__createUserWithEmailAndPassword$3e$__ = __turbopack_context__.i("[project]/node_modules/@firebase/auth/dist/esm2017/index-dfc2d82f.js [app-client] (ecmascript) <export aa as createUserWithEmailAndPassword>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__signOut$3e$__ = __turbopack_context__.i("[project]/node_modules/@firebase/auth/dist/esm2017/index-dfc2d82f.js [app-client] (ecmascript) <export C as signOut>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__y__as__onAuthStateChanged$3e$__ = __turbopack_context__.i("[project]/node_modules/@firebase/auth/dist/esm2017/index-dfc2d82f.js [app-client] (ecmascript) <export y as onAuthStateChanged>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__ak__as__updateProfile$3e$__ = __turbopack_context__.i("[project]/node_modules/@firebase/auth/dist/esm2017/index-dfc2d82f.js [app-client] (ecmascript) <export ak as updateProfile>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__an__as__getIdToken$3e$__ = __turbopack_context__.i("[project]/node_modules/@firebase/auth/dist/esm2017/index-dfc2d82f.js [app-client] (ecmascript) <export an as getIdToken>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__X__as__GoogleAuthProvider$3e$__ = __turbopack_context__.i("[project]/node_modules/@firebase/auth/dist/esm2017/index-dfc2d82f.js [app-client] (ecmascript) <export X as GoogleAuthProvider>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__c__as__signInWithPopup$3e$__ = __turbopack_context__.i("[project]/node_modules/@firebase/auth/dist/esm2017/index-dfc2d82f.js [app-client] (ecmascript) <export c as signInWithPopup>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/lib/firebase.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$users$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/lib/database/users.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Enterprise-grade auth state management
const AUTH_STATE_KEY = 'tap2go_auth_initialized';
const TOKEN_REFRESH_INTERVAL = 50 * 60 * 1000; // 50 minutes
const SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours
// Secure session storage (httpOnly would be better, but this is client-side)
const getAuthSession = ()=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    try {
        const session = sessionStorage.getItem(AUTH_STATE_KEY);
        if (session) {
            const parsed = JSON.parse(session);
            // Check if session is still valid (not expired)
            if (Date.now() - parsed.timestamp < SESSION_TIMEOUT) {
                return parsed;
            }
        }
    } catch (error) {
        console.error('Error reading auth session:', error);
    }
    return {
        initialized: false,
        timestamp: 0
    };
};
const setAuthSession = (initialized)=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    try {
        if (initialized) {
            sessionStorage.setItem(AUTH_STATE_KEY, JSON.stringify({
                initialized: true,
                timestamp: Date.now()
            }));
        } else {
            sessionStorage.removeItem(AUTH_STATE_KEY);
        }
    } catch (error) {
        console.error('Error setting auth session:', error);
    }
};
// Multi-tab synchronization
const broadcastAuthChange = (user)=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    try {
        const event = new CustomEvent('tap2go-auth-change', {
            detail: {
                user,
                timestamp: Date.now()
            }
        });
        window.dispatchEvent(event);
    } catch (error) {
        console.error('Error broadcasting auth change:', error);
    }
};
function useAuth() {
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
_s(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function AuthProvider({ children }) {
    _s1();
    // Enterprise-grade state management
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [isInitialized, setIsInitialized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [authError, setAuthError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isHydrated, setIsHydrated] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Refs for cleanup and token management
    const tokenRefreshInterval = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const authStateUnsubscribe = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const tokenChangeUnsubscribe = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // SSR-safe optimistic auth state (only set after hydration)
    const [showOptimisticAuth, setShowOptimisticAuth] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Handle hydration and set optimistic state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            setIsHydrated(true);
            const session = getAuthSession();
            if (session.initialized) {
                setShowOptimisticAuth(true);
            }
        }
    }["AuthProvider.useEffect"], []);
    // Enterprise-grade token refresh
    const setupTokenRefresh = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AuthProvider.useCallback[setupTokenRefresh]": async (firebaseUser)=>{
            if (tokenRefreshInterval.current) {
                clearInterval(tokenRefreshInterval.current);
            }
            tokenRefreshInterval.current = setInterval({
                "AuthProvider.useCallback[setupTokenRefresh]": async ()=>{
                    try {
                        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__an__as__getIdToken$3e$__["getIdToken"])(firebaseUser, true); // Force refresh
                        console.log('Token refreshed successfully');
                    } catch (error) {
                        console.error('Token refresh failed:', error);
                        setAuthError('Session expired. Please sign in again.');
                    }
                }
            }["AuthProvider.useCallback[setupTokenRefresh]"], TOKEN_REFRESH_INTERVAL);
        }
    }["AuthProvider.useCallback[setupTokenRefresh]"], []);
    // Clean up intervals
    const cleanupTokenRefresh = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AuthProvider.useCallback[cleanupTokenRefresh]": ()=>{
            if (tokenRefreshInterval.current) {
                clearInterval(tokenRefreshInterval.current);
                tokenRefreshInterval.current = null;
            }
        }
    }["AuthProvider.useCallback[cleanupTokenRefresh]"], []);
    // Handle user data loading
    const handleUserLoad = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AuthProvider.useCallback[handleUserLoad]": async (firebaseUser)=>{
            try {
                let userData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$users$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getUser"])(firebaseUser.uid);
                // If user doesn't exist, create them (happens with social auth)
                if (!userData) {
                    const providerData = firebaseUser.providerData[0];
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$users$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createUser"])(firebaseUser.uid, {
                        email: firebaseUser.email || '',
                        role: 'customer',
                        isActive: true,
                        isVerified: firebaseUser.emailVerified
                    });
                    // Fetch the newly created user
                    userData = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$users$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getUser"])(firebaseUser.uid);
                }
                if (userData) {
                    const userObj = {
                        id: firebaseUser.uid,
                        email: firebaseUser.email,
                        role: userData.role,
                        phone: userData.phoneNumber,
                        isActive: userData.isActive,
                        isVerified: userData.isVerified,
                        createdAt: userData.createdAt?.toDate(),
                        updatedAt: userData.updatedAt?.toDate()
                    };
                    // Update last login time in background
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$users$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateUserLastLogin"])(firebaseUser.uid).catch(console.error);
                    return userObj;
                }
            } catch (error) {
                console.error('Error loading user data:', error);
                setAuthError('Failed to load user data');
            }
            return null;
        }
    }["AuthProvider.useCallback[handleUserLoad]"], []);
    // Main auth state listener
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            let mounted = true;
            const unsubscribe = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__y__as__onAuthStateChanged$3e$__["onAuthStateChanged"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["auth"], {
                "AuthProvider.useEffect.unsubscribe": async (firebaseUser)=>{
                    if (!mounted) return;
                    try {
                        setAuthError(null);
                        if (firebaseUser) {
                            const userObj = await handleUserLoad(firebaseUser);
                            if (mounted && userObj) {
                                setUser(userObj);
                                setAuthSession(true);
                                setupTokenRefresh(firebaseUser);
                                broadcastAuthChange(userObj);
                            } else if (mounted) {
                                setUser(null);
                                setAuthSession(false);
                                cleanupTokenRefresh();
                                broadcastAuthChange(null);
                            }
                        } else {
                            if (mounted) {
                                setUser(null);
                                setAuthSession(false);
                                cleanupTokenRefresh();
                                broadcastAuthChange(null);
                            }
                        }
                    } catch (error) {
                        console.error('Auth state change error:', error);
                        if (mounted) {
                            setUser(null);
                            setAuthError('Authentication error occurred');
                            setAuthSession(false);
                            cleanupTokenRefresh();
                        }
                    } finally{
                        if (mounted) {
                            setLoading(false);
                            setIsInitialized(true);
                            // Only clear optimistic auth after real auth state is determined
                            if (isHydrated) {
                                setShowOptimisticAuth(false);
                            }
                        }
                    }
                }
            }["AuthProvider.useEffect.unsubscribe"]);
            authStateUnsubscribe.current = unsubscribe;
            return ({
                "AuthProvider.useEffect": ()=>{
                    mounted = false;
                    unsubscribe();
                    cleanupTokenRefresh();
                }
            })["AuthProvider.useEffect"];
        }
    }["AuthProvider.useEffect"], [
        handleUserLoad,
        setupTokenRefresh,
        cleanupTokenRefresh,
        isHydrated
    ]);
    const signIn = async (email, password)=>{
        setLoading(true);
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__ab__as__signInWithEmailAndPassword$3e$__["signInWithEmailAndPassword"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["auth"], email, password);
        } catch (error) {
            setLoading(false);
            throw error;
        }
    };
    const signUp = async (email, password, name, role)=>{
        setLoading(true);
        try {
            const { user: firebaseUser } = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__aa__as__createUserWithEmailAndPassword$3e$__["createUserWithEmailAndPassword"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["auth"], email, password);
            // Update Firebase Auth profile
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__ak__as__updateProfile$3e$__["updateProfile"])(firebaseUser, {
                displayName: name
            });
            // Create user document in Firestore using new database functions
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$users$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createUser"])(firebaseUser.uid, {
                email,
                role,
                isActive: true,
                isVerified: false
            });
        } catch (error) {
            setLoading(false);
            throw error;
        }
    };
    // Multi-tab auth synchronization
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            const handleAuthChange = {
                "AuthProvider.useEffect.handleAuthChange": (event)=>{
                    const { user: newUser, timestamp } = event.detail;
                    // Only update if this is a newer change (prevent loops)
                    if (timestamp > Date.now() - 1000) {
                        setUser(newUser);
                    }
                }
            }["AuthProvider.useEffect.handleAuthChange"];
            window.addEventListener('tap2go-auth-change', handleAuthChange);
            return ({
                "AuthProvider.useEffect": ()=>{
                    window.removeEventListener('tap2go-auth-change', handleAuthChange);
                }
            })["AuthProvider.useEffect"];
        }
    }["AuthProvider.useEffect"], []);
    // Enhanced sign out with proper cleanup
    // Google Sign-In
    const signInWithGoogle = async ()=>{
        setLoading(true);
        try {
            const provider = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__X__as__GoogleAuthProvider$3e$__["GoogleAuthProvider"]();
            // Optional: Add scopes for additional permissions
            provider.addScope('profile');
            provider.addScope('email');
            // Use popup for better UX (can also use signInWithRedirect)
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__c__as__signInWithPopup$3e$__["signInWithPopup"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["auth"], provider);
        // Auth state change will be handled by onAuthStateChanged listener
        } catch (error) {
            setLoading(false);
            throw error;
        }
    };
    const signOut = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AuthProvider.useCallback[signOut]": async ()=>{
            try {
                setLoading(true);
                cleanupTokenRefresh();
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$auth$2f$dist$2f$esm2017$2f$index$2d$dfc2d82f$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__C__as__signOut$3e$__["signOut"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["auth"]);
                setAuthSession(false);
                broadcastAuthChange(null);
            } catch (error) {
                console.error('Error signing out:', error);
                throw error;
            } finally{
                setLoading(false);
            }
        }
    }["AuthProvider.useCallback[signOut]"], [
        cleanupTokenRefresh
    ]);
    // Enhanced profile update
    const updateProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "AuthProvider.useCallback[updateProfile]": async (data)=>{
            if (!user) throw new Error('No user logged in');
            try {
                await (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$lib$2f$database$2f$users$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateUser"])(user.id, {
                    phoneNumber: data.phone,
                    profileImageUrl: data.profileImage
                });
                const updatedUser = {
                    ...user,
                    ...data,
                    updatedAt: new Date()
                };
                setUser(updatedUser);
                broadcastAuthChange(updatedUser);
            } catch (error) {
                throw error;
            }
        }
    }["AuthProvider.useCallback[updateProfile]"], [
        user
    ]);
    // Enhanced context value with error state
    const value = {
        user,
        loading,
        signIn,
        signUp,
        signInWithGoogle,
        signOut,
        updateProfile,
        authError,
        isInitialized
    };
    // FAST LOADING: Never block the entire app - always render children
    // Let individual components handle their own auth states
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/apps/web/src/contexts/AuthContext.tsx",
        lineNumber: 362,
        columnNumber: 5
    }, this);
}
_s1(AuthProvider, "sKBSone4ZIVPocZuszrNRfr1dP4=");
_c = AuthProvider;
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/contexts/CartContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "CartProvider": (()=>CartProvider),
    "useCart": (()=>useCart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
const CartContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function useCart() {
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(CartContext);
    if (context === undefined) {
        throw new Error('useCart must be used within a CartProvider');
    }
    return context;
}
_s(useCart, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function cartReducer(state, action) {
    switch(action.type){
        case 'ADD_ITEM':
            {
                const { item, quantity, specialInstructions } = action.payload;
                if (!state || state.restaurantId !== item.restaurantId) {
                    // Create new cart or replace if different restaurant
                    const newCartItem = {
                        id: `${item.id}-${Date.now()}`,
                        menuItem: item,
                        quantity,
                        specialInstructions,
                        totalPrice: item.price * quantity
                    };
                    const subtotal = newCartItem.totalPrice;
                    const deliveryFee = 5.99; // Default delivery fee
                    const tax = subtotal * 0.08; // 8% tax
                    return {
                        items: [
                            newCartItem
                        ],
                        restaurantId: item.restaurantId,
                        subtotal,
                        deliveryFee,
                        tax,
                        total: subtotal + deliveryFee + tax
                    };
                }
                // Add to existing cart
                const existingItemIndex = state.items.findIndex((cartItem)=>cartItem.menuItem.id === item.id && cartItem.specialInstructions === specialInstructions);
                let newItems;
                if (existingItemIndex >= 0) {
                    // Update existing item
                    newItems = state.items.map((cartItem, index)=>index === existingItemIndex ? {
                            ...cartItem,
                            quantity: cartItem.quantity + quantity,
                            totalPrice: (cartItem.quantity + quantity) * cartItem.menuItem.price
                        } : cartItem);
                } else {
                    // Add new item
                    const newCartItem = {
                        id: `${item.id}-${Date.now()}`,
                        menuItem: item,
                        quantity,
                        specialInstructions,
                        totalPrice: item.price * quantity
                    };
                    newItems = [
                        ...state.items,
                        newCartItem
                    ];
                }
                const subtotal = newItems.reduce((sum, item)=>sum + item.totalPrice, 0);
                const tax = subtotal * 0.08;
                return {
                    ...state,
                    items: newItems,
                    subtotal,
                    tax,
                    total: subtotal + state.deliveryFee + tax
                };
            }
        case 'REMOVE_ITEM':
            {
                if (!state) return null;
                const newItems = state.items.filter((item)=>item.id !== action.payload.itemId);
                if (newItems.length === 0) {
                    return null;
                }
                const subtotal = newItems.reduce((sum, item)=>sum + item.totalPrice, 0);
                const tax = subtotal * 0.08;
                return {
                    ...state,
                    items: newItems,
                    subtotal,
                    tax,
                    total: subtotal + state.deliveryFee + tax
                };
            }
        case 'UPDATE_QUANTITY':
            {
                if (!state) return null;
                const { itemId, quantity } = action.payload;
                if (quantity <= 0) {
                    return cartReducer(state, {
                        type: 'REMOVE_ITEM',
                        payload: {
                            itemId
                        }
                    });
                }
                const newItems = state.items.map((item)=>item.id === itemId ? {
                        ...item,
                        quantity,
                        totalPrice: item.menuItem.price * quantity
                    } : item);
                const subtotal = newItems.reduce((sum, item)=>sum + item.totalPrice, 0);
                const tax = subtotal * 0.08;
                return {
                    ...state,
                    items: newItems,
                    subtotal,
                    tax,
                    total: subtotal + state.deliveryFee + tax
                };
            }
        case 'CLEAR_CART':
            return null;
        case 'LOAD_CART':
            return action.payload;
        default:
            return state;
    }
}
function CartProvider({ children }) {
    _s1();
    const [cart, dispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useReducer"])(cartReducer, null);
    // Load cart from localStorage on mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CartProvider.useEffect": ()=>{
            const savedCart = localStorage.getItem('cart');
            if (savedCart) {
                try {
                    const parsedCart = JSON.parse(savedCart);
                    dispatch({
                        type: 'LOAD_CART',
                        payload: parsedCart
                    });
                } catch (error) {
                    console.error('Error loading cart from localStorage:', error);
                }
            }
        }
    }["CartProvider.useEffect"], []);
    // Save cart to localStorage whenever it changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CartProvider.useEffect": ()=>{
            if (cart) {
                localStorage.setItem('cart', JSON.stringify(cart));
            } else {
                localStorage.removeItem('cart');
            }
        }
    }["CartProvider.useEffect"], [
        cart
    ]);
    const addToCart = (item, quantity, specialInstructions)=>{
        dispatch({
            type: 'ADD_ITEM',
            payload: {
                item,
                quantity,
                specialInstructions
            }
        });
    };
    const removeFromCart = (itemId)=>{
        dispatch({
            type: 'REMOVE_ITEM',
            payload: {
                itemId
            }
        });
    };
    const updateQuantity = (itemId, quantity)=>{
        dispatch({
            type: 'UPDATE_QUANTITY',
            payload: {
                itemId,
                quantity
            }
        });
    };
    const clearCart = ()=>{
        dispatch({
            type: 'CLEAR_CART'
        });
    };
    const getCartTotal = ()=>{
        return cart?.total || 0;
    };
    const value = {
        cart,
        addToCart,
        removeFromCart,
        updateQuantity,
        clearCart,
        getCartTotal
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CartContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/apps/web/src/contexts/CartContext.tsx",
        lineNumber: 216,
        columnNumber: 5
    }, this);
}
_s1(CartProvider, "t1Wr/BM6S6owD+SoJUaFO+5sBIA=");
_c = CartProvider;
var _c;
__turbopack_context__.k.register(_c, "CartProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/hooks/usePageLoading.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useAuthLoading": (()=>useAuthLoading),
    "useManualLoading": (()=>useManualLoading),
    "usePageLoading": (()=>usePageLoading)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/navigation.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
'use client';
;
;
function usePageLoading() {
    _s();
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isInitialLoad, setIsInitialLoad] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    // Track initial page load - WAIT FOR AUTH RESOLUTION, NOT ARBITRARY TIMING
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "usePageLoading.useEffect": ()=>{
            if (isInitialLoad) {
                setIsLoading(true);
            // Don't set arbitrary timeout - let LoadingProvider control when to hide splash
            // based on actual auth state resolution
            }
        }
    }["usePageLoading.useEffect"], [
        isInitialLoad
    ]);
    // Method to manually complete initial load (called by LoadingProvider)
    const completeInitialLoad = ()=>{
        setIsLoading(false);
        setIsInitialLoad(false);
    };
    // Track route changes - NO LOADING for route changes to maintain speed
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "usePageLoading.useEffect": ()=>{
            if (!isInitialLoad) {
            // No loading indicator for route changes - instant navigation
            // This maintains the lightning-fast feel
            }
        }
    }["usePageLoading.useEffect"], [
        pathname,
        isInitialLoad
    ]);
    return {
        isLoading,
        isInitialLoad,
        pathname,
        completeInitialLoad
    };
}
_s(usePageLoading, "Cdlfn3365YPEm16CuYh53NzJZGQ=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
function useManualLoading() {
    _s1();
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const startLoading = ()=>setIsLoading(true);
    const stopLoading = ()=>setIsLoading(false);
    const withLoading = async (asyncFn)=>{
        startLoading();
        try {
            const result = await asyncFn();
            return result;
        } finally{
            stopLoading();
        }
    };
    return {
        isLoading,
        startLoading,
        stopLoading,
        withLoading
    };
}
_s1(useManualLoading, "EmvgwIb3cHpoFpeP+WmEDbjx4y4=");
function useAuthLoading() {
    _s2();
    const [isAuthLoading, setIsAuthLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const startAuthLoading = ()=>setIsAuthLoading(true);
    const stopAuthLoading = ()=>setIsAuthLoading(false);
    const withAuthLoading = async (asyncFn)=>{
        startAuthLoading();
        try {
            const result = await asyncFn();
            return result;
        } finally{
            // Small delay to show the loading state briefly
            setTimeout(stopAuthLoading, 200);
        }
    };
    return {
        isAuthLoading,
        startAuthLoading,
        stopAuthLoading,
        withAuthLoading
    };
}
_s2(useAuthLoading, "DTsFLCDxHOBG8bYLQh7U36dbCSM=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "LoadingDot": (()=>LoadingDot),
    "TopProgressBar": (()=>TopProgressBar),
    "default": (()=>FacebookStyleSplash)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
function FacebookStyleSplash({ isLoading = false, duration: _duration = 600, className = '' }) {
    _s();
    const [show, setShow] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(isLoading);
    const [fadeOut, setFadeOut] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FacebookStyleSplash.useEffect": ()=>{
            if (isLoading) {
                setShow(true);
                setFadeOut(false);
            // Don't auto-hide - wait for isLoading to become false
            // This ensures we show until auth is fully resolved
            } else {
                // Only hide when loading is explicitly set to false
                setFadeOut(true);
                setTimeout({
                    "FacebookStyleSplash.useEffect": ()=>{
                        setShow(false);
                        setFadeOut(false);
                    }
                }["FacebookStyleSplash.useEffect"], 400); // Slightly longer fade for smoother transition
            }
        }
    }["FacebookStyleSplash.useEffect"], [
        isLoading
    ]);
    if (!show) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `fixed inset-0 z-[9999] flex items-center justify-center transition-opacity duration-300 ${fadeOut ? 'opacity-0' : 'opacity-100'} ${className}`,
        style: {
            background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)'
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex flex-col items-center",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "w-20 h-20 rounded-2xl flex items-center justify-center shadow-2xl",
                        style: {
                            background: 'linear-gradient(135deg, #f3a823 0%, #ef7b06 100%)',
                            boxShadow: '0 20px 40px rgba(243, 168, 35, 0.3)'
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-white font-bold text-3xl",
                            children: "T"
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
                            lineNumber: 64,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
                        lineNumber: 57,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
                    lineNumber: 56,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center mb-12",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-white text-2xl font-semibold mb-1",
                            children: "Tap2Go"
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
                            lineNumber: 70,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-400 text-sm",
                            children: "Food Delivery"
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
                            lineNumber: 71,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
                    lineNumber: 69,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center space-x-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-2 h-2 bg-orange-500 rounded-full animate-pulse"
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
                            lineNumber: 76,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-2 h-2 bg-orange-500 rounded-full animate-pulse",
                            style: {
                                animationDelay: '0.2s'
                            }
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
                            lineNumber: 77,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-2 h-2 bg-orange-500 rounded-full animate-pulse",
                            style: {
                                animationDelay: '0.4s'
                            }
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
                            lineNumber: 78,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
                    lineNumber: 75,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mt-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-gray-400 text-sm",
                        children: "Loading your experience..."
                    }, void 0, false, {
                        fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
                        lineNumber: 83,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
                    lineNumber: 82,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "absolute bottom-16 text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-500 text-sm mb-2",
                            children: "from"
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
                            lineNumber: 88,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-400 text-lg font-medium",
                            children: "Tap2Go Team"
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
                            lineNumber: 89,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
                    lineNumber: 87,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
            lineNumber: 54,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
        lineNumber: 45,
        columnNumber: 5
    }, this);
}
_s(FacebookStyleSplash, "WLhZQZCmfPIA5YXy/mlCJXlj5+g=");
_c = FacebookStyleSplash;
function TopProgressBar({ isLoading = false, className = '' }) {
    _s1();
    const [progress, setProgress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [show, setShow] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TopProgressBar.useEffect": ()=>{
            if (isLoading) {
                setShow(true);
                setProgress(0);
                // Fast progress animation
                const progressInterval = setInterval({
                    "TopProgressBar.useEffect.progressInterval": ()=>{
                        setProgress({
                            "TopProgressBar.useEffect.progressInterval": (prev)=>{
                                if (prev >= 85) {
                                    clearInterval(progressInterval);
                                    return 85;
                                }
                                return prev + Math.random() * 20;
                            }
                        }["TopProgressBar.useEffect.progressInterval"]);
                    }
                }["TopProgressBar.useEffect.progressInterval"], 100);
                return ({
                    "TopProgressBar.useEffect": ()=>clearInterval(progressInterval)
                })["TopProgressBar.useEffect"];
            } else {
                setProgress(100);
                const hideTimer = setTimeout({
                    "TopProgressBar.useEffect.hideTimer": ()=>{
                        setShow(false);
                        setProgress(0);
                    }
                }["TopProgressBar.useEffect.hideTimer"], 300);
                return ({
                    "TopProgressBar.useEffect": ()=>clearTimeout(hideTimer)
                })["TopProgressBar.useEffect"];
            }
        }
    }["TopProgressBar.useEffect"], [
        isLoading
    ]);
    if (!show) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `fixed top-0 left-0 right-0 z-[9999] h-1 ${className}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "h-full bg-gradient-to-r from-orange-500 via-orange-400 to-orange-600 transition-all duration-200 ease-out",
            style: {
                width: `${progress}%`,
                boxShadow: '0 0 8px rgba(243, 168, 35, 0.6)'
            }
        }, void 0, false, {
            fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
            lineNumber: 138,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
        lineNumber: 137,
        columnNumber: 5
    }, this);
}
_s1(TopProgressBar, "hAHCe5FQ4bxoQpx1BPYiSTSfoEM=");
_c1 = TopProgressBar;
function LoadingDot({ isLoading = false, size = 'sm' }) {
    if (!isLoading) return null;
    const sizeClasses = {
        sm: 'w-4 h-4',
        md: 'w-6 h-6',
        lg: 'w-8 h-8'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed top-4 right-4 z-[9999]",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white/95 backdrop-blur-sm p-2 rounded-full shadow-lg border border-gray-100",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `${sizeClasses[size]} animate-spin rounded-full border-2 border-orange-500 border-t-transparent`
            }, void 0, false, {
                fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
                lineNumber: 167,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
            lineNumber: 166,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx",
        lineNumber: 165,
        columnNumber: 5
    }, this);
}
_c2 = LoadingDot;
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "FacebookStyleSplash");
__turbopack_context__.k.register(_c1, "TopProgressBar");
__turbopack_context__.k.register(_c2, "LoadingDot");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/components/loading/LoadingProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "LoadingWrapper": (()=>LoadingWrapper),
    "QuickLoadingDot": (()=>QuickLoadingDot),
    "QuickProgressBar": (()=>QuickProgressBar),
    "default": (()=>LoadingProvider),
    "useLoading": (()=>useLoading)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$hooks$2f$usePageLoading$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/hooks/usePageLoading.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$components$2f$loading$2f$PageLoadingIndicator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/components/loading/PageLoadingIndicator.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
const LoadingContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function useLoading() {
    _s();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(LoadingContext);
    if (context === undefined) {
        throw new Error('useLoading must be used within a LoadingProvider');
    }
    return context;
}
_s(useLoading, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function LoadingProvider({ children, variant = 'facebook', showInitialLoad = true }) {
    _s1();
    const pageLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$hooks$2f$usePageLoading$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePageLoading"])();
    const manualLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$hooks$2f$usePageLoading$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useManualLoading"])();
    const authLoading = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$hooks$2f$usePageLoading$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthLoading"])();
    const auth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const value = {
        // Page loading
        isPageLoading: pageLoading.isLoading,
        isInitialLoad: pageLoading.isInitialLoad,
        // Manual loading
        isManualLoading: manualLoading.isLoading,
        startLoading: manualLoading.startLoading,
        stopLoading: manualLoading.stopLoading,
        withLoading: manualLoading.withLoading,
        // Auth loading
        isAuthLoading: authLoading.isAuthLoading,
        startAuthLoading: authLoading.startAuthLoading,
        stopAuthLoading: authLoading.stopAuthLoading,
        withAuthLoading: authLoading.withAuthLoading
    };
    // PROFESSIONAL AUTH-AWARE SPLASH SCREEN LOGIC
    // Wait for ACTUAL auth resolution, not arbitrary timing
    __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].useEffect({
        "LoadingProvider.useEffect": ()=>{
            // Once auth is initialized and we're not in initial load anymore, complete the page loading
            if (auth.isInitialized && !auth.loading && pageLoading.isInitialLoad) {
                // Small delay to ensure smooth transition without flashing
                const timer = setTimeout({
                    "LoadingProvider.useEffect.timer": ()=>{
                        pageLoading.completeInitialLoad();
                    }
                }["LoadingProvider.useEffect.timer"], 300); // Just enough to prevent flash, but not arbitrary long delay
                return ({
                    "LoadingProvider.useEffect": ()=>clearTimeout(timer)
                })["LoadingProvider.useEffect"];
            }
        }
    }["LoadingProvider.useEffect"], [
        auth.isInitialized,
        auth.loading,
        pageLoading.isInitialLoad,
        pageLoading.completeInitialLoad,
        pageLoading
    ]);
    // Determine if any loading is active
    const isAnyLoading = pageLoading.isLoading || manualLoading.isLoading || authLoading.isAuthLoading;
    const shouldShowInitialLoad = showInitialLoad && pageLoading.isInitialLoad;
    // FIXED: Show splash screen until auth is ACTUALLY resolved
    // This prevents layout shifts by ensuring we don't show content until auth state is determined
    const shouldShowSplash = shouldShowInitialLoad && (!auth.isInitialized || auth.loading);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LoadingContext.Provider, {
        value: value,
        children: [
            variant === 'facebook' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$components$2f$loading$2f$PageLoadingIndicator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                isLoading: shouldShowSplash,
                duration: 2000
            }, void 0, false, {
                fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
                lineNumber: 103,
                columnNumber: 9
            }, this),
            variant === 'progress' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$components$2f$loading$2f$PageLoadingIndicator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TopProgressBar"], {
                isLoading: isAnyLoading
            }, void 0, false, {
                fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
                lineNumber: 110,
                columnNumber: 9
            }, this),
            variant === 'dot' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$components$2f$loading$2f$PageLoadingIndicator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LoadingDot"], {
                isLoading: isAnyLoading,
                size: "md"
            }, void 0, false, {
                fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
                lineNumber: 114,
                columnNumber: 9
            }, this),
            variant === 'minimal' && isAnyLoading && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed top-4 left-4 z-[9999]",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center space-x-2 bg-white/95 backdrop-blur-sm px-3 py-1.5 rounded-full shadow-sm border border-gray-100",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-3 h-3 bg-orange-500 rounded-full animate-pulse"
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
                            lineNumber: 120,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-xs text-gray-600 font-medium",
                            children: "Loading"
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
                            lineNumber: 121,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
                    lineNumber: 119,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
                lineNumber: 118,
                columnNumber: 9
            }, this),
            children
        ]
    }, void 0, true, {
        fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
        lineNumber: 100,
        columnNumber: 5
    }, this);
}
_s1(LoadingProvider, "hVVcJQ2jhcbqXIqwqoPKv9n1gVs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$hooks$2f$usePageLoading$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePageLoading"],
        __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$hooks$2f$usePageLoading$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useManualLoading"],
        __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$hooks$2f$usePageLoading$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuthLoading"],
        __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = LoadingProvider;
function QuickProgressBar() {
    _s2();
    const { isPageLoading, isManualLoading } = useLoading();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$components$2f$loading$2f$PageLoadingIndicator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TopProgressBar"], {
        isLoading: isPageLoading || isManualLoading
    }, void 0, false, {
        fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
        lineNumber: 136,
        columnNumber: 10
    }, this);
}
_s2(QuickProgressBar, "YVQsOPzoYkNxOm1Bhkc+UMbOwPU=", false, function() {
    return [
        useLoading
    ];
});
_c1 = QuickProgressBar;
function QuickLoadingDot() {
    _s3();
    const { isAuthLoading } = useLoading();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$components$2f$loading$2f$PageLoadingIndicator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LoadingDot"], {
        isLoading: isAuthLoading,
        size: "sm"
    }, void 0, false, {
        fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
        lineNumber: 141,
        columnNumber: 10
    }, this);
}
_s3(QuickLoadingDot, "piT6B81BiTz5Vd7mZTplwl7o4HI=", false, function() {
    return [
        useLoading
    ];
});
_c2 = QuickLoadingDot;
function LoadingWrapper({ children, isLoading, variant = 'inline' }) {
    if (!isLoading) return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: children
    }, void 0, false);
    if (variant === 'overlay') {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "relative",
            children: [
                children,
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-5 h-5 animate-spin rounded-full border-2 border-orange-500 border-t-transparent"
                            }, void 0, false, {
                                fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
                                lineNumber: 164,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-sm text-gray-600",
                                children: "Loading..."
                            }, void 0, false, {
                                fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
                                lineNumber: 165,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
                        lineNumber: 163,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
                    lineNumber: 162,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
            lineNumber: 160,
            columnNumber: 7
        }, this);
    }
    if (variant === 'minimal') {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center py-4",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-4 h-4 animate-spin rounded-full border-2 border-orange-500 border-t-transparent"
            }, void 0, false, {
                fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
                lineNumber: 175,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
            lineNumber: 174,
            columnNumber: 7
        }, this);
    }
    // Inline variant
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex items-center space-x-2 py-2",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "w-4 h-4 animate-spin rounded-full border-2 border-orange-500 border-t-transparent"
            }, void 0, false, {
                fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
                lineNumber: 183,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "text-sm text-gray-600",
                children: "Loading..."
            }, void 0, false, {
                fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
                lineNumber: 184,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/apps/web/src/components/loading/LoadingProvider.tsx",
        lineNumber: 182,
        columnNumber: 5
    }, this);
}
_c3 = LoadingWrapper;
var _c, _c1, _c2, _c3;
__turbopack_context__.k.register(_c, "LoadingProvider");
__turbopack_context__.k.register(_c1, "QuickProgressBar");
__turbopack_context__.k.register(_c2, "QuickLoadingDot");
__turbopack_context__.k.register(_c3, "LoadingWrapper");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/utils/serialization.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Serialization utilities for Firebase data in Redux
 * Handles Firebase Timestamps and other non-serializable data
 */ __turbopack_context__.s({
    "deserializeTimestamps": (()=>deserializeTimestamps),
    "isSerializable": (()=>isSerializable),
    "serializeFirebaseDoc": (()=>serializeFirebaseDoc),
    "serializeUser": (()=>serializeUser)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$firebase$2f$firestore$2f$dist$2f$esm$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/firebase/firestore/dist/esm/index.esm.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/firestore/dist/index.esm2017.js [app-client] (ecmascript)");
;
const serializeUser = (user)=>{
    if (!user) return null;
    return {
        ...user,
        createdAt: user.createdAt instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Timestamp"] ? user.createdAt.toDate() : user.createdAt,
        updatedAt: user.updatedAt instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Timestamp"] ? user.updatedAt.toDate() : user.updatedAt
    };
};
const serializeFirebaseDoc = (doc)=>{
    if (!doc) return null;
    const serialized = {
        ...doc
    };
    // Convert all Timestamp fields to ISO strings
    Object.keys(serialized).forEach((key)=>{
        if (serialized[key] instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Timestamp"]) {
            serialized[key] = serialized[key].toDate().toISOString();
        }
    });
    return serialized;
};
const deserializeTimestamps = (data)=>{
    if (!data) return null;
    const deserialized = {
        ...data
    };
    // Convert ISO strings back to Date objects for common timestamp fields
    const timestampFields = [
        'createdAt',
        'updatedAt',
        'lastLoginAt',
        'emailVerified'
    ];
    timestampFields.forEach((field)=>{
        if (deserialized[field] && typeof deserialized[field] === 'string') {
            deserialized[field] = new Date(deserialized[field]);
        }
    });
    return deserialized;
};
const isSerializable = (value)=>{
    if (value === null || value === undefined) return true;
    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') return true;
    if (value instanceof Date) return false;
    if (value instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Timestamp"]) return false;
    if (Array.isArray(value)) return value.every(isSerializable);
    if (typeof value === 'object') return Object.values(value).every(isSerializable);
    return false;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/slices/authSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Auth Slice - Integrates with existing Firebase Auth Context
 * Maintains compatibility with your enterprise-grade auth system
 */ __turbopack_context__.s({
    "clearAuth": (()=>clearAuth),
    "default": (()=>__TURBOPACK__default__export__),
    "initializeAuth": (()=>initializeAuth),
    "selectAuth": (()=>selectAuth),
    "selectAuthError": (()=>selectAuthError),
    "selectAuthLoading": (()=>selectAuthLoading),
    "selectIsAuthenticated": (()=>selectIsAuthenticated),
    "selectIsInitialized": (()=>selectIsInitialized),
    "selectUser": (()=>selectUser),
    "setError": (()=>setError),
    "setInitialized": (()=>setInitialized),
    "setLoading": (()=>setLoading),
    "setSessionExpiry": (()=>setSessionExpiry),
    "setTokenRefreshInProgress": (()=>setTokenRefreshInProgress),
    "setUser": (()=>setUser),
    "signInUser": (()=>signInUser),
    "signOutUser": (()=>signOutUser),
    "signUpUser": (()=>signUpUser),
    "syncAuthState": (()=>syncAuthState),
    "updateUserData": (()=>updateUserData),
    "updateUserProfile": (()=>updateUserProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$utils$2f$serialization$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/utils/serialization.ts [app-client] (ecmascript)");
;
;
// Initial state
const initialState = {
    user: null,
    isAuthenticated: false,
    loading: true,
    error: null,
    isInitialized: false,
    lastSyncTimestamp: 0,
    tokenRefreshInProgress: false,
    sessionExpiry: null
};
const initializeAuth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('auth/initialize', async (_, { rejectWithValue })=>{
    try {
        // This will be called by your existing AuthContext
        // Just return success - the actual auth logic stays in AuthContext
        return {
            initialized: true
        };
    } catch  {
        return rejectWithValue('Failed to initialize auth');
    }
});
const signInUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('auth/signIn', async ({ email }, { rejectWithValue })=>{
    try {
        // This will be handled by your existing AuthContext
        // Redux just tracks the state changes
        return {
            email
        };
    } catch (error) {
        return rejectWithValue(error instanceof Error ? error.message : 'Sign in failed');
    }
});
const signUpUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('auth/signUp', async ({ email, name, role }, { rejectWithValue })=>{
    try {
        // This will be handled by your existing AuthContext
        return {
            email,
            name,
            role
        };
    } catch (error) {
        return rejectWithValue(error instanceof Error ? error.message : 'Sign up failed');
    }
});
const signOutUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('auth/signOut', async (_, { rejectWithValue })=>{
    try {
        // This will be handled by your existing AuthContext
        return {};
    } catch (error) {
        return rejectWithValue(error instanceof Error ? error.message : 'Sign out failed');
    }
});
const updateUserProfile = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('auth/updateProfile', async (data, { rejectWithValue })=>{
    try {
        // This will be handled by your existing AuthContext
        return data;
    } catch (error) {
        return rejectWithValue(error instanceof Error ? error.message : 'Profile update failed');
    }
});
// Auth slice
const authSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'auth',
    initialState,
    reducers: {
        // Sync actions called by AuthContext
        setUser: (state, action)=>{
            // Serialize user data to handle Firebase Timestamps
            state.user = action.payload ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$utils$2f$serialization$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serializeUser"])(action.payload) : null;
            state.isAuthenticated = !!action.payload;
            state.error = null;
            state.lastSyncTimestamp = Date.now();
        },
        setLoading: (state, action)=>{
            state.loading = action.payload;
        },
        setError: (state, action)=>{
            state.error = action.payload;
        },
        setInitialized: (state, action)=>{
            state.isInitialized = action.payload;
        },
        // Multi-tab synchronization
        syncAuthState: (state, action)=>{
            const { user, timestamp } = action.payload;
            // Only update if this is a newer change
            if (timestamp > state.lastSyncTimestamp) {
                // Serialize user data to handle Firebase Timestamps
                state.user = user ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$utils$2f$serialization$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serializeUser"])(user) : null;
                state.isAuthenticated = !!user;
                state.lastSyncTimestamp = timestamp;
            }
        },
        // Token management
        setTokenRefreshInProgress: (state, action)=>{
            state.tokenRefreshInProgress = action.payload;
        },
        setSessionExpiry: (state, action)=>{
            state.sessionExpiry = action.payload;
        },
        // Clear auth state
        clearAuth: (state)=>{
            state.user = null;
            state.isAuthenticated = false;
            state.error = null;
            state.sessionExpiry = null;
            state.lastSyncTimestamp = Date.now();
        },
        // Update user data without full re-auth
        updateUserData: (state, action)=>{
            if (state.user) {
                const updatedUser = {
                    ...state.user,
                    ...action.payload
                };
                state.user = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$utils$2f$serialization$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serializeUser"])(updatedUser);
                state.lastSyncTimestamp = Date.now();
            }
        }
    },
    extraReducers: (builder)=>{
        // Initialize auth
        builder.addCase(initializeAuth.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(initializeAuth.fulfilled, (state)=>{
            state.loading = false;
            state.isInitialized = true;
        }).addCase(initializeAuth.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        });
        // Sign in
        builder.addCase(signInUser.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(signInUser.fulfilled, (state)=>{
            state.loading = false;
        // User will be set by AuthContext via setUser action
        }).addCase(signInUser.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        });
        // Sign up
        builder.addCase(signUpUser.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(signUpUser.fulfilled, (state)=>{
            state.loading = false;
        // User will be set by AuthContext via setUser action
        }).addCase(signUpUser.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        });
        // Sign out
        builder.addCase(signOutUser.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(signOutUser.fulfilled, (state)=>{
            state.user = null;
            state.isAuthenticated = false;
            state.loading = false;
            state.sessionExpiry = null;
            state.lastSyncTimestamp = Date.now();
        }).addCase(signOutUser.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        });
        // Update profile
        builder.addCase(updateUserProfile.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(updateUserProfile.fulfilled, (state, action)=>{
            state.loading = false;
            if (state.user) {
                const updatedUser = {
                    ...state.user,
                    ...action.payload
                };
                state.user = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$utils$2f$serialization$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serializeUser"])(updatedUser);
                state.lastSyncTimestamp = Date.now();
            }
        }).addCase(updateUserProfile.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        });
    }
});
const { setUser, setLoading, setError, setInitialized, syncAuthState, setTokenRefreshInProgress, setSessionExpiry, clearAuth, updateUserData } = authSlice.actions;
const selectAuth = (state)=>state.auth;
const selectUser = (state)=>state.auth.user;
const selectIsAuthenticated = (state)=>state.auth.isAuthenticated;
const selectAuthLoading = (state)=>state.auth.loading;
const selectAuthError = (state)=>state.auth.error;
const selectIsInitialized = (state)=>state.auth.isInitialized;
const __TURBOPACK__default__export__ = authSlice;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/slices/uiSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * UI Slice - Global UI state management
 * Handles theme, modals, notifications, loading states, etc.
 */ __turbopack_context__.s({
    "addNotification": (()=>addNotification),
    "clearFilters": (()=>clearFilters),
    "clearNotifications": (()=>clearNotifications),
    "clearSearch": (()=>clearSearch),
    "closeAllModals": (()=>closeAllModals),
    "closeModal": (()=>closeModal),
    "default": (()=>__TURBOPACK__default__export__),
    "openModal": (()=>openModal),
    "removeFilter": (()=>removeFilter),
    "removeModal": (()=>removeModal),
    "removeNotification": (()=>removeNotification),
    "selectCurrentLocation": (()=>selectCurrentLocation),
    "selectGlobalLoading": (()=>selectGlobalLoading),
    "selectIsOnline": (()=>selectIsOnline),
    "selectModals": (()=>selectModals),
    "selectNotifications": (()=>selectNotifications),
    "selectSidebarOpen": (()=>selectSidebarOpen),
    "selectTheme": (()=>selectTheme),
    "setAdminActiveTab": (()=>setAdminActiveTab),
    "setAdminSelectedItems": (()=>setAdminSelectedItems),
    "setCurrentLocation": (()=>setCurrentLocation),
    "setCustomerSortBy": (()=>setCustomerSortBy),
    "setCustomerViewMode": (()=>setCustomerViewMode),
    "setDriverAutoAccept": (()=>setDriverAutoAccept),
    "setDriverMapView": (()=>setDriverMapView),
    "setFilter": (()=>setFilter),
    "setGlobalLoading": (()=>setGlobalLoading),
    "setMobileMenuOpen": (()=>setMobileMenuOpen),
    "setOnlineStatus": (()=>setOnlineStatus),
    "setPageLoading": (()=>setPageLoading),
    "setSearchLoading": (()=>setSearchLoading),
    "setSearchQuery": (()=>setSearchQuery),
    "setSearchResults": (()=>setSearchResults),
    "setSidebarOpen": (()=>setSidebarOpen),
    "setTheme": (()=>setTheme),
    "setVendorActiveSection": (()=>setVendorActiveSection),
    "setVendorMenuEditMode": (()=>setVendorMenuEditMode),
    "setVendorSelectedOrders": (()=>setVendorSelectedOrders),
    "showErrorNotification": (()=>showErrorNotification),
    "showInfoNotification": (()=>showInfoNotification),
    "showSuccessNotification": (()=>showSuccessNotification),
    "showWarningNotification": (()=>showWarningNotification),
    "toggleAdminBulkActions": (()=>toggleAdminBulkActions),
    "toggleCustomerFilters": (()=>toggleCustomerFilters),
    "toggleDriverTraffic": (()=>toggleDriverTraffic),
    "toggleMobileMenu": (()=>toggleMobileMenu),
    "toggleSidebar": (()=>toggleSidebar),
    "toggleTheme": (()=>toggleTheme),
    "updateLastActivity": (()=>updateLastActivity)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
// Initial state
const initialState = {
    theme: 'light',
    sidebarOpen: false,
    mobileMenuOpen: false,
    globalLoading: false,
    pageLoading: false,
    notifications: [],
    modals: [],
    searchQuery: '',
    searchResults: [],
    searchLoading: false,
    activeFilters: {},
    currentLocation: null,
    isOnline: true,
    lastActivity: Date.now(),
    adminPanel: {
        activeTab: 'dashboard',
        selectedItems: [],
        bulkActions: false
    },
    vendorPanel: {
        activeSection: 'dashboard',
        menuEditMode: false,
        selectedOrders: []
    },
    driverPanel: {
        mapView: 'normal',
        showTraffic: true,
        autoAcceptOrders: false
    },
    customerApp: {
        viewMode: 'list',
        sortBy: 'distance',
        showFilters: false
    }
};
// UI slice
const uiSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'ui',
    initialState,
    reducers: {
        // Theme
        setTheme: (state, action)=>{
            state.theme = action.payload;
        },
        toggleTheme: (state)=>{
            state.theme = state.theme === 'light' ? 'dark' : 'light';
        },
        // Layout
        setSidebarOpen: (state, action)=>{
            state.sidebarOpen = action.payload;
        },
        toggleSidebar: (state)=>{
            state.sidebarOpen = !state.sidebarOpen;
        },
        setMobileMenuOpen: (state, action)=>{
            state.mobileMenuOpen = action.payload;
        },
        toggleMobileMenu: (state)=>{
            state.mobileMenuOpen = !state.mobileMenuOpen;
        },
        // Loading states
        setGlobalLoading: (state, action)=>{
            state.globalLoading = action.payload;
        },
        setPageLoading: (state, action)=>{
            state.pageLoading = action.payload;
        },
        // Notifications
        addNotification: (state, action)=>{
            const notification = {
                ...action.payload,
                id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                createdAt: Date.now()
            };
            state.notifications.unshift(notification);
            // Limit to 10 notifications
            if (state.notifications.length > 10) {
                state.notifications = state.notifications.slice(0, 10);
            }
        },
        removeNotification: (state, action)=>{
            state.notifications = state.notifications.filter((n)=>n.id !== action.payload);
        },
        clearNotifications: (state)=>{
            state.notifications = [];
        },
        // Modals
        openModal: (state, action)=>{
            const { type, props } = action.payload;
            const modal = {
                id: `modal-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                type,
                props,
                isOpen: true
            };
            state.modals.push(modal);
        },
        closeModal: (state, action)=>{
            const modalIndex = state.modals.findIndex((m)=>m.id === action.payload);
            if (modalIndex >= 0) {
                state.modals[modalIndex].isOpen = false;
            }
        },
        removeModal: (state, action)=>{
            state.modals = state.modals.filter((m)=>m.id !== action.payload);
        },
        closeAllModals: (state)=>{
            state.modals.forEach((modal)=>{
                modal.isOpen = false;
            });
        },
        // Search
        setSearchQuery: (state, action)=>{
            state.searchQuery = action.payload;
        },
        setSearchResults: (state, action)=>{
            state.searchResults = action.payload;
        },
        setSearchLoading: (state, action)=>{
            state.searchLoading = action.payload;
        },
        clearSearch: (state)=>{
            state.searchQuery = '';
            state.searchResults = [];
            state.searchLoading = false;
        },
        // Filters
        setFilter: (state, action)=>{
            const { key, value } = action.payload;
            state.activeFilters[key] = value;
        },
        removeFilter: (state, action)=>{
            delete state.activeFilters[action.payload];
        },
        clearFilters: (state)=>{
            state.activeFilters = {};
        },
        // Location
        setCurrentLocation: (state, action)=>{
            state.currentLocation = action.payload;
        },
        // App state
        setOnlineStatus: (state, action)=>{
            state.isOnline = action.payload;
        },
        updateLastActivity: (state)=>{
            state.lastActivity = Date.now();
        },
        // Admin panel
        setAdminActiveTab: (state, action)=>{
            state.adminPanel.activeTab = action.payload;
        },
        setAdminSelectedItems: (state, action)=>{
            state.adminPanel.selectedItems = action.payload;
        },
        toggleAdminBulkActions: (state)=>{
            state.adminPanel.bulkActions = !state.adminPanel.bulkActions;
            if (!state.adminPanel.bulkActions) {
                state.adminPanel.selectedItems = [];
            }
        },
        // Vendor panel
        setVendorActiveSection: (state, action)=>{
            state.vendorPanel.activeSection = action.payload;
        },
        setVendorMenuEditMode: (state, action)=>{
            state.vendorPanel.menuEditMode = action.payload;
        },
        setVendorSelectedOrders: (state, action)=>{
            state.vendorPanel.selectedOrders = action.payload;
        },
        // Driver panel
        setDriverMapView: (state, action)=>{
            state.driverPanel.mapView = action.payload;
        },
        toggleDriverTraffic: (state)=>{
            state.driverPanel.showTraffic = !state.driverPanel.showTraffic;
        },
        setDriverAutoAccept: (state, action)=>{
            state.driverPanel.autoAcceptOrders = action.payload;
        },
        // Customer app
        setCustomerViewMode: (state, action)=>{
            state.customerApp.viewMode = action.payload;
        },
        setCustomerSortBy: (state, action)=>{
            state.customerApp.sortBy = action.payload;
        },
        toggleCustomerFilters: (state)=>{
            state.customerApp.showFilters = !state.customerApp.showFilters;
        },
        // Utility actions
        showSuccessNotification: (state, action)=>{
            const { title, message } = action.payload;
            uiSlice.caseReducers.addNotification(state, {
                type: 'ui/addNotification',
                payload: {
                    type: 'success',
                    title,
                    message,
                    duration: 5000
                }
            });
        },
        showErrorNotification: (state, action)=>{
            const { title, message } = action.payload;
            uiSlice.caseReducers.addNotification(state, {
                type: 'ui/addNotification',
                payload: {
                    type: 'error',
                    title,
                    message,
                    duration: 8000
                }
            });
        },
        showWarningNotification: (state, action)=>{
            const { title, message } = action.payload;
            uiSlice.caseReducers.addNotification(state, {
                type: 'ui/addNotification',
                payload: {
                    type: 'warning',
                    title,
                    message,
                    duration: 6000
                }
            });
        },
        showInfoNotification: (state, action)=>{
            const { title, message } = action.payload;
            uiSlice.caseReducers.addNotification(state, {
                type: 'ui/addNotification',
                payload: {
                    type: 'info',
                    title,
                    message,
                    duration: 5000
                }
            });
        }
    }
});
const { setTheme, toggleTheme, setSidebarOpen, toggleSidebar, setMobileMenuOpen, toggleMobileMenu, setGlobalLoading, setPageLoading, addNotification, removeNotification, clearNotifications, openModal, closeModal, removeModal, closeAllModals, setSearchQuery, setSearchResults, setSearchLoading, clearSearch, setFilter, removeFilter, clearFilters, setCurrentLocation, setOnlineStatus, updateLastActivity, setAdminActiveTab, setAdminSelectedItems, toggleAdminBulkActions, setVendorActiveSection, setVendorMenuEditMode, setVendorSelectedOrders, setDriverMapView, toggleDriverTraffic, setDriverAutoAccept, setCustomerViewMode, setCustomerSortBy, toggleCustomerFilters, showSuccessNotification, showErrorNotification, showWarningNotification, showInfoNotification } = uiSlice.actions;
const selectTheme = (state)=>state.ui.theme;
const selectSidebarOpen = (state)=>state.ui.sidebarOpen;
const selectNotifications = (state)=>state.ui.notifications;
const selectModals = (state)=>state.ui.modals;
const selectGlobalLoading = (state)=>state.ui.globalLoading;
const selectCurrentLocation = (state)=>state.ui.currentLocation;
const selectIsOnline = (state)=>state.ui.isOnline;
const __TURBOPACK__default__export__ = uiSlice;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/slices/cartSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Cart Slice - Enhanced version of your existing cart logic
 * Maintains compatibility with your current cart patterns
 */ __turbopack_context__.s({
    "addToCart": (()=>addToCart),
    "applyPromoCode": (()=>applyPromoCode),
    "clearCart": (()=>clearCart),
    "default": (()=>__TURBOPACK__default__export__),
    "deleteSavedCart": (()=>deleteSavedCart),
    "loadCartFromStorage": (()=>loadCartFromStorage),
    "loadSavedCart": (()=>loadSavedCart),
    "removeFromCart": (()=>removeFromCart),
    "removePromoCode": (()=>removePromoCode),
    "saveCartToStorage": (()=>saveCartToStorage),
    "saveCurrentCart": (()=>saveCurrentCart),
    "selectCart": (()=>selectCart),
    "selectCartError": (()=>selectCartError),
    "selectCartItemCount": (()=>selectCartItemCount),
    "selectCartItems": (()=>selectCartItems),
    "selectCartLoading": (()=>selectCartLoading),
    "selectCartTotal": (()=>selectCartTotal),
    "selectCheckoutStep": (()=>selectCheckoutStep),
    "selectPromoCode": (()=>selectPromoCode),
    "setCheckoutStep": (()=>setCheckoutStep),
    "setDeliveryAddress": (()=>setDeliveryAddress),
    "setError": (()=>setError),
    "setLoading": (()=>setLoading),
    "setPaymentMethod": (()=>setPaymentMethod),
    "undoRemoveItem": (()=>undoRemoveItem),
    "updateQuantity": (()=>updateQuantity),
    "validatePromoCode": (()=>validatePromoCode)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
// Initial state
const initialState = {
    cart: null,
    loading: false,
    error: null,
    savedCarts: [],
    recentlyRemoved: [],
    promoCode: null,
    promoDiscount: 0,
    checkoutStep: 'cart',
    deliveryAddress: null,
    paymentMethod: null
};
const loadCartFromStorage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('cart/loadFromStorage', async (_, { rejectWithValue })=>{
    try {
        const savedCart = localStorage.getItem('cart');
        if (savedCart) {
            return JSON.parse(savedCart);
        }
        return null;
    } catch  {
        return rejectWithValue('Failed to load cart from storage');
    }
});
const saveCartToStorage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('cart/saveToStorage', async (cart, { rejectWithValue })=>{
    try {
        if (cart) {
            localStorage.setItem('cart', JSON.stringify(cart));
        } else {
            localStorage.removeItem('cart');
        }
        return cart;
    } catch  {
        return rejectWithValue('Failed to save cart to storage');
    }
});
const validatePromoCode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('cart/validatePromoCode', async ({ code, cartTotal }, { rejectWithValue })=>{
    try {
        // TODO: Implement promo code validation API call
        // For now, return mock validation
        const mockPromos = {
            'SAVE10': 0.1,
            'SAVE20': 0.2,
            'NEWUSER': 0.15
        };
        const discount = mockPromos[code.toUpperCase()];
        if (discount) {
            return {
                code,
                discount,
                amount: cartTotal * discount
            };
        } else {
            throw new Error('Invalid promo code');
        }
    } catch (error) {
        return rejectWithValue(error instanceof Error ? error.message : 'Promo code validation failed');
    }
});
// Helper functions (same logic as your existing cart)
const calculateCartTotals = (items, deliveryFee = 5.99, promoDiscount = 0)=>{
    const subtotal = items.reduce((sum, item)=>sum + item.totalPrice, 0);
    const tax = subtotal * 0.08; // 8% tax
    const discountAmount = subtotal * promoDiscount;
    const total = subtotal + deliveryFee + tax - discountAmount;
    return {
        subtotal,
        deliveryFee,
        tax,
        discountAmount,
        total
    };
};
const createCartItem = (item, quantity, specialInstructions)=>{
    return {
        id: `${item.id}-${Date.now()}`,
        menuItem: item,
        quantity,
        specialInstructions,
        totalPrice: item.price * quantity
    };
};
// Cart slice
const cartSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'cart',
    initialState,
    reducers: {
        // Core cart operations (matching your existing logic)
        addToCart: (state, action)=>{
            const { item, quantity, specialInstructions } = action.payload;
            if (!state.cart || state.cart.restaurantId !== item.restaurantId) {
                // Create new cart or replace if different restaurant
                const newCartItem = createCartItem(item, quantity, specialInstructions);
                const totals = calculateCartTotals([
                    newCartItem
                ], 5.99, state.promoDiscount);
                state.cart = {
                    items: [
                        newCartItem
                    ],
                    restaurantId: item.restaurantId,
                    ...totals
                };
            } else {
                // Add to existing cart
                const existingItemIndex = state.cart.items.findIndex((cartItem)=>cartItem.menuItem.id === item.id && cartItem.specialInstructions === specialInstructions);
                if (existingItemIndex >= 0) {
                    // Update existing item
                    state.cart.items[existingItemIndex].quantity += quantity;
                    state.cart.items[existingItemIndex].totalPrice = state.cart.items[existingItemIndex].menuItem.price * state.cart.items[existingItemIndex].quantity;
                } else {
                    // Add new item
                    const newCartItem = createCartItem(item, quantity, specialInstructions);
                    state.cart.items.push(newCartItem);
                }
                // Recalculate totals
                const totals = calculateCartTotals(state.cart.items, state.cart.deliveryFee, state.promoDiscount);
                Object.assign(state.cart, totals);
            }
            state.error = null;
        },
        removeFromCart: (state, action)=>{
            if (!state.cart) return;
            const itemId = action.payload;
            const itemIndex = state.cart.items.findIndex((item)=>item.id === itemId);
            if (itemIndex >= 0) {
                // Store for undo functionality
                const removedItem = state.cart.items[itemIndex];
                state.recentlyRemoved.unshift(removedItem);
                // Keep only last 5 removed items
                state.recentlyRemoved = state.recentlyRemoved.slice(0, 5);
                // Remove item
                state.cart.items.splice(itemIndex, 1);
                if (state.cart.items.length === 0) {
                    state.cart = null;
                    state.promoCode = null;
                    state.promoDiscount = 0;
                } else {
                    // Recalculate totals
                    const totals = calculateCartTotals(state.cart.items, state.cart.deliveryFee, state.promoDiscount);
                    Object.assign(state.cart, totals);
                }
            }
        },
        updateQuantity: (state, action)=>{
            if (!state.cart) return;
            const { itemId, quantity } = action.payload;
            if (quantity <= 0) {
                // Remove item if quantity is 0 or negative
                cartSlice.caseReducers.removeFromCart(state, {
                    type: 'cart/removeFromCart',
                    payload: itemId
                });
                return;
            }
            const itemIndex = state.cart.items.findIndex((item)=>item.id === itemId);
            if (itemIndex >= 0) {
                state.cart.items[itemIndex].quantity = quantity;
                state.cart.items[itemIndex].totalPrice = state.cart.items[itemIndex].menuItem.price * quantity;
                // Recalculate totals
                const totals = calculateCartTotals(state.cart.items, state.cart.deliveryFee, state.promoDiscount);
                Object.assign(state.cart, totals);
            }
        },
        clearCart: (state)=>{
            if (state.cart) {
                // Save current cart to recently removed for potential recovery
                state.recentlyRemoved = [
                    ...state.cart.items
                ];
            }
            state.cart = null;
            state.promoCode = null;
            state.promoDiscount = 0;
            state.checkoutStep = 'cart';
            state.deliveryAddress = null;
            state.paymentMethod = null;
        },
        // Enhanced features
        undoRemoveItem: (state, action)=>{
            const index = action.payload || 0;
            if (state.recentlyRemoved[index]) {
                const itemToRestore = state.recentlyRemoved[index];
                state.recentlyRemoved.splice(index, 1);
                // Add back to cart
                cartSlice.caseReducers.addToCart(state, {
                    type: 'cart/addToCart',
                    payload: {
                        item: itemToRestore.menuItem,
                        quantity: itemToRestore.quantity,
                        specialInstructions: itemToRestore.specialInstructions
                    }
                });
            }
        },
        applyPromoCode: (state, action)=>{
            const { code, discount } = action.payload;
            state.promoCode = code;
            state.promoDiscount = discount;
            if (state.cart) {
                const totals = calculateCartTotals(state.cart.items, state.cart.deliveryFee, discount);
                Object.assign(state.cart, totals);
            }
        },
        removePromoCode: (state)=>{
            state.promoCode = null;
            state.promoDiscount = 0;
            if (state.cart) {
                const totals = calculateCartTotals(state.cart.items, state.cart.deliveryFee, 0);
                Object.assign(state.cart, totals);
            }
        },
        // Checkout flow
        setCheckoutStep: (state, action)=>{
            state.checkoutStep = action.payload;
        },
        setDeliveryAddress: (state, action)=>{
            state.deliveryAddress = action.payload;
        },
        setPaymentMethod: (state, action)=>{
            state.paymentMethod = action.payload;
        },
        // Save cart for later
        saveCurrentCart: (state, action)=>{
            if (state.cart) {
                const savedCart = {
                    ...state.cart,
                    savedAt: new Date().toISOString(),
                    name: action.payload
                };
                state.savedCarts.push(savedCart);
            }
        },
        loadSavedCart: (state, action)=>{
            const savedCart = state.savedCarts[action.payload];
            if (savedCart) {
                state.cart = {
                    ...savedCart
                };
                // Remove saved timestamp and name
                delete state.cart.savedAt;
                delete state.cart.name;
            }
        },
        deleteSavedCart: (state, action)=>{
            state.savedCarts.splice(action.payload, 1);
        },
        setError: (state, action)=>{
            state.error = action.payload;
        },
        setLoading: (state, action)=>{
            state.loading = action.payload;
        }
    },
    extraReducers: (builder)=>{
        // Load from storage
        builder.addCase(loadCartFromStorage.fulfilled, (state, action)=>{
            state.cart = action.payload;
            state.loading = false;
        }).addCase(loadCartFromStorage.rejected, (state, action)=>{
            state.error = action.payload;
            state.loading = false;
        });
        // Save to storage
        builder.addCase(saveCartToStorage.fulfilled, (state)=>{
            state.loading = false;
        }).addCase(saveCartToStorage.rejected, (state, action)=>{
            state.error = action.payload;
            state.loading = false;
        });
        // Promo code validation
        builder.addCase(validatePromoCode.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(validatePromoCode.fulfilled, (state, action)=>{
            const { code, discount } = action.payload;
            state.promoCode = code;
            state.promoDiscount = discount;
            state.loading = false;
            if (state.cart) {
                const totals = calculateCartTotals(state.cart.items, state.cart.deliveryFee, discount);
                Object.assign(state.cart, totals);
            }
        }).addCase(validatePromoCode.rejected, (state, action)=>{
            state.error = action.payload;
            state.loading = false;
        });
    }
});
const { addToCart, removeFromCart, updateQuantity, clearCart, undoRemoveItem, applyPromoCode, removePromoCode, setCheckoutStep, setDeliveryAddress, setPaymentMethod, saveCurrentCart, loadSavedCart, deleteSavedCart, setError, setLoading } = cartSlice.actions;
const selectCart = (state)=>state.cart.cart;
const selectCartItems = (state)=>state.cart.cart?.items || [];
const selectCartTotal = (state)=>state.cart.cart?.total || 0;
const selectCartItemCount = (state)=>state.cart.cart?.items.reduce((sum, item)=>sum + item.quantity, 0) || 0;
const selectCartLoading = (state)=>state.cart.loading;
const selectCartError = (state)=>state.cart.error;
const selectPromoCode = (state)=>state.cart.promoCode;
const selectCheckoutStep = (state)=>state.cart.checkoutStep;
const __TURBOPACK__default__export__ = cartSlice;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/slices/ordersSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Orders Slice - Comprehensive order management
 * Handles orders across all user roles (Customer, Vendor, Driver, Admin)
 */ __turbopack_context__.s({
    "assignDriver": (()=>assignDriver),
    "clearError": (()=>clearError),
    "clearFilters": (()=>clearFilters),
    "createOrder": (()=>createOrder),
    "default": (()=>__TURBOPACK__default__export__),
    "fetchOrders": (()=>fetchOrders),
    "rateOrder": (()=>rateOrder),
    "selectCurrentOrder": (()=>selectCurrentOrder),
    "selectOrderFilters": (()=>selectOrderFilters),
    "selectOrderStats": (()=>selectOrderStats),
    "selectOrders": (()=>selectOrders),
    "selectOrdersError": (()=>selectOrdersError),
    "selectOrdersLoading": (()=>selectOrdersLoading),
    "setCreating": (()=>setCreating),
    "setCurrentOrder": (()=>setCurrentOrder),
    "setCurrentPage": (()=>setCurrentPage),
    "setError": (()=>setError),
    "setFilters": (()=>setFilters),
    "setLiveUpdates": (()=>setLiveUpdates),
    "setLoading": (()=>setLoading),
    "setSorting": (()=>setSorting),
    "setUpdating": (()=>setUpdating),
    "updateFilter": (()=>updateFilter),
    "updateOrderRealTime": (()=>updateOrderRealTime),
    "updateOrderStatus": (()=>updateOrderStatus),
    "updateStats": (()=>updateStats)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
// Initial state
const initialState = {
    orders: [],
    currentOrder: null,
    loading: false,
    creating: false,
    updating: false,
    error: null,
    filters: {},
    sortBy: 'createdAt',
    sortOrder: 'desc',
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    liveUpdates: true,
    lastUpdate: 0,
    customerOrders: [],
    vendorOrders: [],
    driverOrders: [],
    adminOrders: [],
    stats: {
        total: 0,
        pending: 0,
        confirmed: 0,
        preparing: 0,
        ready: 0,
        pickedUp: 0,
        delivered: 0,
        cancelled: 0,
        todayRevenue: 0,
        averageOrderValue: 0
    }
};
const fetchOrders = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('orders/fetchOrders', async ({ filters, page = 1, limit = 20, role }, { rejectWithValue })=>{
    try {
        // TODO: Implement API call to fetch orders
        // This will integrate with your existing Firebase/API patterns
        const response = await fetch('/api/orders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                filters,
                page,
                limit,
                role
            })
        });
        if (!response.ok) {
            throw new Error('Failed to fetch orders');
        }
        return await response.json();
    } catch (error) {
        return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch orders');
    }
});
const createOrder = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('orders/createOrder', async (orderData, { rejectWithValue })=>{
    try {
        // TODO: Implement order creation API call
        const response = await fetch('/api/orders', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(orderData)
        });
        if (!response.ok) {
            throw new Error('Failed to create order');
        }
        return await response.json();
    } catch (error) {
        return rejectWithValue(error instanceof Error ? error.message : 'Failed to create order');
    }
});
const updateOrderStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('orders/updateStatus', async ({ orderId, status, note }, { rejectWithValue })=>{
    try {
        // TODO: Implement status update API call
        const response = await fetch(`/api/orders/${orderId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                status,
                note
            })
        });
        if (!response.ok) {
            throw new Error('Failed to update order status');
        }
        return await response.json();
    } catch (error) {
        return rejectWithValue(error instanceof Error ? error.message : 'Failed to update order status');
    }
});
const assignDriver = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('orders/assignDriver', async ({ orderId, driverId }, { rejectWithValue })=>{
    try {
        // TODO: Implement driver assignment API call
        const response = await fetch(`/api/orders/${orderId}/assign-driver`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                driverId
            })
        });
        if (!response.ok) {
            throw new Error('Failed to assign driver');
        }
        return await response.json();
    } catch (error) {
        return rejectWithValue(error instanceof Error ? error.message : 'Failed to assign driver');
    }
});
const rateOrder = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('orders/rateOrder', async ({ orderId, rating }, { rejectWithValue })=>{
    try {
        // TODO: Implement rating API call
        const response = await fetch(`/api/orders/${orderId}/rate`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                rating
            })
        });
        if (!response.ok) {
            throw new Error('Failed to rate order');
        }
        return await response.json();
    } catch (error) {
        return rejectWithValue(error instanceof Error ? error.message : 'Failed to rate order');
    }
});
// Orders slice
const ordersSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'orders',
    initialState,
    reducers: {
        // Real-time order updates
        updateOrderRealTime: (state, action)=>{
            const updatedOrder = action.payload;
            const index = state.orders.findIndex((order)=>order.id === updatedOrder.id);
            if (index >= 0) {
                state.orders[index] = updatedOrder;
            } else {
                state.orders.unshift(updatedOrder);
            }
            // Update role-specific arrays
            if (updatedOrder.customerId) {
                const customerIndex = state.customerOrders.findIndex((o)=>o.id === updatedOrder.id);
                if (customerIndex >= 0) {
                    state.customerOrders[customerIndex] = updatedOrder;
                }
            }
            if (updatedOrder.vendorId) {
                const vendorIndex = state.vendorOrders.findIndex((o)=>o.id === updatedOrder.id);
                if (vendorIndex >= 0) {
                    state.vendorOrders[vendorIndex] = updatedOrder;
                }
            }
            if (updatedOrder.driverId) {
                const driverIndex = state.driverOrders.findIndex((o)=>o.id === updatedOrder.id);
                if (driverIndex >= 0) {
                    state.driverOrders[driverIndex] = updatedOrder;
                }
            }
            state.lastUpdate = Date.now();
        },
        // Set current order
        setCurrentOrder: (state, action)=>{
            state.currentOrder = action.payload;
        },
        // Filters
        setFilters: (state, action)=>{
            state.filters = action.payload;
            state.currentPage = 1; // Reset to first page when filters change
        },
        updateFilter: (state, action)=>{
            const { key, value } = action.payload;
            if (value !== undefined) {
                // Type assertion is safe here as we're using the correct key-value pair
                state.filters[key] = value;
            } else {
                delete state.filters[key];
            }
            state.currentPage = 1;
        },
        clearFilters: (state)=>{
            state.filters = {};
            state.currentPage = 1;
        },
        // Sorting
        setSorting: (state, action)=>{
            const { sortBy, sortOrder } = action.payload;
            state.sortBy = sortBy;
            state.sortOrder = sortOrder;
        },
        // Pagination
        setCurrentPage: (state, action)=>{
            state.currentPage = action.payload;
        },
        // Live updates
        setLiveUpdates: (state, action)=>{
            state.liveUpdates = action.payload;
        },
        // Statistics
        updateStats: (state, action)=>{
            state.stats = {
                ...state.stats,
                ...action.payload
            };
        },
        // Error handling
        setError: (state, action)=>{
            state.error = action.payload;
        },
        clearError: (state)=>{
            state.error = null;
        },
        // Loading states
        setLoading: (state, action)=>{
            state.loading = action.payload;
        },
        setCreating: (state, action)=>{
            state.creating = action.payload;
        },
        setUpdating: (state, action)=>{
            state.updating = action.payload;
        }
    },
    extraReducers: (builder)=>{
        // Fetch orders
        builder.addCase(fetchOrders.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(fetchOrders.fulfilled, (state, action)=>{
            const { orders, totalCount, totalPages, role } = action.payload;
            state.orders = orders;
            state.totalCount = totalCount;
            state.totalPages = totalPages;
            state.loading = false;
            // Update role-specific arrays
            if (role === 'customer') {
                state.customerOrders = orders;
            } else if (role === 'vendor') {
                state.vendorOrders = orders;
            } else if (role === 'driver') {
                state.driverOrders = orders;
            } else if (role === 'admin') {
                state.adminOrders = orders;
            }
        }).addCase(fetchOrders.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        });
        // Create order
        builder.addCase(createOrder.pending, (state)=>{
            state.creating = true;
            state.error = null;
        }).addCase(createOrder.fulfilled, (state, action)=>{
            const newOrder = action.payload;
            state.orders.unshift(newOrder);
            state.currentOrder = newOrder;
            state.creating = false;
        }).addCase(createOrder.rejected, (state, action)=>{
            state.creating = false;
            state.error = action.payload;
        });
        // Update order status
        builder.addCase(updateOrderStatus.pending, (state)=>{
            state.updating = true;
            state.error = null;
        }).addCase(updateOrderStatus.fulfilled, (state, action)=>{
            const updatedOrder = action.payload;
            ordersSlice.caseReducers.updateOrderRealTime(state, {
                type: 'orders/updateOrderRealTime',
                payload: updatedOrder
            });
            state.updating = false;
        }).addCase(updateOrderStatus.rejected, (state, action)=>{
            state.updating = false;
            state.error = action.payload;
        });
        // Assign driver
        builder.addCase(assignDriver.fulfilled, (state, action)=>{
            const updatedOrder = action.payload;
            ordersSlice.caseReducers.updateOrderRealTime(state, {
                type: 'orders/updateOrderRealTime',
                payload: updatedOrder
            });
        });
        // Rate order
        builder.addCase(rateOrder.fulfilled, (state, action)=>{
            const updatedOrder = action.payload;
            ordersSlice.caseReducers.updateOrderRealTime(state, {
                type: 'orders/updateOrderRealTime',
                payload: updatedOrder
            });
        });
    }
});
const { updateOrderRealTime, setCurrentOrder, setFilters, updateFilter, clearFilters, setSorting, setCurrentPage, setLiveUpdates, updateStats, setError, clearError, setLoading, setCreating, setUpdating } = ordersSlice.actions;
const selectOrders = (state)=>state.orders.orders;
const selectCurrentOrder = (state)=>state.orders.currentOrder;
const selectOrdersLoading = (state)=>state.orders.loading;
const selectOrdersError = (state)=>state.orders.error;
const selectOrderFilters = (state)=>state.orders.filters;
const selectOrderStats = (state)=>state.orders.stats;
const __TURBOPACK__default__export__ = ordersSlice;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/slices/restaurantsSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Restaurants Slice - Restaurant and menu management
 */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "fetchRestaurants": (()=>fetchRestaurants),
    "setCurrentRestaurant": (()=>setCurrentRestaurant),
    "setFilters": (()=>setFilters),
    "setSearchQuery": (()=>setSearchQuery),
    "setSortBy": (()=>setSortBy)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    restaurants: [],
    currentRestaurant: null,
    menu: [],
    loading: false,
    error: null,
    filters: {},
    searchQuery: '',
    sortBy: 'distance'
};
const fetchRestaurants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('restaurants/fetchRestaurants', async (params, { rejectWithValue })=>{
    try {
        // TODO: Implement API call
        return [];
    } catch (error) {
        return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch restaurants');
    }
});
const restaurantsSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'restaurants',
    initialState,
    reducers: {
        setCurrentRestaurant: (state, action)=>{
            state.currentRestaurant = action.payload;
        },
        setFilters: (state, action)=>{
            state.filters = action.payload;
        },
        setSearchQuery: (state, action)=>{
            state.searchQuery = action.payload;
        },
        setSortBy: (state, action)=>{
            state.sortBy = action.payload;
        }
    },
    extraReducers: (builder)=>{
        builder.addCase(fetchRestaurants.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(fetchRestaurants.fulfilled, (state, action)=>{
            state.restaurants = action.payload;
            state.loading = false;
        }).addCase(fetchRestaurants.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        });
    }
});
const { setCurrentRestaurant, setFilters, setSearchQuery, setSortBy } = restaurantsSlice.actions;
const __TURBOPACK__default__export__ = restaurantsSlice;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/slices/driversSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Drivers Slice - Driver management and tracking
 */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "fetchDrivers": (()=>fetchDrivers),
    "setDriverOnlineStatus": (()=>setDriverOnlineStatus),
    "setRealTimeTracking": (()=>setRealTimeTracking),
    "updateDriverLocation": (()=>updateDriverLocation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    drivers: [],
    availableDrivers: [],
    currentDriver: null,
    loading: false,
    error: null,
    realTimeTracking: false
};
const fetchDrivers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('drivers/fetchDrivers', async (params, { rejectWithValue })=>{
    try {
        // TODO: Implement API call
        return [];
    } catch (error) {
        return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch drivers');
    }
});
const driversSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'drivers',
    initialState,
    reducers: {
        updateDriverLocation: (state, action)=>{
            const { driverId, location } = action.payload;
            const driver = state.drivers.find((d)=>d.id === driverId);
            if (driver) {
                driver.location = location;
            }
        },
        setDriverOnlineStatus: (state, action)=>{
            const { driverId, isOnline } = action.payload;
            const driver = state.drivers.find((d)=>d.id === driverId);
            if (driver) {
                driver.isOnline = isOnline;
            }
        },
        setRealTimeTracking: (state, action)=>{
            state.realTimeTracking = action.payload;
        }
    },
    extraReducers: (builder)=>{
        builder.addCase(fetchDrivers.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(fetchDrivers.fulfilled, (state, action)=>{
            state.drivers = action.payload;
            state.availableDrivers = action.payload.filter((d)=>d.isOnline && d.status === 'active');
            state.loading = false;
        }).addCase(fetchDrivers.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        });
    }
});
const { updateDriverLocation, setDriverOnlineStatus, setRealTimeTracking } = driversSlice.actions;
const __TURBOPACK__default__export__ = driversSlice;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/slices/customersSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Customers Slice - Customer management
 */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "fetchCustomers": (()=>fetchCustomers)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    customers: [],
    loading: false,
    error: null
};
const fetchCustomers = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('customers/fetchCustomers', async (params, { rejectWithValue })=>{
    try {
        // TODO: Implement API call
        return [];
    } catch (error) {
        return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch customers');
    }
});
const customersSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'customers',
    initialState,
    reducers: {},
    extraReducers: (builder)=>{
        builder.addCase(fetchCustomers.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(fetchCustomers.fulfilled, (state, action)=>{
            state.customers = action.payload;
            state.loading = false;
        }).addCase(fetchCustomers.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        });
    }
});
const __TURBOPACK__default__export__ = customersSlice;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/slices/realTimeSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Real-time Slice - Real-time updates and connections
 */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "setConnected": (()=>setConnected),
    "setConnection": (()=>setConnection),
    "updateDriverLocation": (()=>updateDriverLocation),
    "updateOrderRealTime": (()=>updateOrderRealTime)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    connections: {},
    orderUpdates: {},
    driverLocations: {},
    isConnected: false,
    lastHeartbeat: 0
};
const realTimeSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'realTime',
    initialState,
    reducers: {
        setConnection: (state, action)=>{
            const { id, connected } = action.payload;
            state.connections[id] = connected;
        },
        updateOrderRealTime: (state, action)=>{
            const { orderId, update } = action.payload;
            state.orderUpdates[orderId] = update;
        },
        updateDriverLocation: (state, action)=>{
            const { driverId, location } = action.payload;
            state.driverLocations[driverId] = {
                ...location,
                timestamp: Date.now()
            };
        },
        setConnected: (state, action)=>{
            state.isConnected = action.payload;
            state.lastHeartbeat = Date.now();
        }
    }
});
const { setConnection, updateOrderRealTime, updateDriverLocation, setConnected } = realTimeSlice.actions;
const __TURBOPACK__default__export__ = realTimeSlice;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/slices/analyticsSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Analytics Slice - Analytics and reporting
 */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "fetchAnalytics": (()=>fetchAnalytics)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    metrics: {
        totalRevenue: 0,
        totalOrders: 0,
        averageOrderValue: 0,
        customerCount: 0
    },
    reports: [],
    loading: false,
    error: null
};
const fetchAnalytics = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('analytics/fetchAnalytics', async (params, { rejectWithValue })=>{
    try {
        // TODO: Implement actual analytics API call
        console.log('Fetching analytics with params:', params);
        return {
            metrics: {
                totalRevenue: 0,
                totalOrders: 0,
                averageOrderValue: 0,
                customerCount: 0
            },
            reports: []
        };
    } catch (error) {
        return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch analytics');
    }
});
const analyticsSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'analytics',
    initialState,
    reducers: {},
    extraReducers: (builder)=>{
        builder.addCase(fetchAnalytics.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(fetchAnalytics.fulfilled, (state, action)=>{
            state.metrics = action.payload.metrics;
            state.reports = action.payload.reports;
            state.loading = false;
        }).addCase(fetchAnalytics.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        });
    }
});
const __TURBOPACK__default__export__ = analyticsSlice;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/slices/notificationsSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Notifications Slice - Push notifications and alerts
 */ __turbopack_context__.s({
    "addNotification": (()=>addNotification),
    "default": (()=>__TURBOPACK__default__export__),
    "markAllAsRead": (()=>markAllAsRead),
    "markAsRead": (()=>markAsRead),
    "removeNotification": (()=>removeNotification)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    notifications: [],
    unreadCount: 0,
    loading: false,
    error: null
};
const notificationsSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'notifications',
    initialState,
    reducers: {
        addNotification: (state, action)=>{
            state.notifications.unshift(action.payload);
            if (!action.payload.read) {
                state.unreadCount += 1;
            }
        },
        markAsRead: (state, action)=>{
            const notification = state.notifications.find((n)=>n.id === action.payload);
            if (notification && !notification.read) {
                notification.read = true;
                state.unreadCount -= 1;
            }
        },
        markAllAsRead: (state)=>{
            state.notifications.forEach((n)=>n.read = true);
            state.unreadCount = 0;
        },
        removeNotification: (state, action)=>{
            const index = state.notifications.findIndex((n)=>n.id === action.payload);
            if (index >= 0) {
                const notification = state.notifications[index];
                if (!notification.read) {
                    state.unreadCount -= 1;
                }
                state.notifications.splice(index, 1);
            }
        }
    }
});
const { addNotification, markAsRead, markAllAsRead, removeNotification } = notificationsSlice.actions;
const __TURBOPACK__default__export__ = notificationsSlice;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/slices/adminSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Admin Slice - Admin panel state management
 */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "fetchDashboardStats": (()=>fetchDashboardStats),
    "setSelectedItems": (()=>setSelectedItems),
    "toggleBulkActions": (()=>toggleBulkActions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    dashboardStats: {
        totalUsers: 0,
        totalVendors: 0,
        totalDrivers: 0,
        totalOrders: 0,
        totalRevenue: 0,
        activeOrders: 0,
        pendingVendors: 0,
        pendingDrivers: 0
    },
    users: [],
    vendors: [],
    drivers: [],
    loading: false,
    error: null,
    selectedItems: [],
    bulkActions: false
};
const fetchDashboardStats = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])('admin/fetchDashboardStats', async (_, { rejectWithValue })=>{
    try {
        // TODO: Implement API call
        return {
            totalUsers: 0,
            totalVendors: 0,
            totalDrivers: 0,
            totalOrders: 0,
            totalRevenue: 0,
            activeOrders: 0,
            pendingVendors: 0,
            pendingDrivers: 0
        };
    } catch (error) {
        return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch dashboard stats');
    }
});
const adminSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'admin',
    initialState,
    reducers: {
        setSelectedItems: (state, action)=>{
            state.selectedItems = action.payload;
        },
        toggleBulkActions: (state)=>{
            state.bulkActions = !state.bulkActions;
            if (!state.bulkActions) {
                state.selectedItems = [];
            }
        }
    },
    extraReducers: (builder)=>{
        builder.addCase(fetchDashboardStats.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(fetchDashboardStats.fulfilled, (state, action)=>{
            state.dashboardStats = action.payload;
            state.loading = false;
        }).addCase(fetchDashboardStats.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        });
    }
});
const { setSelectedItems, toggleBulkActions } = adminSlice.actions;
const __TURBOPACK__default__export__ = adminSlice;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/slices/vendorSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Vendor Slice - Vendor panel state management
 */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "setMenu": (()=>setMenu),
    "setMenuEditMode": (()=>setMenuEditMode),
    "setRestaurant": (()=>setRestaurant),
    "setSelectedOrders": (()=>setSelectedOrders)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    restaurant: null,
    menu: [],
    orders: [],
    analytics: null,
    loading: false,
    error: null,
    menuEditMode: false,
    selectedOrders: []
};
const vendorSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'vendor',
    initialState,
    reducers: {
        setRestaurant: (state, action)=>{
            state.restaurant = action.payload;
        },
        setMenu: (state, action)=>{
            state.menu = action.payload;
        },
        setMenuEditMode: (state, action)=>{
            state.menuEditMode = action.payload;
        },
        setSelectedOrders: (state, action)=>{
            state.selectedOrders = action.payload;
        }
    }
});
const { setRestaurant, setMenu, setMenuEditMode, setSelectedOrders } = vendorSlice.actions;
const __TURBOPACK__default__export__ = vendorSlice;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/slices/driverPanelSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Driver Panel Slice - Driver panel state management
 */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "setCurrentDelivery": (()=>setCurrentDelivery),
    "setOnlineStatus": (()=>setOnlineStatus),
    "setProfile": (()=>setProfile),
    "updateEarnings": (()=>updateEarnings),
    "updateLocation": (()=>updateLocation)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    profile: null,
    currentDelivery: null,
    earnings: {
        today: 0,
        week: 0,
        month: 0,
        total: 0
    },
    isOnline: false,
    location: null,
    loading: false,
    error: null
};
const driverPanelSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'driverPanel',
    initialState,
    reducers: {
        setProfile: (state, action)=>{
            state.profile = action.payload;
        },
        setCurrentDelivery: (state, action)=>{
            state.currentDelivery = action.payload;
        },
        setOnlineStatus: (state, action)=>{
            state.isOnline = action.payload;
        },
        updateLocation: (state, action)=>{
            state.location = action.payload;
        },
        updateEarnings: (state, action)=>{
            state.earnings = {
                ...state.earnings,
                ...action.payload
            };
        }
    }
});
const { setProfile, setCurrentDelivery, setOnlineStatus, updateLocation, updateEarnings } = driverPanelSlice.actions;
const __TURBOPACK__default__export__ = driverPanelSlice;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/slices/cmsSliceSimple.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Simple CMS Redux Slice - Test Version
 */ __turbopack_context__.s({
    "clearError": (()=>clearError),
    "default": (()=>__TURBOPACK__default__export__),
    "selectCMSActiveTab": (()=>selectCMSActiveTab),
    "selectCMSCategories": (()=>selectCMSCategories),
    "selectCMSError": (()=>selectCMSError),
    "selectCMSLoading": (()=>selectCMSLoading),
    "selectCMSPages": (()=>selectCMSPages),
    "selectCMSPosts": (()=>selectCMSPosts),
    "selectCMSState": (()=>selectCMSState),
    "selectCMSStats": (()=>selectCMSStats),
    "selectCMSTags": (()=>selectCMSTags),
    "selectCMSViewMode": (()=>selectCMSViewMode),
    "selectIsLoading": (()=>selectIsLoading),
    "setActiveTab": (()=>setActiveTab),
    "setCategories": (()=>setCategories),
    "setError": (()=>setError),
    "setLoading": (()=>setLoading),
    "setPages": (()=>setPages),
    "setPosts": (()=>setPosts),
    "setStats": (()=>setStats),
    "setTags": (()=>setTags),
    "setViewMode": (()=>setViewMode)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    posts: [],
    pages: [],
    categories: [],
    tags: [],
    stats: {
        totalPosts: 0,
        publishedPosts: 0,
        draftPosts: 0,
        totalPages: 0,
        publishedPages: 0,
        totalCategories: 0,
        totalTags: 0,
        totalViews: 0,
        trashedPosts: 0,
        trashedPages: 0
    },
    loading: {
        global: false,
        posts: false,
        pages: false,
        categories: false,
        tags: false
    },
    activeTab: 'posts',
    viewMode: 'all',
    error: null,
    lastUpdated: null
};
const cmsSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: 'cms',
    initialState,
    reducers: {
        setActiveTab: (state, action)=>{
            state.activeTab = action.payload;
            state.error = null;
        },
        setViewMode: (state, action)=>{
            state.viewMode = action.payload;
            state.error = null;
        },
        setLoading: (state, action)=>{
            state.loading.global = action.payload;
        },
        setPosts: (state, action)=>{
            state.posts = action.payload;
        },
        setPages: (state, action)=>{
            state.pages = action.payload;
        },
        setCategories: (state, action)=>{
            state.categories = action.payload;
        },
        setTags: (state, action)=>{
            state.tags = action.payload;
        },
        setStats: (state, action)=>{
            state.stats = action.payload;
        },
        setError: (state, action)=>{
            state.error = action.payload;
        },
        clearError: (state)=>{
            state.error = null;
        }
    }
});
const { setActiveTab, setViewMode, setLoading, setPosts, setPages, setCategories, setTags, setStats, setError, clearError } = cmsSlice.actions;
const selectCMSState = (state)=>state.cms;
const selectCMSPosts = (state)=>state.cms.posts;
const selectCMSPages = (state)=>state.cms.pages;
const selectCMSCategories = (state)=>state.cms.categories;
const selectCMSTags = (state)=>state.cms.tags;
const selectCMSStats = (state)=>state.cms.stats;
const selectCMSLoading = (state)=>state.cms.loading;
const selectIsLoading = (state)=>{
    const loading = state.cms.loading;
    return loading.global || loading.posts || loading.pages || loading.categories || loading.tags;
};
const selectCMSActiveTab = (state)=>state.cms.activeTab;
const selectCMSViewMode = (state)=>state.cms.viewMode;
const selectCMSError = (state)=>state.cms.error;
const __TURBOPACK__default__export__ = cmsSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/api/apiSlice.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * API Slice - RTK Query configuration
 * Centralized API management for all Tap2Go endpoints
 */ __turbopack_context__.s({
    "apiSlice": (()=>apiSlice),
    "useGetOrdersQuery": (()=>useGetOrdersQuery),
    "useGetRestaurantsQuery": (()=>useGetRestaurantsQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$react$2f$rtk$2d$query$2d$react$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs [app-client] (ecmascript)");
;
// Simple base query
const baseQuery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchBaseQuery"])({
    baseUrl: '/api',
    prepareHeaders: (headers)=>{
        headers.set('content-type', 'application/json');
        return headers;
    }
});
const apiSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$react$2f$rtk$2d$query$2d$react$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createApi"])({
    reducerPath: 'api',
    baseQuery,
    tagTypes: [
        'User',
        'Restaurant',
        'MenuItem',
        'Order',
        'Driver',
        'Customer',
        'Vendor',
        'Admin',
        'Analytics',
        'Notification'
    ],
    endpoints: (builder)=>({
            // Basic endpoints - will be expanded later
            getRestaurants: builder.query({
                query: ()=>'/restaurants',
                providesTags: [
                    'Restaurant'
                ]
            }),
            getOrders: builder.query({
                query: ()=>'/orders',
                providesTags: [
                    'Order'
                ]
            })
        })
});
const { useGetRestaurantsQuery, useGetOrdersQuery } = apiSlice;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/middleware/analyticsMiddleware.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Analytics Middleware - Track user actions and events
 */ __turbopack_context__.s({
    "analyticsMiddleware": (()=>analyticsMiddleware)
});
const analyticsMiddleware = (store)=>(next)=>(action)=>{
            // Type assertion for action
            const typedAction = action;
            // Track specific actions for analytics
            const trackableActions = [
                'cart/addToCart',
                'cart/removeFromCart',
                'orders/createOrder',
                'auth/signIn',
                'auth/signUp',
                'restaurants/setCurrentRestaurant'
            ];
            if (trackableActions.includes(typedAction.type)) {
                // Send analytics event
                try {
                    // TODO: Integrate with your analytics service (Google Analytics, Mixpanel, etc.)
                    console.log('Analytics Event:', {
                        action: typedAction.type,
                        payload: typedAction.payload,
                        timestamp: new Date().toISOString(),
                        userId: store.getState().auth.user?.id
                    });
                } catch (error) {
                    console.error('Analytics tracking error:', error);
                }
            }
            return next(action);
        };
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/middleware/realTimeMiddleware.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Real-time Middleware - Handle Firebase real-time updates
 */ __turbopack_context__.s({
    "realTimeMiddleware": (()=>realTimeMiddleware)
});
const realTimeMiddleware = (store)=>(next)=>(action)=>{
            // Type assertion for action
            const typedAction = action;
            // Handle real-time connection setup
            if (typedAction.type === 'auth/setUser' && typedAction.payload) {
                // Set up real-time listeners when user logs in
                setupRealTimeListeners(store, typedAction.payload);
            }
            // Handle real-time disconnection
            if (typedAction.type === 'auth/clearAuth') {
                // Clean up real-time listeners when user logs out
                cleanupRealTimeListeners();
            }
            return next(action);
        };
let realTimeListeners = [];
function setupRealTimeListeners(store, user) {
    // TODO: Set up Firebase real-time listeners based on user role
    console.log('Setting up real-time listeners for user:', user.id);
    // Example: Listen to order updates for vendors
    if (user.role === 'vendor') {
    // Set up vendor-specific listeners
    }
    // Example: Listen to delivery updates for drivers
    if (user.role === 'driver') {
    // Set up driver-specific listeners
    }
    // Example: Listen to order status for customers
    if (user.role === 'customer') {
    // Set up customer-specific listeners
    }
}
function cleanupRealTimeListeners() {
    realTimeListeners.forEach((unsubscribe)=>unsubscribe());
    realTimeListeners = [];
    console.log('Cleaned up real-time listeners');
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/middleware/errorMiddleware.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Error Middleware - Centralized error handling
 */ __turbopack_context__.s({
    "errorMiddleware": (()=>errorMiddleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$uiSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/slices/uiSlice.ts [app-client] (ecmascript)");
;
const errorMiddleware = (store)=>(next)=>(action)=>{
            // Type assertion for action
            const typedAction = action;
            // Handle rejected async thunks
            if (typedAction.type.endsWith('/rejected')) {
                const error = typedAction.payload || typedAction.error?.message || 'An error occurred';
                // Show error notification
                store.dispatch((0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$uiSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["showErrorNotification"])({
                    title: 'Error',
                    message: error
                }));
                // Log error for debugging
                console.error('Redux Error:', {
                    action: typedAction.type,
                    error: error,
                    timestamp: new Date().toISOString()
                });
            }
            return next(action);
        };
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/middleware/serializationMiddleware.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Serialization Middleware - Automatically handles Firebase Timestamps
 * Converts all Firebase Timestamps to ISO strings before they reach Redux
 */ __turbopack_context__.s({
    "serializationMiddleware": (()=>serializationMiddleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$firebase$2f$firestore$2f$dist$2f$esm$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/firebase/firestore/dist/esm/index.esm.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/firestore/dist/index.esm2017.js [app-client] (ecmascript)");
;
/**
 * Recursively serialize Firebase Timestamps in any object
 */ const serializeFirebaseData = (obj)=>{
    if (obj === null || obj === undefined) {
        return obj;
    }
    // Handle Firebase Timestamp
    if (obj instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Timestamp"]) {
        return obj.toDate().toISOString();
    }
    // Handle Date objects
    if (obj instanceof Date) {
        return obj.toISOString();
    }
    // Handle arrays
    if (Array.isArray(obj)) {
        return obj.map(serializeFirebaseData);
    }
    // Handle objects
    if (typeof obj === 'object' && obj !== null && obj.constructor === Object) {
        const serialized = {};
        for(const key in obj){
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                serialized[key] = serializeFirebaseData(obj[key]);
            }
        }
        return serialized;
    }
    // Return primitive values as-is
    return obj;
};
const serializationMiddleware = ()=>(next)=>(action)=>{
            // Type assertion for action
            const typedAction = action;
            // List of actions that might contain Firebase data
            const actionsToSerialize = [
                'auth/setUser',
                'auth/syncAuthState',
                'auth/updateUserData',
                'auth/signIn/fulfilled',
                'auth/signUp/fulfilled',
                'auth/updateUserProfile/fulfilled'
            ];
            // Check if this action needs serialization
            if (actionsToSerialize.includes(typedAction.type)) {
                // Create a new action with serialized payload
                const serializedAction = {
                    ...typedAction,
                    payload: serializeFirebaseData(typedAction.payload)
                };
                return next(serializedAction);
            }
            // For other actions, just pass them through
            return next(action);
        };
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/store/index.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Tap2Go Redux Store Configuration
 * Enterprise-grade state management for FoodPanda-level complexity
 * 
 * Architecture:
 * - Respects existing Firebase/Auth patterns
 * - Integrates with existing service layer
 * - Supports multi-panel architecture (Admin, Vendor, Driver, Customer)
 * - Optimized for real-time updates and scalability
 */ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "persistor": (()=>persistor),
    "store": (()=>store)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2f$dist$2f$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/redux/dist/redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$redux$2d$persist$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/redux-persist/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistStore$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/redux-persist/es/persistStore.js [app-client] (ecmascript) <export default as persistStore>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/redux-persist/es/persistReducer.js [app-client] (ecmascript) <export default as persistReducer>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/redux-persist/lib/storage/index.js [app-client] (ecmascript)");
// Core slices
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/slices/authSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$uiSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/slices/uiSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$cartSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/slices/cartSlice.ts [app-client] (ecmascript)");
// Business logic slices
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$ordersSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/slices/ordersSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$restaurantsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/slices/restaurantsSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$driversSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/slices/driversSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$customersSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/slices/customersSlice.ts [app-client] (ecmascript)");
// Advanced features
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$realTimeSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/slices/realTimeSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$analyticsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/slices/analyticsSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$notificationsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/slices/notificationsSlice.ts [app-client] (ecmascript)");
// Panel-specific slices
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$adminSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/slices/adminSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$vendorSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/slices/vendorSlice.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$driverPanelSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/slices/driverPanelSlice.ts [app-client] (ecmascript)");
// CMS slice
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$cmsSliceSimple$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/slices/cmsSliceSimple.ts [app-client] (ecmascript)");
// API slice for RTK Query
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$api$2f$apiSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/api/apiSlice.ts [app-client] (ecmascript)");
// Middleware
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$middleware$2f$analyticsMiddleware$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/middleware/analyticsMiddleware.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$middleware$2f$realTimeMiddleware$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/middleware/realTimeMiddleware.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$middleware$2f$errorMiddleware$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/middleware/errorMiddleware.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$middleware$2f$serializationMiddleware$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/middleware/serializationMiddleware.ts [app-client] (ecmascript)");
// Store setup listener for RTK Query
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// Root reducer configuration
const rootReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$redux$2f$dist$2f$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["combineReducers"])({
    // Core state
    auth: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$authSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].reducer,
    ui: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$uiSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].reducer,
    cart: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$cartSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].reducer,
    // Business logic
    orders: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$ordersSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].reducer,
    restaurants: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$restaurantsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].reducer,
    drivers: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$driversSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].reducer,
    customers: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$customersSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].reducer,
    // Advanced features
    realTime: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$realTimeSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].reducer,
    analytics: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$analyticsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].reducer,
    notifications: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$notificationsSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].reducer,
    // Panel-specific
    admin: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$adminSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].reducer,
    vendor: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$vendorSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].reducer,
    driverPanel: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$driverPanelSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].reducer,
    // CMS
    cms: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$slices$2f$cmsSliceSimple$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    // API
    api: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$api$2f$apiSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiSlice"].reducer
});
// Persistence configuration
const persistConfig = {
    key: 'tap2go-root',
    version: 1,
    storage: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$redux$2d$persist$2f$lib$2f$storage$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    // Only persist essential data for performance
    whitelist: [
        'cart',
        'ui',
        'auth'
    ],
    blacklist: [
        'api',
        'realTime',
        'analytics'
    ]
};
const persistedReducer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistReducer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistReducer$3e$__["persistReducer"])(persistConfig, rootReducer);
const store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["configureStore"])({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware)=>{
        const middleware = getDefaultMiddleware({
            serializableCheck: ("TURBOPACK compile-time truthy", 1) ? false : ("TURBOPACK unreachable", undefined),
            // Enable immutability checks in development
            immutableCheck: ("TURBOPACK compile-time value", "development") === 'development'
        });
        return middleware.concat(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$middleware$2f$serializationMiddleware$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["serializationMiddleware"]).concat(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$api$2f$apiSlice$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiSlice"].middleware).concat(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$middleware$2f$analyticsMiddleware$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["analyticsMiddleware"]).concat(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$middleware$2f$realTimeMiddleware$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["realTimeMiddleware"]).concat(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$middleware$2f$errorMiddleware$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["errorMiddleware"]);
    },
    // Enable Redux DevTools in development
    devTools: ("TURBOPACK compile-time value", "development") === 'development'
});
const persistor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$redux$2d$persist$2f$es$2f$persistStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__persistStore$3e$__["persistStore"])(store);
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$query$2f$rtk$2d$query$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setupListeners"])(store.dispatch);
const __TURBOPACK__default__export__ = store;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
 /**
 * Store Architecture Notes:
 * 
 * 1. **Scalability**: Each slice handles specific domain logic
 * 2. **Performance**: Selective persistence and memoized selectors
 * 3. **Real-time**: Dedicated middleware for Firebase real-time updates
 * 4. **Multi-panel**: Separate slices for different user roles
 * 5. **Analytics**: Built-in analytics tracking middleware
 * 6. **Error handling**: Centralized error management
 * 7. **API management**: RTK Query for all server communication
 * 8. **Persistence**: Smart persistence strategy for offline support
 */ }}),
"[project]/apps/web/src/store/ReduxProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Redux Provider - Wraps the app with Redux store and persistence
 * Integrates seamlessly with your existing provider structure
 */ __turbopack_context__.s({
    "default": (()=>ReduxProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$redux$2d$persist$2f$es$2f$integration$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/redux-persist/es/integration/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/store/index.ts [app-client] (ecmascript)");
'use client';
;
;
;
;
function ReduxProvider({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Provider"], {
        store: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["store"],
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$redux$2d$persist$2f$es$2f$integration$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PersistGate"], {
            loading: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 z-[9999] flex items-center justify-center bg-white",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col items-center space-y-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-12 h-12 border-4 border-orange-500 border-t-transparent rounded-full animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/store/ReduxProvider.tsx",
                            lineNumber: 30,
                            columnNumber: 15
                        }, void 0),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 font-medium",
                            children: "Loading Tap2Go..."
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/store/ReduxProvider.tsx",
                            lineNumber: 31,
                            columnNumber: 15
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "[project]/apps/web/src/store/ReduxProvider.tsx",
                    lineNumber: 29,
                    columnNumber: 13
                }, void 0)
            }, void 0, false, {
                fileName: "[project]/apps/web/src/store/ReduxProvider.tsx",
                lineNumber: 28,
                columnNumber: 11
            }, void 0),
            persistor: __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$store$2f$index$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["persistor"],
            children: children
        }, void 0, false, {
            fileName: "[project]/apps/web/src/store/ReduxProvider.tsx",
            lineNumber: 26,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/apps/web/src/store/ReduxProvider.tsx",
        lineNumber: 25,
        columnNumber: 5
    }, this);
}
_c = ReduxProvider;
var _c;
__turbopack_context__.k.register(_c, "ReduxProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/hooks/useChatbot.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useChatbot": (()=>useChatbot)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
;
function useChatbot() {
    _s();
    const [state, setState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        messages: [],
        loading: false,
        error: null,
        isTyping: false
    });
    // Add welcome message on first load
    const [hasWelcomed, setHasWelcomed] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    if (!hasWelcomed && state.messages.length === 0) {
        setState((prev)=>({
                ...prev,
                messages: [
                    {
                        id: `welcome_${Date.now()}`,
                        role: 'assistant',
                        content: "Hi! I'm an AI assistant. I can help you with any questions or topics you'd like to discuss. How can I help you today?",
                        timestamp: new Date().toISOString()
                    }
                ]
            }));
        setHasWelcomed(true);
    }
    // Send message - No sessions needed!
    const sendMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useChatbot.useCallback[sendMessage]": async (message)=>{
            if (!message.trim()) return;
            // Add user message immediately
            const userMessage = {
                id: `user_${Date.now()}`,
                role: 'user',
                content: message.trim(),
                timestamp: new Date().toISOString()
            };
            setState({
                "useChatbot.useCallback[sendMessage]": (prev)=>({
                        ...prev,
                        messages: [
                            ...prev.messages,
                            userMessage
                        ],
                        isTyping: true,
                        error: null
                    })
            }["useChatbot.useCallback[sendMessage]"]);
            try {
                // Send to simple chat API
                const response = await fetch('/api/chatbot/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message.trim(),
                        conversationHistory: state.messages.slice(-6)
                    })
                });
                const data = await response.json();
                if (!response.ok) {
                    throw new Error(data.error || 'Failed to send message');
                }
                // Add AI response
                setState({
                    "useChatbot.useCallback[sendMessage]": (prev)=>({
                            ...prev,
                            messages: [
                                ...prev.messages,
                                data.message
                            ],
                            isTyping: false
                        })
                }["useChatbot.useCallback[sendMessage]"]);
                return data.message;
            } catch (error) {
                console.error('Error sending message:', error);
                setState({
                    "useChatbot.useCallback[sendMessage]": (prev)=>({
                            ...prev,
                            isTyping: false,
                            error: error instanceof Error ? error.message : 'Failed to send message'
                        })
                }["useChatbot.useCallback[sendMessage]"]);
                throw error;
            }
        }
    }["useChatbot.useCallback[sendMessage]"], [
        state.messages
    ]);
    // Clear chat
    const clearChat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useChatbot.useCallback[clearChat]": ()=>{
            setState({
                messages: [],
                loading: false,
                error: null,
                isTyping: false
            });
            setHasWelcomed(false);
        }
    }["useChatbot.useCallback[clearChat]"], []);
    return {
        // State
        messages: Array.isArray(state.messages) ? state.messages : [],
        loading: state.loading,
        error: state.error,
        isTyping: state.isTyping,
        // Actions
        sendMessage,
        clearChat
    };
}
_s(useChatbot, "kYXH1DH5ZeqohCn+cf4mcxehT98=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/components/chatbot/ChatMessage.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ChatMessage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
'use client';
;
function ChatMessage({ message }) {
    const isUser = message.role === 'user';
    const isEscalated = message.metadata?.escalated;
    const formatTime = (timestamp)=>{
        const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `flex ${isUser ? 'justify-end' : 'justify-start'} mb-2`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `flex items-end space-x-2 max-w-[85%] ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`,
            children: [
                !isUser && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "w-7 h-7 rounded-full bg-gradient-to-r from-orange-400 to-orange-600 flex items-center justify-center flex-shrink-0 mb-1",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-white text-xs font-bold",
                        children: "T2G"
                    }, void 0, false, {
                        fileName: "[project]/apps/web/src/components/chatbot/ChatMessage.tsx",
                        lineNumber: 29,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/apps/web/src/components/chatbot/ChatMessage.tsx",
                    lineNumber: 28,
                    columnNumber: 11
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `message-bubble px-3 py-2 max-w-xs lg:max-w-md ${isUser ? 'bg-orange-500 text-white rounded-2xl rounded-br-md' : isEscalated ? 'bg-red-50 border border-red-200 text-red-800 rounded-2xl rounded-bl-md' : 'bg-gray-100 text-gray-900 rounded-2xl rounded-bl-md'}`,
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm leading-relaxed whitespace-pre-wrap break-words",
                                    children: message.content
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/src/components/chatbot/ChatMessage.tsx",
                                    lineNumber: 44,
                                    columnNumber: 13
                                }, this),
                                isEscalated && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mt-2 p-2 bg-red-100 rounded-lg border border-red-200",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-xs text-red-600 font-medium",
                                        children: "🚨 This conversation has been escalated to our human support team"
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/src/components/chatbot/ChatMessage.tsx",
                                        lineNumber: 51,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/src/components/chatbot/ChatMessage.tsx",
                                    lineNumber: 50,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/src/components/chatbot/ChatMessage.tsx",
                            lineNumber: 35,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `text-xs mt-1 px-1 ${isUser ? 'text-right text-gray-500' : 'text-left text-gray-500'}`,
                            children: [
                                formatTime(message.timestamp),
                                message.metadata?.confidence && ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "ml-2",
                                    children: [
                                        "• ",
                                        Math.round(message.metadata.confidence * 100),
                                        "%"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/apps/web/src/components/chatbot/ChatMessage.tsx",
                                    lineNumber: 62,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/src/components/chatbot/ChatMessage.tsx",
                            lineNumber: 59,
                            columnNumber: 11
                        }, this),
                        !isUser && message.metadata?.intent && ("TURBOPACK compile-time value", "development") === 'development' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-1",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "inline-block px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded-full",
                                children: [
                                    "Intent: ",
                                    message.metadata.intent
                                ]
                            }, void 0, true, {
                                fileName: "[project]/apps/web/src/components/chatbot/ChatMessage.tsx",
                                lineNumber: 71,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/components/chatbot/ChatMessage.tsx",
                            lineNumber: 70,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/apps/web/src/components/chatbot/ChatMessage.tsx",
                    lineNumber: 33,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/apps/web/src/components/chatbot/ChatMessage.tsx",
            lineNumber: 25,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/apps/web/src/components/chatbot/ChatMessage.tsx",
        lineNumber: 24,
        columnNumber: 5
    }, this);
}
_c = ChatMessage;
var _c;
__turbopack_context__.k.register(_c, "ChatMessage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/components/chatbot/ChatInput.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ChatInput)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$PaperAirplaneIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PaperAirplaneIcon$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@heroicons/react/24/outline/esm/PaperAirplaneIcon.js [app-client] (ecmascript) <export default as PaperAirplaneIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$MicrophoneIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MicrophoneIcon$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@heroicons/react/24/outline/esm/MicrophoneIcon.js [app-client] (ecmascript) <export default as MicrophoneIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$FaceSmileIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FaceSmileIcon$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@heroicons/react/24/outline/esm/FaceSmileIcon.js [app-client] (ecmascript) <export default as FaceSmileIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$PhotoIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PhotoIcon$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js [app-client] (ecmascript) <export default as PhotoIcon>");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
function ChatInput({ onSendMessage, disabled = false, placeholder = "Type your message..." }) {
    _s();
    const [message, setMessage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('');
    const [isRecording, setIsRecording] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const textareaRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Auto-resize textarea
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ChatInput.useEffect": ()=>{
            if (textareaRef.current) {
                textareaRef.current.style.height = 'auto';
                textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
            }
        }
    }["ChatInput.useEffect"], [
        message
    ]);
    // Focus on textarea when component mounts
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ChatInput.useEffect": ()=>{
            if (textareaRef.current && !disabled) {
                textareaRef.current.focus();
            }
        }
    }["ChatInput.useEffect"], [
        disabled
    ]);
    const handleSubmit = (e)=>{
        e.preventDefault();
        if (message.trim() && !disabled) {
            onSendMessage(message.trim());
            setMessage('');
            // Reset textarea height
            if (textareaRef.current) {
                textareaRef.current.style.height = 'auto';
            }
        }
    };
    const handleKeyPress = (e)=>{
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSubmit(e);
        }
    };
    const handleVoiceInput = ()=>{
        // Voice input functionality can be implemented here
        // For now, just toggle recording state
        setIsRecording(!isRecording);
        // Placeholder for voice recognition
        if (!isRecording) {
            // Start recording
            console.log('Voice recording started');
        } else {
            // Stop recording
            console.log('Voice recording stopped');
        }
    };
    const canSend = message.trim().length > 0 && !disabled;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white border-t border-gray-200 max-md:flex-shrink-0",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "px-4 py-3 max-md:px-3 max-md:py-2",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                onSubmit: handleSubmit,
                className: "flex items-end space-x-2",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center space-x-1",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "button",
                            disabled: disabled,
                            className: `p-2 rounded-full transition-colors duration-200 ${disabled ? 'opacity-50 cursor-not-allowed' : 'text-orange-500 hover:bg-orange-50'}`,
                            title: "Add photo",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$PhotoIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PhotoIcon$3e$__["PhotoIcon"], {
                                className: "w-5 h-5"
                            }, void 0, false, {
                                fileName: "[project]/apps/web/src/components/chatbot/ChatInput.tsx",
                                lineNumber: 89,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/components/chatbot/ChatInput.tsx",
                            lineNumber: 81,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/apps/web/src/components/chatbot/ChatInput.tsx",
                        lineNumber: 80,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1 relative",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "chat-input-container flex items-end bg-gray-100 rounded-2xl px-3 py-2 min-h-[40px] transition-all duration-200 focus-within:bg-gray-50",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                        ref: textareaRef,
                                        value: message,
                                        onChange: (e)=>setMessage(e.target.value),
                                        onKeyPress: handleKeyPress,
                                        placeholder: disabled ? 'Chat is disabled' : placeholder,
                                        disabled: disabled,
                                        rows: 1,
                                        className: "flex-1 bg-transparent resize-none outline-none text-gray-900 placeholder:text-gray-500 text-sm leading-5",
                                        style: {
                                            minHeight: '20px',
                                            maxHeight: '100px'
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/src/components/chatbot/ChatInput.tsx",
                                        lineNumber: 96,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        type: "button",
                                        disabled: disabled,
                                        className: `ml-2 p-1 rounded-full transition-colors duration-200 ${disabled ? 'opacity-50 cursor-not-allowed' : 'text-gray-500 hover:text-orange-500'}`,
                                        title: "Add emoji",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$FaceSmileIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__FaceSmileIcon$3e$__["FaceSmileIcon"], {
                                            className: "w-5 h-5"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/src/components/chatbot/ChatInput.tsx",
                                            lineNumber: 120,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/src/components/chatbot/ChatInput.tsx",
                                        lineNumber: 112,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/apps/web/src/components/chatbot/ChatInput.tsx",
                                lineNumber: 95,
                                columnNumber: 13
                            }, this),
                            message.length > 800 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute -top-6 right-2 text-xs text-gray-500 bg-white px-1 rounded",
                                children: [
                                    message.length,
                                    "/1000"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/apps/web/src/components/chatbot/ChatInput.tsx",
                                lineNumber: 126,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/apps/web/src/components/chatbot/ChatInput.tsx",
                        lineNumber: 94,
                        columnNumber: 11
                    }, this),
                    message.trim() ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "submit",
                        disabled: !canSend,
                        className: `flex-shrink-0 p-2 rounded-full transition-all duration-200 ${canSend ? 'bg-orange-500 text-white hover:bg-orange-600 transform hover:scale-105' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`,
                        title: "Send message",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$PaperAirplaneIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__PaperAirplaneIcon$3e$__["PaperAirplaneIcon"], {
                            className: "w-5 h-5"
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/components/chatbot/ChatInput.tsx",
                            lineNumber: 144,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/apps/web/src/components/chatbot/ChatInput.tsx",
                        lineNumber: 134,
                        columnNumber: 13
                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        onClick: handleVoiceInput,
                        disabled: disabled,
                        className: `flex-shrink-0 p-2 rounded-full transition-colors duration-200 ${isRecording ? 'bg-red-500 text-white animate-pulse' : disabled ? 'bg-gray-200 text-gray-400 cursor-not-allowed' : 'bg-orange-500 text-white hover:bg-orange-600'}`,
                        title: isRecording ? 'Stop recording' : 'Voice message',
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$MicrophoneIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MicrophoneIcon$3e$__["MicrophoneIcon"], {
                            className: "w-5 h-5"
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/components/chatbot/ChatInput.tsx",
                            lineNumber: 160,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/apps/web/src/components/chatbot/ChatInput.tsx",
                        lineNumber: 147,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/apps/web/src/components/chatbot/ChatInput.tsx",
                lineNumber: 78,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/apps/web/src/components/chatbot/ChatInput.tsx",
            lineNumber: 77,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/apps/web/src/components/chatbot/ChatInput.tsx",
        lineNumber: 75,
        columnNumber: 5
    }, this);
}
_s(ChatInput, "aYv+hZs1nwx4Qbv/OsVNXoGN27Q=");
_c = ChatInput;
var _c;
__turbopack_context__.k.register(_c, "ChatInput");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/components/chatbot/ChatWindow.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ChatWindow)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$XMarkIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XMarkIcon$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js [app-client] (ecmascript) <export default as XMarkIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$MinusIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MinusIcon$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@heroicons/react/24/outline/esm/MinusIcon.js [app-client] (ecmascript) <export default as MinusIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$hooks$2f$useChatbot$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/hooks/useChatbot.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$components$2f$chatbot$2f$ChatMessage$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/components/chatbot/ChatMessage.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$components$2f$chatbot$2f$ChatInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/components/chatbot/ChatInput.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
function ChatWindow({ isOpen, onClose, onMinimize, isMinimized, position = 'bottom-right' }) {
    _s();
    const { messages, loading, error, isTyping, sendMessage, clearChat } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$hooks$2f$useChatbot$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useChatbot"])();
    const messagesEndRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Auto-scroll to bottom when new messages arrive
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ChatWindow.useEffect": ()=>{
            if (messagesEndRef.current) {
                messagesEndRef.current.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        }
    }["ChatWindow.useEffect"], [
        messages,
        isTyping
    ]);
    // Handle sending messages - Simple!
    const handleSendMessage = async (message)=>{
        try {
            await sendMessage(message);
        } catch (error) {
            console.error('Failed to send message:', error);
        }
    };
    // Handle restart chat
    const handleRestartChat = ()=>{
        clearChat();
    };
    if (!isOpen) return null;
    // Position classes for chat window
    const windowPositionClasses = {
        'bottom-right': 'bottom-4 right-4',
        'bottom-left': 'bottom-4 left-4'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `fixed ${windowPositionClasses[position]} z-50 max-md:inset-0`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `chat-widget bg-white rounded-lg shadow-2xl border border-gray-200 transition-all duration-300 max-md:rounded-none max-md:h-screen max-md:w-full max-md:flex max-md:flex-col ${isMinimized ? 'w-80 h-16 max-md:w-full max-md:h-16' : 'w-96 h-[600px] max-md:w-full max-md:h-screen'}`,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center justify-between px-4 py-3 bg-white border-b border-gray-200 rounded-t-lg",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-3",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "relative",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-10 h-10 rounded-full bg-gradient-to-r from-orange-400 to-orange-600 flex items-center justify-center",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-white text-sm font-bold",
                                                children: "T2G"
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                                lineNumber: 83,
                                                columnNumber: 17
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                            lineNumber: 82,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                            lineNumber: 85,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                    lineNumber: 81,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "font-semibold text-gray-900 text-sm",
                                            children: "Tap2Go Assistant"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                            lineNumber: 88,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-xs text-green-600 font-medium",
                                            children: "Active now"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                            lineNumber: 89,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                    lineNumber: 87,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                            lineNumber: 79,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-1",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: onMinimize,
                                    className: "p-2 hover:bg-gray-100 rounded-full transition-colors",
                                    title: isMinimized ? "Expand chat" : "Minimize chat",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$MinusIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__MinusIcon$3e$__["MinusIcon"], {
                                        className: "w-5 h-5 text-gray-600"
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                        lineNumber: 100,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                    lineNumber: 95,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: onClose,
                                    className: "p-2 hover:bg-gray-100 rounded-full transition-colors",
                                    title: "Close chat",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$XMarkIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__XMarkIcon$3e$__["XMarkIcon"], {
                                        className: "w-5 h-5 text-gray-600"
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                        lineNumber: 107,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                    lineNumber: 102,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                            lineNumber: 93,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                    lineNumber: 78,
                    columnNumber: 9
                }, this),
                !isMinimized && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex-1 overflow-y-auto p-4 h-[440px] max-md:flex-1 max-md:h-auto bg-white",
                            children: loading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-center h-full",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-2"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                            lineNumber: 122,
                                            columnNumber: 21
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-gray-600 text-sm",
                                            children: "Starting chat..."
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                            lineNumber: 123,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                    lineNumber: 121,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                lineNumber: 120,
                                columnNumber: 17
                            }, this) : error ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center justify-center h-full",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-red-500 mb-2",
                                            children: "⚠️"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                            lineNumber: 129,
                                            columnNumber: 21
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-red-600 text-sm mb-3",
                                            children: error
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                            lineNumber: 130,
                                            columnNumber: 21
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: handleRestartChat,
                                            className: "px-4 py-2 bg-orange-500 text-white rounded-lg text-sm hover:bg-orange-600 transition-colors duration-200",
                                            children: "Try Again"
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                            lineNumber: 131,
                                            columnNumber: 21
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                    lineNumber: 128,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                lineNumber: 127,
                                columnNumber: 17
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    Array.isArray(messages) && messages.length === 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mb-4 flex justify-center",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "bg-gray-100 px-3 py-1 rounded-full",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-gray-600 text-xs text-center",
                                                children: "👋 Welcome to Tap2Go! I'm here to help you with orders, restaurant info, delivery questions, and more."
                                            }, void 0, false, {
                                                fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                                lineNumber: 145,
                                                columnNumber: 25
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                            lineNumber: 144,
                                            columnNumber: 23
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                        lineNumber: 143,
                                        columnNumber: 21
                                    }, this),
                                    Array.isArray(messages) && messages.map((message)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$components$2f$chatbot$2f$ChatMessage$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            message: message
                                        }, message.id, false, {
                                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                            lineNumber: 154,
                                            columnNumber: 21
                                        }, this)),
                                    isTyping && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex justify-start mb-2",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-end space-x-2 max-w-[85%]",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-7 h-7 rounded-full bg-gradient-to-r from-orange-400 to-orange-600 flex items-center justify-center flex-shrink-0 mb-1",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-white text-xs font-bold",
                                                        children: "T2G"
                                                    }, void 0, false, {
                                                        fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                                        lineNumber: 166,
                                                        columnNumber: 27
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                                    lineNumber: 165,
                                                    columnNumber: 25
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "bg-gray-100 rounded-2xl rounded-bl-md px-3 py-2",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex space-x-1",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                                                            }, void 0, false, {
                                                                fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                                                lineNumber: 172,
                                                                columnNumber: 29
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "w-2 h-2 bg-gray-400 rounded-full animate-bounce",
                                                                style: {
                                                                    animationDelay: '0.1s'
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                                                lineNumber: 173,
                                                                columnNumber: 29
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "w-2 h-2 bg-gray-400 rounded-full animate-bounce",
                                                                style: {
                                                                    animationDelay: '0.2s'
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                                                lineNumber: 174,
                                                                columnNumber: 29
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                                        lineNumber: 171,
                                                        columnNumber: 27
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                                    lineNumber: 170,
                                                    columnNumber: 25
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                            lineNumber: 163,
                                            columnNumber: 23
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                        lineNumber: 162,
                                        columnNumber: 21
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        ref: messagesEndRef
                                    }, void 0, false, {
                                        fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                        lineNumber: 182,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true)
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                            lineNumber: 118,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$components$2f$chatbot$2f$ChatInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            onSendMessage: handleSendMessage,
                            disabled: loading,
                            placeholder: "Ask me anything about Tap2Go..."
                        }, void 0, false, {
                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                            lineNumber: 188,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true),
                isMinimized && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "p-4 flex items-center justify-between",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center space-x-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-2 h-2 bg-green-500 rounded-full animate-pulse"
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                    lineNumber: 200,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm text-gray-600",
                                    children: "Chat minimized"
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                                    lineNumber: 201,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                            lineNumber: 199,
                            columnNumber: 13
                        }, this),
                        Array.isArray(messages) && messages.length > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-xs text-gray-500",
                            children: [
                                messages.length - 1,
                                " message",
                                messages.length > 2 ? 's' : ''
                            ]
                        }, void 0, true, {
                            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                            lineNumber: 204,
                            columnNumber: 15
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
                    lineNumber: 198,
                    columnNumber: 11
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
            lineNumber: 72,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/apps/web/src/components/chatbot/ChatWindow.tsx",
        lineNumber: 71,
        columnNumber: 5
    }, this);
}
_s(ChatWindow, "cwhPeVssMZz7Mm8mLESGqMgDvTk=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$hooks$2f$useChatbot$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useChatbot"]
    ];
});
_c = ChatWindow;
var _c;
__turbopack_context__.k.register(_c, "ChatWindow");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/apps/web/src/components/chatbot/ChatWidget.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ChatWidget)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ChatBubbleLeftRightIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChatBubbleLeftRightIcon$3e$__ = __turbopack_context__.i("[project]/apps/web/node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js [app-client] (ecmascript) <export default as ChatBubbleLeftRightIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$components$2f$chatbot$2f$ChatWindow$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/apps/web/src/components/chatbot/ChatWindow.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function ChatWidget({ customerInfo, position = 'bottom-right', autoOpen = false, theme = 'orange' }) {
    _s();
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isMinimized, setIsMinimized] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [hasInteracted, setHasInteracted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Theme colors
    const themeColors = {
        orange: {
            primary: 'from-orange-500 to-orange-600',
            hover: 'hover:from-orange-600 hover:to-orange-700',
            text: 'text-orange-600',
            bg: 'bg-orange-50',
            border: 'border-orange-200'
        },
        blue: {
            primary: 'from-blue-500 to-blue-600',
            hover: 'hover:from-blue-600 hover:to-blue-700',
            text: 'text-blue-600',
            bg: 'bg-blue-50',
            border: 'border-blue-200'
        },
        green: {
            primary: 'from-green-500 to-green-600',
            hover: 'hover:from-green-600 hover:to-green-700',
            text: 'text-green-600',
            bg: 'bg-green-50',
            border: 'border-green-200'
        }
    };
    const colors = themeColors[theme];
    // Position classes - Added 20px more space (bottom-12 -> bottom-20, bottom-16 -> bottom-20)
    const positionClasses = {
        'bottom-right': 'bottom-20 right-4 max-md:bottom-20',
        'bottom-left': 'bottom-20 left-4 max-md:bottom-20'
    };
    // Auto-open after delay
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ChatWidget.useEffect": ()=>{
            if (autoOpen && !hasInteracted) {
                const timer = setTimeout({
                    "ChatWidget.useEffect.timer": ()=>{
                        setIsOpen(true);
                    }
                }["ChatWidget.useEffect.timer"], 3000); // Open after 3 seconds
                return ({
                    "ChatWidget.useEffect": ()=>clearTimeout(timer)
                })["ChatWidget.useEffect"];
            }
        }
    }["ChatWidget.useEffect"], [
        autoOpen,
        hasInteracted
    ]);
    // Disable welcome message - focus on pure chat
    // useEffect(() => {
    //   if (showWelcomeMessage && !hasInteracted && !isOpen) {
    //     const timer = setTimeout(() => {
    //       setShowWelcome(true);
    //     }, 2000);
    //     return () => clearTimeout(timer);
    //   }
    // }, [showWelcomeMessage, hasInteracted, isOpen]);
    // useEffect(() => {
    //   if (showWelcome) {
    //     const timer = setTimeout(() => {
    //       setShowWelcome(false);
    //     }, 8000);
    //     return () => clearTimeout(timer);
    //   }
    // }, [showWelcome]);
    const handleToggleChat = ()=>{
        setHasInteracted(true);
        if (isOpen) {
            if (isMinimized) {
                setIsMinimized(false);
            } else {
                setIsOpen(false);
                setIsMinimized(false);
            }
        } else {
            setIsOpen(true);
            setIsMinimized(false);
        }
    };
    const handleCloseChat = ()=>{
        setIsOpen(false);
        setIsMinimized(false);
    };
    const handleMinimizeChat = ()=>{
        setIsMinimized(!isMinimized);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$src$2f$components$2f$chatbot$2f$ChatWindow$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                isOpen: isOpen,
                onClose: handleCloseChat,
                onMinimize: handleMinimizeChat,
                isMinimized: isMinimized,
                position: position,
                customerInfo: customerInfo
            }, void 0, false, {
                fileName: "[project]/apps/web/src/components/chatbot/ChatWidget.tsx",
                lineNumber: 123,
                columnNumber: 7
            }, this),
            !isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `fixed ${positionClasses[position]} z-[9999]`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleToggleChat,
                        className: `w-14 h-14 bg-gradient-to-r ${colors.primary} ${colors.hover} text-white rounded-full shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-300 flex items-center justify-center group relative`,
                        title: "Open Tap2Go Assistant",
                        style: {
                            zIndex: 9999
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f40$heroicons$2f$react$2f$24$2f$outline$2f$esm$2f$ChatBubbleLeftRightIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChatBubbleLeftRightIcon$3e$__["ChatBubbleLeftRightIcon"], {
                                className: "w-6 h-6 group-hover:scale-110 transition-transform duration-200"
                            }, void 0, false, {
                                fileName: "[project]/apps/web/src/components/chatbot/ChatWidget.tsx",
                                lineNumber: 144,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center border-2 border-white",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "w-2 h-2 bg-white rounded-full animate-pulse"
                                }, void 0, false, {
                                    fileName: "[project]/apps/web/src/components/chatbot/ChatWidget.tsx",
                                    lineNumber: 148,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/apps/web/src/components/chatbot/ChatWidget.tsx",
                                lineNumber: 147,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/apps/web/src/components/chatbot/ChatWidget.tsx",
                        lineNumber: 138,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-0 rounded-full bg-gradient-to-r from-orange-500 to-orange-600 opacity-30 animate-ping"
                    }, void 0, false, {
                        fileName: "[project]/apps/web/src/components/chatbot/ChatWidget.tsx",
                        lineNumber: 153,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/apps/web/src/components/chatbot/ChatWidget.tsx",
                lineNumber: 134,
                columnNumber: 9
            }, this),
            "object" !== 'undefined' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                onKeyDown: (e)=>{
                    if (e.ctrlKey && e.key === '/') {
                        e.preventDefault();
                        handleToggleChat();
                    }
                },
                tabIndex: -1,
                className: "sr-only"
            }, void 0, false, {
                fileName: "[project]/apps/web/src/components/chatbot/ChatWidget.tsx",
                lineNumber: 159,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$apps$2f$web$2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("style", {
                dangerouslySetInnerHTML: {
                    __html: `
        @keyframes chatBounce {
          0%, 20%, 53%, 80%, 100% {
            transform: translate3d(0,0,0);
          }
          40%, 43% {
            transform: translate3d(0, -8px, 0);
          }
          70% {
            transform: translate3d(0, -4px, 0);
          }
          90% {
            transform: translate3d(0, -2px, 0);
          }
        }

        .chat-bounce {
          animation: chatBounce 2s ease-in-out infinite;
        }

        /* Custom scrollbar for chat messages */
        .chat-messages::-webkit-scrollbar {
          width: 4px;
        }

        .chat-messages::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 2px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 2px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
          background: #a1a1a1;
        }

        /* Smooth transitions for chat elements */
        .chat-transition {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Focus styles for accessibility */
        .chat-widget button:focus {
          outline: 2px solid #f97316;
          outline-offset: 2px;
        }

        /* Mobile responsiveness - Full screen like Messenger */
        @media (max-width: 767px) {
          .chat-widget {
            width: 100vw !important;
            height: 100vh !important; /* Fallback for older browsers */
            height: 100dvh !important; /* Use dynamic viewport height for mobile */
            max-width: none !important;
            max-height: 100vh !important; /* Fallback */
            max-height: 100dvh !important; /* Dynamic viewport height */
            border-radius: 0 !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
            overflow: hidden !important;
          }
        }

        /* Messenger-like smooth scrolling */
        .chat-messages {
          scroll-behavior: smooth;
        }

        /* Enhanced message bubble animations */
        .message-bubble {
          animation: messageSlideIn 0.3s ease-out;
        }

        @keyframes messageSlideIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        /* Input focus effects */
        .chat-input-container:focus-within {
          box-shadow: 0 0 0 2px rgba(243, 168, 35, 0.2);
        }
      `
                }
            }, void 0, false, {
                fileName: "[project]/apps/web/src/components/chatbot/ChatWidget.tsx",
                lineNumber: 172,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_s(ChatWidget, "RwqGkvgJH6/5V/bTes3uAH+P22U=");
_c = ChatWidget;
var _c;
__turbopack_context__.k.register(_c, "ChatWidget");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=apps_web_src_7769c225._.js.map