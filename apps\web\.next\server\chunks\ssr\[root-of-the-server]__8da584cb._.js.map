{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_59dee874-module__9CtR0q__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_59dee874.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22]}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/contexts/AuthContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/contexts/AuthContext.tsx <module evaluation>\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/contexts/AuthContext.tsx <module evaluation>\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,uEACA;AAEG,MAAM,UAAU,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,uEACA", "debugId": null}}, {"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/contexts/AuthContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AuthProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/contexts/AuthContext.tsx\",\n    \"AuthProvider\",\n);\nexport const useAuth = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/contexts/AuthContext.tsx\",\n    \"useAuth\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,mDACA;AAEG,MAAM,UAAU,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,mDACA", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/contexts/CartContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CartProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/contexts/CartContext.tsx <module evaluation>\",\n    \"CartProvider\",\n);\nexport const useCart = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/contexts/CartContext.tsx <module evaluation>\",\n    \"useCart\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,uEACA;AAEG,MAAM,UAAU,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,uEACA", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/contexts/CartContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const CartProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call CartProvider() from the server but CartProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/contexts/CartContext.tsx\",\n    \"CartProvider\",\n);\nexport const useCart = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCart() from the server but useCart is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/contexts/CartContext.tsx\",\n    \"useCart\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,mDACA;AAEG,MAAM,UAAU,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,mDACA", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/loading/LoadingProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LoadingWrapper = registerClientReference(\n    function() { throw new Error(\"Attempted to call LoadingWrapper() from the server but LoadingWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/loading/LoadingProvider.tsx <module evaluation>\",\n    \"LoadingWrapper\",\n);\nexport const QuickLoadingDot = registerClientReference(\n    function() { throw new Error(\"Attempted to call QuickLoadingDot() from the server but QuickLoadingDot is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/loading/LoadingProvider.tsx <module evaluation>\",\n    \"QuickLoadingDot\",\n);\nexport const QuickProgressBar = registerClientReference(\n    function() { throw new Error(\"Attempted to call QuickProgressB<PERSON>() from the server but QuickProgressBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/loading/LoadingProvider.tsx <module evaluation>\",\n    \"QuickProgressBar\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/apps/web/src/components/loading/LoadingProvider.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/loading/LoadingProvider.tsx <module evaluation>\",\n    \"default\",\n);\nexport const useLoading = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLoading() from the server but useLoading is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/loading/LoadingProvider.tsx <module evaluation>\",\n    \"useLoading\",\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,qFACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,qFACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qFACA;uCAEW,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuT,GACpV,qFACA;AAEG,MAAM,aAAa,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,qFACA", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/loading/LoadingProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const LoadingWrapper = registerClientReference(\n    function() { throw new Error(\"Attempted to call LoadingWrapper() from the server but LoadingWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/loading/LoadingProvider.tsx\",\n    \"LoadingWrapper\",\n);\nexport const QuickLoadingDot = registerClientReference(\n    function() { throw new Error(\"Attempted to call QuickLoadingDot() from the server but QuickLoadingDot is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/loading/LoadingProvider.tsx\",\n    \"QuickLoadingDot\",\n);\nexport const QuickProgressBar = registerClientReference(\n    function() { throw new Error(\"Attempted to call QuickProgressBar() from the server but QuickProgressBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/loading/LoadingProvider.tsx\",\n    \"QuickProgressBar\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/apps/web/src/components/loading/LoadingProvider.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/loading/LoadingProvider.tsx\",\n    \"default\",\n);\nexport const useLoading = registerClientReference(\n    function() { throw new Error(\"Attempted to call useLoading() from the server but useLoading is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/loading/LoadingProvider.tsx\",\n    \"useLoading\",\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,iEACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,iEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iEACA;uCAEW,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA;AAEG,MAAM,aAAa,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,iEACA", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/ReduxProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/apps/web/src/store/ReduxProvider.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/store/ReduxProvider.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/store/ReduxProvider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/apps/web/src/store/ReduxProvider.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/store/ReduxProvider.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/chatbot/ChatWidget.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/apps/web/src/components/chatbot/ChatWidget.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/chatbot/ChatWidget.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkT,GAC/U,gFACA", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/components/chatbot/ChatWidget.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/apps/web/src/components/chatbot/ChatWidget.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/src/components/chatbot/ChatWidget.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,oQAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\r\nimport { Inter } from \"next/font/google\";\r\nimport \"./globals.css\";\r\nimport { AuthProvider } from \"@/contexts/AuthContext\";\r\nimport { CartProvider } from \"@/contexts/CartContext\";\r\nimport LoadingProvider from \"@/components/loading/LoadingProvider\";\r\nimport ReduxProvider from \"@/store/ReduxProvider\";\r\nimport ChatWidget from \"@/components/chatbot/ChatWidget\";\r\n\r\nconst inter = Inter({ subsets: [\"latin\"] });\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"Tap2Go - Food Delivery\",\r\n  description: \"Professional multivendor food delivery platform\",\r\n  other: {\r\n    \"theme-color\": \"#f3a823\",\r\n    \"msapplication-navbutton-color\": \"#f3a823\",\r\n    \"apple-mobile-web-app-status-bar-style\": \"default\",\r\n  },\r\n};\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <html lang=\"en\">\r\n      <body className={`${inter.className} antialiased`}>\r\n        <ReduxProvider>\r\n          <AuthProvider>\r\n            <LoadingProvider variant=\"facebook\" showInitialLoad={true}>\r\n              <CartProvider>\r\n                {children}\r\n                {/* Global Chatbot Widget */}\r\n                <ChatWidget\r\n                  showWelcomeMessage={true}\r\n                  autoOpen={false}\r\n                  theme=\"orange\"\r\n                  position=\"bottom-left\"\r\n                />\r\n              </CartProvider>\r\n            </LoadingProvider>\r\n          </AuthProvider>\r\n        </ReduxProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;AACA;AACA;AACA;;;;;;;;;AAIO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,OAAO;QACL,eAAe;QACf,iCAAiC;QACjC,yCAAyC;IAC3C;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,6PAAC;QAAK,MAAK;kBACT,cAAA,6PAAC;YAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,SAAS,CAAC,YAAY,CAAC;sBAC/C,cAAA,6PAAC,6IAAA,CAAA,UAAa;0BACZ,cAAA,6PAAC,8IAAA,CAAA,eAAY;8BACX,cAAA,6PAAC,+JAAA,CAAA,UAAe;wBAAC,SAAQ;wBAAW,iBAAiB;kCACnD,cAAA,6PAAC,8IAAA,CAAA,eAAY;;gCACV;8CAED,6PAAC,0JAAA,CAAA,UAAU;oCACT,oBAAoB;oCACpB,UAAU;oCACV,OAAM;oCACN,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3B", "debugId": null}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/tap2go/apps/web/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,qIAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}